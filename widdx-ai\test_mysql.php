<?php
// Simple database connection test
$dbHost = 'localhost';
$dbUser = 'widdx';
$dbPass = 'widdx';
$dbName = 'widdx_ai';

echo "<h2>Testing Database Connection</h2>";

// Test 1: Check if MySQLi extension is loaded
if (!extension_loaded('mysqli')) {
    die("MySQLi extension is not loaded. Please enable it in your php.ini file.");
}
echo "MySQLi extension is loaded.<br>";

// Test 2: Try to connect to MySQL server
$conn = @new mysqli($dbHost, $dbUser, $dbPass);
if ($conn->connect_error) {
    die("<span style='color:red'>Failed to connect to MySQL: " . $conn->connect_error . "</span>");
}
echo "Successfully connected to MySQL server.<br>";

// Test 3: Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbName'");
if ($result->num_rows > 0) {
    echo "Database '$dbName' exists.<br>";
    
    // Select the database
    if (!$conn->select_db($dbName)) {
        die("<span style='color:red'>Failed to select database: " . $conn->error . "</span>");
    }
    
    // List tables
    $tables = $conn->query("SHOW TABLES");
    if ($tables->num_rows > 0) {
        echo "<br>Tables in database '$dbName':<br>";
        while ($row = $tables->fetch_row()) {
            echo "- " . $row[0] . "<br>";
        }
    } else {
        echo "<br>No tables found in database '$dbName'.<br>";
    }
    
} else {
    echo "<span style='color:red'>Database '$dbName' does not exist.</span><br>";
}

$conn->close();
?>
