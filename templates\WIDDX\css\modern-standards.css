/*!
 * WIDDX Modern Design Standards CSS
 * Advanced CSS features and modern design patterns
 * Copyright (c) 2025 WIDDX Development Team
 */

/* ===== MODERN CSS FEATURES ===== */

/* CSS Logical Properties */
.widdx-logical {
  margin-block-start: 1rem;
  margin-block-end: 1rem;
  margin-inline-start: 0.5rem;
  margin-inline-end: 0.5rem;
  padding-block: 1rem;
  padding-inline: 1.5rem;
  border-block-start: 1px solid var(--widdx-primary);
  border-inline-start: 2px solid var(--widdx-accent);
}

/* Container Queries (Progressive Enhancement) */
@supports (container-type: inline-size) {
  .widdx-container {
    container-type: inline-size;
    container-name: widdx-main;
  }

  @container widdx-main (min-width: 400px) {
    .widdx-responsive-text {
      font-size: 1.125rem;
      line-height: 1.7;
    }
  }

  @container widdx-main (min-width: 600px) {
    .widdx-responsive-text {
      font-size: 1.25rem;
      line-height: 1.8;
    }
  }
}

/* CSS Cascade Layers */
@layer base, components, utilities;

@layer base {
  /* Base styles with lowest specificity */
  .widdx-base {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
}

@layer components {
  /* Component styles */
  .widdx-card {
    background: var(--widdx-bg-primary, #ffffff);
    border-radius: var(--widdx-border-radius);
    box-shadow: var(--widdx-shadow);
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .widdx-card:hover {
    box-shadow: var(--widdx-shadow-lg);
    transform: translateY(-2px);
  }
}

@layer utilities {
  /* Utility classes with highest specificity */
  .widdx-sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }
}

/* ===== ADVANCED LAYOUT SYSTEMS ===== */

/* CSS Subgrid (Progressive Enhancement) */
@supports (grid-template-rows: subgrid) {
  .widdx-subgrid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .widdx-subgrid-item {
    display: grid;
    grid-template-rows: subgrid;
    grid-row: span 3;
  }
}

/* Advanced Grid Patterns */
.widdx-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: masonry; /* Future CSS feature */
  gap: 1.5rem;
}

/* Fallback for browsers without masonry support */
@supports not (grid-auto-rows: masonry) {
  .widdx-masonry {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
  }

  .widdx-masonry > * {
    flex: 1 1 300px;
  }
}

/* ===== MODERN TYPOGRAPHY ===== */

/* Fluid Typography */
.widdx-fluid-text {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
  line-height: clamp(1.4, 1.5vw + 1rem, 1.8);
}

.widdx-fluid-heading {
  font-size: clamp(1.5rem, 4vw, 3rem);
  line-height: clamp(1.2, 1vw + 1rem, 1.4);
}

/* Advanced Typography Features */
.widdx-typography {
  font-feature-settings: 
    "kern" 1,           /* Kerning */
    "liga" 1,           /* Ligatures */
    "calt" 1,           /* Contextual alternates */
    "ss01" 1;           /* Stylistic set 1 */
  font-variant-numeric: oldstyle-nums proportional-nums;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== MODERN COLOR SYSTEMS ===== */

/* CSS Color Functions */
.widdx-modern-colors {
  /* Using modern color functions */
  background: oklch(0.7 0.15 180); /* OKLCH color space */
  border-color: color-mix(in srgb, var(--widdx-primary) 80%, transparent);
  box-shadow: 0 4px 12px color-mix(in srgb, var(--widdx-primary) 20%, transparent);
}

/* Relative Color Syntax (Future CSS) */
@supports (color: color-mix(in srgb, red, blue)) {
  .widdx-relative-colors {
    --primary-light: color-mix(in srgb, var(--widdx-primary) 80%, white);
    --primary-dark: color-mix(in srgb, var(--widdx-primary) 80%, black);
    --primary-alpha: color-mix(in srgb, var(--widdx-primary) 50%, transparent);
  }
}

/* ===== ADVANCED ANIMATIONS ===== */

/* CSS Motion Path */
@supports (offset-path: path('M 0 0 L 100 100')) {
  .widdx-motion-path {
    offset-path: path('M 0 0 Q 50 -50 100 0');
    animation: widdx-follow-path 3s ease-in-out infinite;
  }

  @keyframes widdx-follow-path {
    to {
      offset-distance: 100%;
    }
  }
}

/* View Transitions API (Progressive Enhancement) */
@supports (view-transition-name: auto) {
  .widdx-view-transition {
    view-transition-name: widdx-main-content;
  }

  ::view-transition-old(widdx-main-content),
  ::view-transition-new(widdx-main-content) {
    animation-duration: 0.5s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Scroll-driven Animations */
@supports (animation-timeline: scroll()) {
  .widdx-scroll-animation {
    animation: widdx-fade-in-scale linear;
    animation-timeline: scroll();
    animation-range: entry 0% entry 100%;
  }

  @keyframes widdx-fade-in-scale {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

/* ===== MODERN INTERACTION PATTERNS ===== */

/* Focus-visible for better accessibility */
.widdx-interactive:focus-visible {
  outline: 2px solid var(--widdx-primary);
  outline-offset: 2px;
  border-radius: var(--widdx-border-radius);
}

/* Remove focus outline for mouse users */
.widdx-interactive:focus:not(:focus-visible) {
  outline: none;
}

/* Hover and active states with modern timing */
.widdx-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.widdx-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.widdx-button:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

/* ===== MODERN RESPONSIVE PATTERNS ===== */

/* Intrinsic Web Design */
.widdx-intrinsic {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: clamp(1rem, 3vw, 2rem);
}

/* Container-based Responsive Design */
.widdx-responsive-container {
  container-type: inline-size;
  width: 100%;
}

@container (min-width: 400px) {
  .widdx-responsive-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1.5rem;
  }
}

@container (min-width: 600px) {
  .widdx-responsive-grid {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
  }
}

/* ===== MODERN ACCESSIBILITY FEATURES ===== */

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .widdx-motion-safe {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .widdx-high-contrast {
    border: 2px solid;
    background: ButtonFace;
    color: ButtonText;
  }

  .widdx-high-contrast:hover {
    background: Highlight;
    color: HighlightText;
  }
}

/* Forced colors mode support */
@media (forced-colors: active) {
  .widdx-forced-colors {
    border: 1px solid;
    background: transparent;
  }
}

/* ===== MODERN LOADING STATES ===== */

/* CSS-only skeleton loading */
.widdx-skeleton {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: widdx-skeleton-loading 1.5s infinite;
}

@keyframes widdx-skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Content visibility for performance */
.widdx-content-visibility {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* ===== MODERN FORM PATTERNS ===== */

/* Enhanced form controls */
.widdx-form-control {
  appearance: none;
  background: var(--widdx-bg-primary, #ffffff);
  border: 1px solid color-mix(in srgb, var(--widdx-primary) 30%, transparent);
  border-radius: var(--widdx-border-radius);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.widdx-form-control:focus {
  border-color: var(--widdx-primary);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--widdx-primary) 20%, transparent);
  outline: none;
}

/* Custom checkbox with modern styling */
.widdx-checkbox {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--widdx-primary);
  border-radius: 0.25rem;
  position: relative;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.widdx-checkbox:checked {
  background: var(--widdx-primary);
  border-color: var(--widdx-primary);
}

.widdx-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

/* ===== MODERN UTILITY CLASSES ===== */

/* Logical property utilities */
.widdx-mbs-1 { margin-block-start: 0.25rem; }
.widdx-mbs-2 { margin-block-start: 0.5rem; }
.widdx-mbs-3 { margin-block-start: 1rem; }
.widdx-mbs-4 { margin-block-start: 1.5rem; }

.widdx-mis-1 { margin-inline-start: 0.25rem; }
.widdx-mis-2 { margin-inline-start: 0.5rem; }
.widdx-mis-3 { margin-inline-start: 1rem; }
.widdx-mis-4 { margin-inline-start: 1.5rem; }

/* Modern spacing utilities */
.widdx-space-y-1 > * + * { margin-block-start: 0.25rem; }
.widdx-space-y-2 > * + * { margin-block-start: 0.5rem; }
.widdx-space-y-3 > * + * { margin-block-start: 1rem; }
.widdx-space-y-4 > * + * { margin-block-start: 1.5rem; }

/* Aspect ratio utilities */
.widdx-aspect-square { aspect-ratio: 1 / 1; }
.widdx-aspect-video { aspect-ratio: 16 / 9; }
.widdx-aspect-photo { aspect-ratio: 4 / 3; }

/* ===== PRINT STYLES ===== */

@media print {
  .widdx-print-hidden {
    display: none !important;
  }

  .widdx-print-visible {
    display: block !important;
  }

  .widdx-card {
    box-shadow: none;
    border: 1px solid #000;
  }

  .widdx-button {
    background: transparent !important;
    color: #000 !important;
    border: 1px solid #000 !important;
  }
}

/* ===== BROWSER-SPECIFIC OPTIMIZATIONS ===== */

/* Safari-specific optimizations */
@supports (-webkit-touch-callout: none) {
  .widdx-safari-fix {
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
  }
}

/* Firefox-specific optimizations */
@-moz-document url-prefix() {
  .widdx-firefox-fix {
    scrollbar-width: thin;
    scrollbar-color: var(--widdx-primary) transparent;
  }
}

/* ===== FUTURE CSS FEATURES (PROGRESSIVE ENHANCEMENT) ===== */

/* CSS Nesting (when supported) */
@supports (selector(&)) {
  .widdx-nested {
    color: var(--widdx-text-primary);
    
    & .widdx-nested__title {
      font-size: 1.25rem;
      font-weight: 600;
    }
    
    & .widdx-nested__content {
      margin-block-start: 1rem;
      
      & p {
        margin-block-end: 0.75rem;
      }
    }
    
    &:hover {
      background: var(--widdx-bg-secondary);
    }
  }
}
