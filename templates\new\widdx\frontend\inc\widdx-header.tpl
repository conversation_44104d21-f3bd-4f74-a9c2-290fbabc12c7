<!-- Modern WIDDX Header -->
<header id="header" class="widdx-header modern-header" role="banner">
    <!-- Top Bar for Additional Info -->
    <div class="widdx-topbar d-none d-lg-block">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="widdx-contact-info">
                        <span class="contact-item">
                            <i class="fas fa-envelope" aria-hidden="true"></i>
                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        </span>
                        <span class="contact-item ml-3">
                            <i class="fas fa-phone" aria-hidden="true"></i>
                            <a href="tel:+************" class="text-decoration-none">+970 123 456 789</a>
                        </span>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="widdx-header-actions">
                        {if $languagechangeenabled && count($locales) > 1 || $currencies}
                            <button type="button" class="btn btn-sm btn-outline-light widdx-lang-btn"
                                    data-toggle="modal" data-target="#modalChooseLanguage"
                                    aria-label="{lang key='chooselanguage'}">
                                <div class="d-inline-block align-middle mr-1">
                                    <div class="iti-flag {if $activeLocale.countryCode === 'GB'}us{else}{$activeLocale.countryCode|lower}{/if}"></div>
                                </div>
                                <span class="d-none d-md-inline">{$activeLocale.localisedName}</span>
                                <span class="d-none d-lg-inline">/ {$activeCurrency.prefix}{$activeCurrency.code}</span>
                            </button>
                        {/if}
                        <div class="widdx-social-links ml-3">
                            {include file="$template/includes/social-accounts.tpl"}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="widdx-main-nav navbar navbar-expand-xl" role="navigation" aria-label="Main navigation">
        <div class="container">
            <!-- Brand Logo -->
            <a class="navbar-brand widdx-brand" href="{$WEB_ROOT}/index.php" aria-label="Home">
                {if $assetLogoPath}
                    <img src="{$assetLogoPath}" alt="{$companyname}" class="widdx-logo-img">
                {else}
                    <span class="widdx-brand-text">{$companyname}</span>
                {/if}
            </a>

            <!-- Mobile Menu Toggle -->
            <button class="navbar-toggler widdx-mobile-toggle d-xl-none" type="button"
                    data-toggle="collapse" data-target="#widdxMainNav"
                    aria-controls="widdxMainNav" aria-expanded="false"
                    aria-label="Toggle navigation">
                <span class="widdx-hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="widdxMainNav">
                <div class="d-flex w-100 justify-content-between align-items-center">
                    <!-- Primary Navigation -->
                    <ul class="navbar-nav widdx-primary-nav">
                        {include file="$template/frontend/inc/widdx-navbar.tpl" navbar=$primaryNavbar}
                    </ul>

                    <!-- Search Widget -->
                    <div class="widdx-search-widget mx-3">
                        {include file="$template/frontend/widgets/widdx-search-icon.tpl"}
                    </div>

                    <!-- Right Side Actions -->
                    <div class="widdx-header-right d-flex align-items-center">
                        <!-- Cart Button -->
                        <a class="widdx-cart-btn btn btn-outline-primary position-relative mr-3"
                           href="{$WEB_ROOT}/cart.php?a=view"
                           aria-label="{lang key='carttitle'}">
                            <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                            {if $cartitemcount > 0}
                                <span class="widdx-cart-count badge badge-danger position-absolute">{$cartitemcount}</span>
                            {/if}
                            <span class="d-none d-lg-inline ml-2">{lang key="carttitle"}</span>
                        </a>

                        <!-- Theme Toggle -->
                        <div class="widdx-theme-toggle mr-3">
                            {include file="$template/frontend/inc/widdx-header-theme-switcher.tpl"}
                        </div>

                        <!-- User Menu -->
                        <div class="widdx-user-menu">
                            {include file="$template/frontend/inc/widdx-navbar.tpl" navbar=$secondaryNavbar rightDrop=true}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Language/Currency (shown only on mobile) -->
    <div class="widdx-mobile-lang d-lg-none">
        <div class="container">
            {if $languagechangeenabled && count($locales) > 1 || $currencies}
                <button type="button" class="btn btn-sm btn-outline-secondary w-100"
                        data-toggle="modal" data-target="#modalChooseLanguage">
                    <div class="d-inline-block align-middle mr-2">
                        <div class="iti-flag {if $activeLocale.countryCode === 'GB'}us{else}{$activeLocale.countryCode|lower}{/if}"></div>
                    </div>
                    {$activeLocale.localisedName} / {$activeCurrency.prefix}{$activeCurrency.code}
                </button>
            {/if}
        </div>
    </div>
</header>


<!-- Modern Mobile Overlay Menu -->
<div class="widdx-mobile-overlay" id="widdxMobileOverlay">
    <div class="widdx-mobile-menu">
        <div class="widdx-mobile-header">
            <div class="widdx-mobile-logo">
                {if $assetLogoPath}
                    <img src="{$assetLogoPath}" alt="{$companyname}" class="widdx-mobile-logo-img">
                {else}
                    <span class="widdx-mobile-brand-text">{$companyname}</span>
                {/if}
            </div>
            <button class="widdx-mobile-close" aria-label="Close menu">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
        </div>

        <div class="widdx-mobile-content">
            <!-- Mobile Search -->
            <div class="widdx-mobile-search mb-4">
                <form method="post" action="{routePath('knowledgebase-search')}" class="position-relative">
                    <input class="form-control widdx-search-input" type="text" name="search"
                           placeholder="{lang key='searchOurKnowledgebase'}..."
                           aria-label="Search">
                    <button class="widdx-search-btn" type="submit" aria-label="Search">
                        <i class="fas fa-search" aria-hidden="true"></i>
                    </button>
                </form>
            </div>

            <!-- Mobile Navigation -->
            <nav class="widdx-mobile-nav" role="navigation" aria-label="Mobile navigation">
                {include file="$template/frontend/inc/widdx-navbar.tpl" navbar=$primaryNavbar mobile=true}

                <!-- Mobile User Actions -->
                <div class="widdx-mobile-actions mt-4">
                    {if !$loggedin}
                        <a href="{$WEB_ROOT}/login.php" class="btn btn-primary btn-block mb-3">
                            <i class="fas fa-sign-in-alt mr-2" aria-hidden="true"></i>
                            {lang key="login"}
                        </a>
                        <a href="{$WEB_ROOT}/register.php" class="btn btn-outline-primary btn-block mb-3">
                            <i class="fas fa-user-plus mr-2" aria-hidden="true"></i>
                            {lang key="register"}
                        </a>
                    {else}
                        {include file="$template/frontend/inc/widdx-navbar.tpl" navbar=$secondaryNavbar mobile=true}
                    {/if}
                </div>
            </nav>
        </div>

        <div class="widdx-mobile-footer">
            <!-- Mobile Social Links -->
            <div class="widdx-mobile-social mb-3">
                {include file="$template/includes/social-accounts.tpl"}
            </div>

            <!-- Mobile Contact Info -->
            <div class="widdx-mobile-contact">
                <div class="contact-item mb-2">
                    <i class="fas fa-envelope mr-2" aria-hidden="true"></i>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone mr-2" aria-hidden="true"></i>
                    <a href="tel:+************">+970 123 456 789</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Bar for Page Loading -->
<div class="widdx-progress-bar" id="widdxProgressBar">
    <div class="widdx-progress-fill"></div>
</div>