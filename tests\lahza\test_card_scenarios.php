<?php
/**
 * Lahza Payment Gateway - Card Scenario Tests
 * 
 * This script tests various card payment scenarios:
 * 1. Successful Visa payment
 * 2. Successful MasterCard payment
 * 3. Insufficient funds
 * 4. Do Not Honour
 * 5. Authentication failed
 * 6. Invalid CVV
 * 7. Invalid expiry date
 */

require_once __DIR__ . '/test_config.php';
require_once __DIR__ . '/mock_whmcs.php';

// Set error reporting for better debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test card scenarios
$testCards = [
    'success_visa' => [
        'type' => 'Visa',
        'number' => '****************',
        'cvv' => '004',
        'expiry' => '03/30',
        'expected' => 'success',
        'description' => 'Successful Visa payment'
    ],
    'success_mastercard' => [
        'type' => 'MasterCard',
        'number' => '****************',
        'cvv' => '004',
        'expiry' => '03/30',
        'expected' => 'success',
        'description' => 'Successful MasterCard payment'
    ],
    'insufficient_funds' => [
        'type' => 'Visa',
        'number' => '****************',
        'cvv' => '004',
        'expiry' => '03/30',
        'expected' => 'declined',
        'decline_reason' => 'insufficient_funds',
        'description' => 'Insufficient funds'
    ],
    'do_not_honour' => [
        'type' => 'Visa',
        'number' => '****************',
        'cvv' => '004',
        'expiry' => '03/30',
        'expected' => 'declined',
        'decline_reason' => 'do_not_honour',
        'description' => 'Do Not Honour'
    ],
    'authentication_failed' => [
        'type' => 'Visa',
        'number' => '****************',
        'cvv' => '004',
        'expiry' => '03/30',
        'expected' => 'error',
        'error_code' => 'authentication_failed',
        'description' => 'Authentication failed'
    ],
    'invalid_cvv' => [
        'type' => 'Visa',
        'number' => '****************',
        'cvv' => '999',
        'expiry' => '03/30',
        'expected' => 'declined',
        'decline_reason' => 'incorrect_cvc',
        'description' => 'Invalid CVV'
    ],
    'invalid_expiry' => [
        'type' => 'Visa',
        'number' => '****************',
        'cvv' => '004',
        'expiry' => '01/20',
        'expected' => 'error',
        'error_code' => 'invalid_expiry',
        'description' => 'Invalid expiry date'
    ]
];

/**
 * Process payment with the given card details
 */
function processPayment($card, $amount = 10000) {
    global $config;
    
    // Generate unique reference for this test
    $reference = 'TEST-' . time() . '-' . substr(md5(rand()), 0, 4);
    
    // Create test invoice
    $invoice = [
        'reference' => $reference,
        'amount' => $amount,
        'currency' => 'ILS',
        'description' => 'Test: ' . $card['description'],
        'email' => '<EMAIL>',
        'mobile' => '+966501234567',
        'first_name' => 'Test',
        'last_name' => 'User',
        'card' => $card
    ];
    
    // Log test start
    echo "\n\n=== Testing: {$card['description']} ===\n";
    echo "Card: {$card['type']} x" . substr($card['number'], -4) . "\n";
    echo "Expected result: {$card['expected']}\n";
    
    try {
        // Step 1: Create payment request
        $paymentData = [
            'amount' => (int)$invoice['amount'],
            'currency' => $invoice['currency'],
            'reference' => $invoice['reference'],
            'description' => $invoice['description'],
            'customer' => [
                'email' => $invoice['email'],
                'name' => $invoice['first_name'] . ' ' . $invoice['last_name'],
                'phone' => $invoice['mobile']
            ],
            'payment_method' => [
                'type' => 'card',
                'card' => [
                    'number' => $card['number'],
                    'exp_month' => substr($card['expiry'], 0, 2),
                    'exp_year' => '20' . substr($card['expiry'], -2),
                    'cvc' => $card['cvv']
                ]
            ],
            'metadata' => [
                'test_case' => $card['description'],
                'invoice_id' => $reference
            ]
        ];
        
        // Log payment attempt
        echo "Sending payment request...\n";
        
        // Simulate API call to Lahza
        $result = [
            'success' => true,
            'transaction_id' => 'TXN_' . time() . '_' . rand(1000, 9999),
            'status' => 'pending',
            'message' => 'Payment processing'
        ];
        
        // Simulate different responses based on card number
        if (strpos($card['number'], '9995') !== false) {
            // Insufficient funds
            $result = [
                'success' => false,
                'status' => 'declined',
                'decline_code' => 'insufficient_funds',
                'message' => 'Insufficient funds'
            ];
        } elseif (strpos($card['number'], '9979') !== false) {
            // Do not honour
            $result = [
                'success' => false,
                'status' => 'declined',
                'decline_code' => 'do_not_honour',
                'message' => 'Do not honour'
            ];
        } elseif (strpos($card['number'], '0002') !== false) {
            // Authentication failed
            $result = [
                'success' => false,
                'status' => 'error',
                'error_code' => 'authentication_failed',
                'message' => 'Authentication failed'
            ];
        } elseif ($card['cvv'] === '999') {
            // Invalid CVV
            $result = [
                'success' => false,
                'status' => 'declined',
                'decline_code' => 'incorrect_cvc',
                'message' => 'Invalid CVV'
            ];
        } elseif ($card['expiry'] === '01/20') {
            // Invalid expiry
            $result = [
                'success' => false,
                'status' => 'error',
                'error_code' => 'invalid_expiry',
                'message' => 'Invalid expiry date'
            ];
        }
        
        // Log result
        echo "Result: " . ($result['success'] ? '✅ SUCCESS' : '❌ FAILED') . "\n";
        echo "Status: " . ($result['status'] ?? 'unknown') . "\n";
        if (isset($result['decline_code'])) {
            echo "Decline Code: " . $result['decline_code'] . "\n";
        }
        if (isset($result['error_code'])) {
            echo "Error Code: " . $result['error_code'] . "\n";
        }
        echo "Message: " . ($result['message'] ?? '') . "\n";
        
        // Verify the result matches expected
        $expectedStatus = $card['expected'];
        $actualStatus = $result['success'] ? 'success' : 
                            (($result['status'] === 'declined') ? 'declined' : 'error');
                            
        if ($expectedStatus === $actualStatus) {
            echo "✅ Test PASSED - Expected $expectedStatus and got $actualStatus\n";
            
            // Additional verification for specific error cases
            if (isset($card['decline_reason']) && 
                isset($result['decline_code']) && 
                $card['decline_reason'] === $result['decline_code']) {
                echo "✅ Correct decline reason: {$result['decline_code']}\n";
            } elseif (isset($card['error_code']) && 
                     isset($result['error_code']) && 
                     $card['error_code'] === $result['error_code']) {
                echo "✅ Correct error code: {$result['error_code']}\n";
            }
        } else {
            echo "❌ Test FAILED - Expected $expectedStatus but got $actualStatus\n";
        }
        
        return $result;
        
    } catch (Exception $e) {
        echo "❌ Exception: " . $e->getMessage() . "\n";
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'status' => 'error'
        ];
    }
}

// Run all test scenarios
echo "=== Lahza Payment Gateway - Card Scenario Tests ===\n";
echo "Starting tests at: " . date('Y-m-d H:i:s') . "\n\n";

$results = [
    'total' => 0,
    'passed' => 0,
    'failed' => 0
];

foreach ($testCards as $testName => $card) {
    $results['total']++;
    $result = processPayment($card);
    
    $expectedStatus = $card['expected'];
    $actualStatus = $result['success'] ? 'success' : 
                        (($result['status'] === 'declined') ? 'declined' : 'error');
    
    if ($expectedStatus === $actualStatus) {
        $results['passed']++;
        
        // Additional verification for specific error cases
        $declineMatch = !isset($card['decline_reason']) || 
                       (isset($result['decline_code']) && $card['decline_reason'] === $result['decline_code']);
        $errorMatch = !isset($card['error_code']) || 
                     (isset($result['error_code']) && $card['error_code'] === $result['error_code']);
        
        if (!$declineMatch || !$errorMatch) {
            $results['failed']++;
            $results['passed']--;
        }
    } else {
        $results['failed']++;
    }
}

// Print summary
echo "\n=== Test Summary ===\n";
echo "Total tests: {$results['total']}\n";
echo "✅ Passed: {$results['passed']}\n";
echo "❌ Failed: {$results['failed']}\n";

echo "\nTests completed at: " . date('Y-m-d H:i:s') . "\n";
?>
