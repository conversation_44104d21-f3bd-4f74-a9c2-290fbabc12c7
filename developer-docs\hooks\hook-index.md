+++
title = "Hook Index"
toc = true
weight = 100

+++

<div class="row"><div class="col-sm-6">
<h3>Invoices and Quotes</h3>

<ul><li> <a href="/hooks-reference/invoices-and-quotes/#acceptquote">AcceptQuote</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#addinvoicelatefee">AddInvoiceLateFee</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#addinvoicepayment">AddInvoicePayment</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#addtransaction">AddTransaction</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#afterinvoicinggenerateinvoiceitems">AfterInvoicingGenerateInvoiceItems</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#cancelandrefundorder">CancelAndRefundOrder</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicecancelled">InvoiceCancelled</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicechangegateway">InvoiceChangeGateway</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicecreated">InvoiceCreated</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicecreation">InvoiceCreation</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicecreationpreemail">InvoiceCreationPreEmail</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicepaid">InvoicePaid</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicepaidpreemail">InvoicePaidPreEmail</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicepaymentreminder">InvoicePaymentReminder</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicerefunded">InvoiceRefunded</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoicesplit">InvoiceSplit</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#invoiceunpaid">InvoiceUnpaid</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#logtransaction">LogTransaction</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#manualrefund">ManualRefund</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#preinvoiceautomaticcancellation">PreInvoiceAutomaticCancellation</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#preinvoicinggenerateinvoiceitems">PreInvoicingGenerateInvoiceItems</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#quotecreated">QuoteCreated</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#quotestatuschange">QuoteStatusChange</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#updateinvoicetotal">UpdateInvoiceTotal</a>
<li> <a href="/hooks-reference/invoices-and-quotes/#viewinvoicedetailspage">ViewInvoiceDetailsPage</a>
</ul>
<h3>Shopping Cart</h3>

<ul><li> <a href="/hooks-reference/shopping-cart/#acceptorder">AcceptOrder</a>
<li> <a href="/hooks-reference/shopping-cart/#addonfraud">AddonFraud</a>
<li> <a href="/hooks-reference/shopping-cart/#aftercalculatecarttotals">AfterCalculateCartTotals</a>
<li> <a href="/hooks-reference/shopping-cart/#afterfraudcheck">AfterFraudCheck</a>
<li> <a href="/hooks-reference/shopping-cart/#aftershoppingcartcheckout">AfterShoppingCartCheckout</a>
<li> <a href="/hooks-reference/shopping-cart/#cancelorder">CancelOrder</a>
<li> <a href="/hooks-reference/shopping-cart/#cartitemstax">CartItemsTax</a>
<li> <a href="/hooks-reference/shopping-cart/#cartsubdomainvalidation">CartSubdomainValidation</a>
<li> <a href="/hooks-reference/shopping-cart/#carttotaladjustment">CartTotalAdjustment</a>
<li> <a href="/hooks-reference/shopping-cart/#deleteorder">DeleteOrder</a>
<li> <a href="/hooks-reference/shopping-cart/#fraudcheckawaitinguserinput">FraudCheckAwaitingUserInput</a>
<li> <a href="/hooks-reference/shopping-cart/#fraudcheckfailed">FraudCheckFailed</a>
<li> <a href="/hooks-reference/shopping-cart/#fraudcheckpassed">FraudCheckPassed</a>
<li> <a href="/hooks-reference/shopping-cart/#fraudorder">FraudOrder</a>
<li> <a href="/hooks-reference/shopping-cart/#orderaddonpricingoverride">OrderAddonPricingOverride</a>
<li> <a href="/hooks-reference/shopping-cart/#orderdomainpricingoverride">OrderDomainPricingOverride</a>
<li> <a href="/hooks-reference/shopping-cart/#orderpaid">OrderPaid</a>
<li> <a href="/hooks-reference/shopping-cart/#orderproductpricingoverride">OrderProductPricingOverride</a>
<li> <a href="/hooks-reference/shopping-cart/#orderproductupgradeoverride">OrderProductUpgradeOverride</a>
<li> <a href="/hooks-reference/shopping-cart/#overrideordernumbergeneration">OverrideOrderNumberGeneration</a>
<li> <a href="/hooks-reference/shopping-cart/#pendingorder">PendingOrder</a>
<li> <a href="/hooks-reference/shopping-cart/#precalculatecarttotals">PreCalculateCartTotals</a>
<li> <a href="/hooks-reference/shopping-cart/#prefraudcheck">PreFraudCheck</a>
<li> <a href="/hooks-reference/shopping-cart/#preshoppingcartcheckout">PreShoppingCartCheckout</a>
<li> <a href="/hooks-reference/shopping-cart/#runfraudcheck">RunFraudCheck</a>
<li> <a href="/hooks-reference/shopping-cart/#shoppingcartcheckoutcompletepage">ShoppingCartCheckoutCompletePage</a>
<li> <a href="/hooks-reference/shopping-cart/#shoppingcartvalidatecheckout">ShoppingCartValidateCheckout</a>
<li> <a href="/hooks-reference/shopping-cart/#shoppingcartvalidatedomain">ShoppingCartValidateDomain</a>
<li> <a href="/hooks-reference/shopping-cart/#shoppingcartvalidatedomainsconfig">ShoppingCartValidateDomainsConfig</a>
<li> <a href="/hooks-reference/shopping-cart/#shoppingcartvalidateproductupdate">ShoppingCartValidateProductUpdate</a>
</ul>
<h3>Service</h3>

<ul><li> <a href="/hooks-reference/service/#cancellationrequest">CancellationRequest</a>
<li> <a href="/hooks-reference/service/#preserviceedit">PreServiceEdit</a>
<li> <a href="/hooks-reference/service/#servicedelete">ServiceDelete</a>
<li> <a href="/hooks-reference/service/#serviceedit">ServiceEdit</a>
<li> <a href="/hooks-reference/service/#servicerecurringcompleted">ServiceRecurringCompleted</a>
</ul>
<h3>Module</h3>

<ul><li> <a href="/hooks-reference/module/#aftermodulechangepackage">AfterModuleChangePackage</a>
<li> <a href="/hooks-reference/module/#aftermodulechangepackagefailed">AfterModuleChangePackageFailed</a>
<li> <a href="/hooks-reference/module/#aftermodulechangepassword">AfterModuleChangePassword</a>
<li> <a href="/hooks-reference/module/#aftermodulechangepasswordfailed">AfterModuleChangePasswordFailed</a>
<li> <a href="/hooks-reference/module/#aftermodulecreate">AfterModuleCreate</a>
<li> <a href="/hooks-reference/module/#aftermodulecreatefailed">AfterModuleCreateFailed</a>
<li> <a href="/hooks-reference/module/#aftermodulecustom">AfterModuleCustom</a>
<li> <a href="/hooks-reference/module/#aftermodulecustomfailed">AfterModuleCustomFailed</a>
<li> <a href="/hooks-reference/module/#aftermoduledeprovisionaddonfeature">AfterModuleDeprovisionAddOnFeature</a>
<li> <a href="/hooks-reference/module/#aftermoduledeprovisionaddonfeaturefailed">AfterModuleDeprovisionAddOnFeatureFailed</a>
<li> <a href="/hooks-reference/module/#aftermoduleprovisionaddonfeature">AfterModuleProvisionAddOnFeature</a>
<li> <a href="/hooks-reference/module/#aftermoduleprovisionaddonfeaturefailed">AfterModuleProvisionAddOnFeatureFailed</a>
<li> <a href="/hooks-reference/module/#aftermodulesuspend">AfterModuleSuspend</a>
<li> <a href="/hooks-reference/module/#aftermodulesuspendaddonfeature">AfterModuleSuspendAddOnFeature</a>
<li> <a href="/hooks-reference/module/#aftermodulesuspendaddonfeaturefailed">AfterModuleSuspendAddOnFeatureFailed</a>
<li> <a href="/hooks-reference/module/#aftermodulesuspendfailed">AfterModuleSuspendFailed</a>
<li> <a href="/hooks-reference/module/#aftermoduleterminate">AfterModuleTerminate</a>
<li> <a href="/hooks-reference/module/#aftermoduleterminatefailed">AfterModuleTerminateFailed</a>
<li> <a href="/hooks-reference/module/#aftermoduleunsuspend">AfterModuleUnsuspend</a>
<li> <a href="/hooks-reference/module/#aftermoduleunsuspendaddonfeature">AfterModuleUnsuspendAddOnFeature</a>
<li> <a href="/hooks-reference/module/#aftermoduleunsuspendaddonfeaturefailed">AfterModuleUnsuspendAddOnFeatureFailed</a>
<li> <a href="/hooks-reference/module/#aftermoduleunsuspendfailed">AfterModuleUnsuspendFailed</a>
<li> <a href="/hooks-reference/module/#overridemoduleusernamegeneration">OverrideModuleUsernameGeneration</a>
<li> <a href="/hooks-reference/module/#premodulechangepackage">PreModuleChangePackage</a>
<li> <a href="/hooks-reference/module/#premodulechangepassword">PreModuleChangePassword</a>
<li> <a href="/hooks-reference/module/#premodulecreate">PreModuleCreate</a>
<li> <a href="/hooks-reference/module/#premodulecustom">PreModuleCustom</a>
<li> <a href="/hooks-reference/module/#premoduledeprovisionaddonfeature">PreModuleDeprovisionAddOnFeature</a>
<li> <a href="/hooks-reference/module/#premoduleprovisionaddonfeature">PreModuleProvisionAddOnFeature</a>
<li> <a href="/hooks-reference/module/#premodulerenew">PreModuleRenew</a>
<li> <a href="/hooks-reference/module/#premodulesuspend">PreModuleSuspend</a>
<li> <a href="/hooks-reference/module/#premodulesuspendaddonfeature">PreModuleSuspendAddOnFeature</a>
<li> <a href="/hooks-reference/module/#premoduleterminate">PreModuleTerminate</a>
<li> <a href="/hooks-reference/module/#premoduleunsuspend">PreModuleUnsuspend</a>
<li> <a href="/hooks-reference/module/#premoduleunsuspendaddonfeature">PreModuleUnsuspendAddOnFeature</a>
</ul>
<h3>Domain</h3>

<ul><li> <a href="/hooks-reference/domain/#domaindelete">DomainDelete</a>
<li> <a href="/hooks-reference/domain/#domainedit">DomainEdit</a>
<li> <a href="/hooks-reference/domain/#domaintransfercompleted">DomainTransferCompleted</a>
<li> <a href="/hooks-reference/domain/#domaintransferfailed">DomainTransferFailed</a>
<li> <a href="/hooks-reference/domain/#domainvalidation">DomainValidation</a>
<li> <a href="/hooks-reference/domain/#predomainregister">PreDomainRegister</a>
<li> <a href="/hooks-reference/domain/#predomaintransfer">PreDomainTransfer</a>
<li> <a href="/hooks-reference/domain/#preregistrarregisterdomain">PreRegistrarRegisterDomain</a>
<li> <a href="/hooks-reference/domain/#preregistrarrenewdomain">PreRegistrarRenewDomain</a>
<li> <a href="/hooks-reference/domain/#preregistrartransferdomain">PreRegistrarTransferDomain</a>
<li> <a href="/hooks-reference/domain/#topleveldomainadd">TopLevelDomainAdd</a>
<li> <a href="/hooks-reference/domain/#topleveldomaindelete">TopLevelDomainDelete</a>
<li> <a href="/hooks-reference/domain/#topleveldomainpricingupdate">TopLevelDomainPricingUpdate</a>
<li> <a href="/hooks-reference/domain/#topleveldomainupdate">TopLevelDomainUpdate</a>
</ul>
<h3>Registrar Module</h3>

<ul><li> <a href="/hooks-reference/registrar-module/#afterregistrargetcontactdetails">AfterRegistrarGetContactDetails</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrargetdns">AfterRegistrarGetDNS</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrargeteppcode">AfterRegistrarGetEPPCode</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrargetnameservers">AfterRegistrarGetNameservers</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarregister">AfterRegistrarRegister</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarregistration">AfterRegistrarRegistration</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarregistrationfailed">AfterRegistrarRegistrationFailed</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarrenew">AfterRegistrarRenew</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarrenewal">AfterRegistrarRenewal</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarrenewalfailed">AfterRegistrarRenewalFailed</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarrequestdelete">AfterRegistrarRequestDelete</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarsavecontactdetails">AfterRegistrarSaveContactDetails</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarsavedns">AfterRegistrarSaveDNS</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrarsavenameservers">AfterRegistrarSaveNameservers</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrartransfer">AfterRegistrarTransfer</a>
<li> <a href="/hooks-reference/registrar-module/#afterregistrartransferfailed">AfterRegistrarTransferFailed</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrargetcontactdetails">PreRegistrarGetContactDetails</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrargetdns">PreRegistrarGetDNS</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrargeteppcode">PreRegistrarGetEPPCode</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrargetnameservers">PreRegistrarGetNameservers</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrarrequestdelete">PreRegistrarRequestDelete</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrarsavecontactdetails">PreRegistrarSaveContactDetails</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrarsavedns">PreRegistrarSaveDNS</a>
<li> <a href="/hooks-reference/registrar-module/#preregistrarsavenameservers">PreRegistrarSaveNameservers</a>
</ul>
<h3>Addon</h3>

<ul><li> <a href="/hooks-reference/addon/#addonactivated">AddonActivated</a>
<li> <a href="/hooks-reference/addon/#addonactivation">AddonActivation</a>
<li> <a href="/hooks-reference/addon/#addonadd">AddonAdd</a>
<li> <a href="/hooks-reference/addon/#addoncancelled">AddonCancelled</a>
<li> <a href="/hooks-reference/addon/#addonconfig">AddonConfig</a>
<li> <a href="/hooks-reference/addon/#addonconfigsave">AddonConfigSave</a>
<li> <a href="/hooks-reference/addon/#addondeleted">AddonDeleted</a>
<li> <a href="/hooks-reference/addon/#addonedit">AddonEdit</a>
<li> <a href="/hooks-reference/addon/#addonrenewal">AddonRenewal</a>
<li> <a href="/hooks-reference/addon/#addonsuspended">AddonSuspended</a>
<li> <a href="/hooks-reference/addon/#addonterminated">AddonTerminated</a>
<li> <a href="/hooks-reference/addon/#addonunsuspended">AddonUnsuspended</a>
<li> <a href="/hooks-reference/addon/#afteraddonupgrade">AfterAddonUpgrade</a>
<li> <a href="/hooks-reference/addon/#licensingaddonreissue">LicensingAddonReissue</a>
<li> <a href="/hooks-reference/addon/#licensingaddonverify">LicensingAddonVerify</a>
<li> <a href="/hooks-reference/addon/#productaddondelete">ProductAddonDelete</a>
</ul>
<h3>Client</h3>

<ul><li> <a href="/hooks-reference/client/#afterclientmerge">AfterClientMerge</a>
<li> <a href="/hooks-reference/client/#clientadd">ClientAdd</a>
<li> <a href="/hooks-reference/client/#clientalert">ClientAlert</a>
<li> <a href="/hooks-reference/client/#clientchangepassword">ClientChangePassword</a>
<li> <a href="/hooks-reference/client/#clientclose">ClientClose</a>
<li> <a href="/hooks-reference/client/#clientdelete">ClientDelete</a>
<li> <a href="/hooks-reference/client/#clientdetailsvalidation">ClientDetailsValidation</a>
<li> <a href="/hooks-reference/client/#clientedit">ClientEdit</a>
<li> <a href="/hooks-reference/client/#predeleteclient">PreDeleteClient</a>
</ul>
<h3>User</h3>

<ul><li> <a href="/hooks-reference/user/#useradd">UserAdd</a>
<li> <a href="/hooks-reference/user/#userchangepassword">UserChangePassword</a>
<li> <a href="/hooks-reference/user/#useredit">UserEdit</a>
<li> <a href="/hooks-reference/user/#useremailverificationcomplete">UserEmailVerificationComplete</a>
</ul>
<h3>Contact</h3>

<ul><li> <a href="/hooks-reference/contact/#contactadd">ContactAdd</a>
<li> <a href="/hooks-reference/contact/#contactdelete">ContactDelete</a>
<li> <a href="/hooks-reference/contact/#contactdetailsvalidation">ContactDetailsValidation</a>
<li> <a href="/hooks-reference/contact/#contactedit">ContactEdit</a>
</ul>
</div><div class="col-sm-6"><h3>Products and Services</h3>

<ul><li> <a href="/hooks-reference/products-and-services/#afterproductupgrade">AfterProductUpgrade</a>
<li> <a href="/hooks-reference/products-and-services/#productdelete">ProductDelete</a>
<li> <a href="/hooks-reference/products-and-services/#productedit">ProductEdit</a>
<li> <a href="/hooks-reference/products-and-services/#serveradd">ServerAdd</a>
<li> <a href="/hooks-reference/products-and-services/#serverdelete">ServerDelete</a>
<li> <a href="/hooks-reference/products-and-services/#serveredit">ServerEdit</a>
</ul>
<h3>Ticket</h3>

<ul><li> <a href="/hooks-reference/ticket/#adminareaviewticketpage">AdminAreaViewTicketPage</a>
<li> <a href="/hooks-reference/ticket/#adminareaviewticketpagesidebar">AdminAreaViewTicketPageSidebar</a>
<li> <a href="/hooks-reference/ticket/#adminsupportticketpagepretickets">AdminSupportTicketPagePreTickets</a>
<li> <a href="/hooks-reference/ticket/#clientareapagesubmitticket">ClientAreaPageSubmitTicket</a>
<li> <a href="/hooks-reference/ticket/#clientareapagesupporttickets">ClientAreaPageSupportTickets</a>
<li> <a href="/hooks-reference/ticket/#clientareapageviewticket">ClientAreaPageViewTicket</a>
<li> <a href="/hooks-reference/ticket/#submitticketanswersuggestions">SubmitTicketAnswerSuggestions</a>
<li> <a href="/hooks-reference/ticket/#ticketaddnote">TicketAddNote</a>
<li> <a href="/hooks-reference/ticket/#ticketadminreply">TicketAdminReply</a>
<li> <a href="/hooks-reference/ticket/#ticketclose">TicketClose</a>
<li> <a href="/hooks-reference/ticket/#ticketdelete">TicketDelete</a>
<li> <a href="/hooks-reference/ticket/#ticketdeletereply">TicketDeleteReply</a>
<li> <a href="/hooks-reference/ticket/#ticketdepartmentchange">TicketDepartmentChange</a>
<li> <a href="/hooks-reference/ticket/#ticketflagged">TicketFlagged</a>
<li> <a href="/hooks-reference/ticket/#ticketmerge">TicketMerge</a>
<li> <a href="/hooks-reference/ticket/#ticketopen">TicketOpen</a>
<li> <a href="/hooks-reference/ticket/#ticketopenadmin">TicketOpenAdmin</a>
<li> <a href="/hooks-reference/ticket/#ticketopenvalidation">TicketOpenValidation</a>
<li> <a href="/hooks-reference/ticket/#ticketpiping">TicketPiping</a>
<li> <a href="/hooks-reference/ticket/#ticketprioritychange">TicketPriorityChange</a>
<li> <a href="/hooks-reference/ticket/#ticketsplit">TicketSplit</a>
<li> <a href="/hooks-reference/ticket/#ticketstatuschange">TicketStatusChange</a>
<li> <a href="/hooks-reference/ticket/#ticketsubjectchange">TicketSubjectChange</a>
<li> <a href="/hooks-reference/ticket/#ticketuserreply">TicketUserReply</a>
<li> <a href="/hooks-reference/ticket/#transliteratetickettext">TransliterateTicketText</a>
</ul>
<h3>Support Tools</h3>

<ul><li> <a href="/hooks-reference/support-tools/#announcementadd">AnnouncementAdd</a>
<li> <a href="/hooks-reference/support-tools/#announcementedit">AnnouncementEdit</a>
<li> <a href="/hooks-reference/support-tools/#filedownload">FileDownload</a>
<li> <a href="/hooks-reference/support-tools/#networkissueadd">NetworkIssueAdd</a>
<li> <a href="/hooks-reference/support-tools/#networkissueclose">NetworkIssueClose</a>
<li> <a href="/hooks-reference/support-tools/#networkissuedelete">NetworkIssueDelete</a>
<li> <a href="/hooks-reference/support-tools/#networkissueedit">NetworkIssueEdit</a>
<li> <a href="/hooks-reference/support-tools/#networkissuereopen">NetworkIssueReopen</a>
</ul>
<h3>Authentication</h3>

<ul><li> <a href="/hooks-reference/authentication/#clientloginshare">ClientLoginShare</a>
<li> <a href="/hooks-reference/authentication/#userlogin">UserLogin</a>
<li> <a href="/hooks-reference/authentication/#userlogout">UserLogout</a>
</ul>
<h3>Client Area Interface</h3>

<ul><li> <a href="/hooks-reference/client-area-interface/#clientareadomaindetails">ClientAreaDomainDetails</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareahomepage">ClientAreaHomepage</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareahomepagepanels">ClientAreaHomepagePanels</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareanavbars">ClientAreaNavbars</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapage">ClientAreaPage</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageaddcontact">ClientAreaPageAddContact</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageaddfunds">ClientAreaPageAddFunds</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageaddonmodule">ClientAreaPageAddonModule</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageaffiliates">ClientAreaPageAffiliates</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageannouncements">ClientAreaPageAnnouncements</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagebanned">ClientAreaPageBanned</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagebulkdomainmanagement">ClientAreaPageBulkDomainManagement</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagecancellation">ClientAreaPageCancellation</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagecart">ClientAreaPageCart</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagechangepassword">ClientAreaPageChangePassword</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageconfiguressl">ClientAreaPageConfigureSSL</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagecontact">ClientAreaPageContact</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagecontacts">ClientAreaPageContacts</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagecreditcard">ClientAreaPageCreditCard</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagecreditcardcheckout">ClientAreaPageCreditCardCheckout</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomainaddons">ClientAreaPageDomainAddons</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomaincontacts">ClientAreaPageDomainContacts</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomaindnsmanagement">ClientAreaPageDomainDNSManagement</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomaindetails">ClientAreaPageDomainDetails</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomaineppcode">ClientAreaPageDomainEPPCode</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomainemailforwarding">ClientAreaPageDomainEmailForwarding</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomainregisternameservers">ClientAreaPageDomainRegisterNameservers</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedomains">ClientAreaPageDomains</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagedownloads">ClientAreaPageDownloads</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageemails">ClientAreaPageEmails</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagehome">ClientAreaPageHome</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageinvoices">ClientAreaPageInvoices</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageknowledgebase">ClientAreaPageKnowledgebase</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagelogin">ClientAreaPageLogin</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagelogout">ClientAreaPageLogout</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagemasspay">ClientAreaPageMassPay</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagenetworkissues">ClientAreaPageNetworkIssues</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagepasswordreset">ClientAreaPagePasswordReset</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageproductdetails">ClientAreaPageProductDetails</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageproductsservices">ClientAreaPageProductsServices</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageprofile">ClientAreaPageProfile</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagequotes">ClientAreaPageQuotes</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageregister">ClientAreaPageRegister</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapagesecurity">ClientAreaPageSecurity</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageserverstatus">ClientAreaPageServerStatus</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageunsubscribe">ClientAreaPageUnsubscribe</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageupgrade">ClientAreaPageUpgrade</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageviewemail">ClientAreaPageViewEmail</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageviewinvoice">ClientAreaPageViewInvoice</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapageviewquote">ClientAreaPageViewQuote</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareapaymentmethods">ClientAreaPaymentMethods</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareaprimarynavbar">ClientAreaPrimaryNavbar</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareaprimarysidebar">ClientAreaPrimarySidebar</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareaproductdetails">ClientAreaProductDetails</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareaproductdetailspremoduletemplate">ClientAreaProductDetailsPreModuleTemplate</a>
<li> <a href="/hooks-reference/client-area-interface/#clientarearegister">ClientAreaRegister</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareasecondarynavbar">ClientAreaSecondaryNavbar</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareasecondarysidebar">ClientAreaSecondarySidebar</a>
<li> <a href="/hooks-reference/client-area-interface/#clientareasidebars">ClientAreaSidebars</a>
</ul>
<h3>Admin Area</h3>

<ul><li> <a href="/hooks-reference/admin-area/#adminareaclientsummaryactionlinks">AdminAreaClientSummaryActionLinks</a>
<li> <a href="/hooks-reference/admin-area/#adminareaclientsummarypage">AdminAreaClientSummaryPage</a>
<li> <a href="/hooks-reference/admin-area/#adminareapage">AdminAreaPage</a>
<li> <a href="/hooks-reference/admin-area/#adminareaviewquotepage">AdminAreaViewQuotePage</a>
<li> <a href="/hooks-reference/admin-area/#adminclientdomainstabfields">AdminClientDomainsTabFields</a>
<li> <a href="/hooks-reference/admin-area/#adminclientdomainstabfieldssave">AdminClientDomainsTabFieldsSave</a>
<li> <a href="/hooks-reference/admin-area/#adminclientfileupload">AdminClientFileUpload</a>
<li> <a href="/hooks-reference/admin-area/#adminclientprofiletabfields">AdminClientProfileTabFields</a>
<li> <a href="/hooks-reference/admin-area/#adminclientprofiletabfieldssave">AdminClientProfileTabFieldsSave</a>
<li> <a href="/hooks-reference/admin-area/#adminclientservicestabfields">AdminClientServicesTabFields</a>
<li> <a href="/hooks-reference/admin-area/#adminclientservicestabfieldssave">AdminClientServicesTabFieldsSave</a>
<li> <a href="/hooks-reference/admin-area/#adminhomepage">AdminHomepage</a>
<li> <a href="/hooks-reference/admin-area/#adminlogin">AdminLogin</a>
<li> <a href="/hooks-reference/admin-area/#adminlogout">AdminLogout</a>
<li> <a href="/hooks-reference/admin-area/#adminpredefinedaddons">AdminPredefinedAddons</a>
<li> <a href="/hooks-reference/admin-area/#adminproductconfigfields">AdminProductConfigFields</a>
<li> <a href="/hooks-reference/admin-area/#adminproductconfigfieldssave">AdminProductConfigFieldsSave</a>
<li> <a href="/hooks-reference/admin-area/#adminserviceedit">AdminServiceEdit</a>
<li> <a href="/hooks-reference/admin-area/#authadmin">AuthAdmin</a>
<li> <a href="/hooks-reference/admin-area/#authadminapi">AuthAdminApi</a>
<li> <a href="/hooks-reference/admin-area/#invoicecreationadminarea">InvoiceCreationAdminArea</a>
<li> <a href="/hooks-reference/admin-area/#preadminserviceedit">PreAdminServiceEdit</a>
<li> <a href="/hooks-reference/admin-area/#vieworderdetailspage">ViewOrderDetailsPage</a>
</ul>
<h3>Output</h3>

<ul><li> <a href="/hooks-reference/output/#adminareafooteroutput">AdminAreaFooterOutput</a>
<li> <a href="/hooks-reference/output/#adminareaheadoutput">AdminAreaHeadOutput</a>
<li> <a href="/hooks-reference/output/#adminareaheaderoutput">AdminAreaHeaderOutput</a>
<li> <a href="/hooks-reference/output/#admininvoicescontrolsoutput">AdminInvoicesControlsOutput</a>
<li> <a href="/hooks-reference/output/#clientareadomaindetailsoutput">ClientAreaDomainDetailsOutput</a>
<li> <a href="/hooks-reference/output/#clientareafooteroutput">ClientAreaFooterOutput</a>
<li> <a href="/hooks-reference/output/#clientareaheadoutput">ClientAreaHeadOutput</a>
<li> <a href="/hooks-reference/output/#clientareaheaderoutput">ClientAreaHeaderOutput</a>
<li> <a href="/hooks-reference/output/#clientareaproductdetailsoutput">ClientAreaProductDetailsOutput</a>
<li> <a href="/hooks-reference/output/#formatdateforclientareaoutput">FormatDateForClientAreaOutput</a>
<li> <a href="/hooks-reference/output/#formatdatetimeforclientareaoutput">FormatDateTimeForClientAreaOutput</a>
<li> <a href="/hooks-reference/output/#reportviewpostoutput">ReportViewPostOutput</a>
<li> <a href="/hooks-reference/output/#reportviewpreoutput">ReportViewPreOutput</a>
<li> <a href="/hooks-reference/output/#shoppingcartcheckoutoutput">ShoppingCartCheckoutOutput</a>
<li> <a href="/hooks-reference/output/#shoppingcartconfigureproductaddonsoutput">ShoppingCartConfigureProductAddonsOutput</a>
<li> <a href="/hooks-reference/output/#shoppingcartviewcartoutput">ShoppingCartViewCartOutput</a>
</ul>
<h3>Cron</h3>

<ul><li> <a href="/hooks-reference/cron/#aftercronjob">AfterCronJob</a>
<li> <a href="/hooks-reference/cron/#dailycronjob">DailyCronJob</a>
<li> <a href="/hooks-reference/cron/#dailycronjobpreemail">DailyCronJobPreEmail</a>
<li> <a href="/hooks-reference/cron/#popemailcollectioncroncompleted">PopEmailCollectionCronCompleted</a>
<li> <a href="/hooks-reference/cron/#postautomationtask">PostAutomationTask</a>
<li> <a href="/hooks-reference/cron/#preautomationtask">PreAutomationTask</a>
<li> <a href="/hooks-reference/cron/#precronjob">PreCronJob</a>
</ul>
<h3>Everything Else</h3>

<ul><li> <a href="/hooks-reference/everything-else/#affiliateactivation">AffiliateActivation</a>
<li> <a href="/hooks-reference/everything-else/#affiliateclickthru">AffiliateClickthru</a>
<li> <a href="/hooks-reference/everything-else/#affiliatecommission">AffiliateCommission</a>
<li> <a href="/hooks-reference/everything-else/#affiliatewithdrawalrequest">AffiliateWithdrawalRequest</a>
<li> <a href="/hooks-reference/everything-else/#afterconfigoptionsupgrade">AfterConfigOptionsUpgrade</a>
<li> <a href="/hooks-reference/everything-else/#ccupdate">CCUpdate</a>
<li> <a href="/hooks-reference/everything-else/#calcaffiliatecommission">CalcAffiliateCommission</a>
<li> <a href="/hooks-reference/everything-else/#customfieldload">CustomFieldLoad</a>
<li> <a href="/hooks-reference/everything-else/#customfieldsave">CustomFieldSave</a>
<li> <a href="/hooks-reference/everything-else/#emailprelog">EmailPreLog</a>
<li> <a href="/hooks-reference/everything-else/#emailpresend">EmailPreSend</a>
<li> <a href="/hooks-reference/everything-else/#emailtplmergefields">EmailTplMergeFields</a>
<li> <a href="/hooks-reference/everything-else/#fetchcurrencyexchangerates">FetchCurrencyExchangeRates</a>
<li> <a href="/hooks-reference/everything-else/#intelligentsearch">IntelligentSearch</a>
<li> <a href="/hooks-reference/everything-else/#linktracker">LinkTracker</a>
<li> <a href="/hooks-reference/everything-else/#logactivity">LogActivity</a>
<li> <a href="/hooks-reference/everything-else/#notificationpresend">NotificationPreSend</a>
<li> <a href="/hooks-reference/everything-else/#paymethodmigration">PayMethodMigration</a>
<li> <a href="/hooks-reference/everything-else/#preemailsendreducerecipients">PreEmailSendReduceRecipients</a>
<li> <a href="/hooks-reference/everything-else/#preupgradecheckout">PreUpgradeCheckout</a>
<li> <a href="/hooks-reference/everything-else/#premiumpriceoverride">PremiumPriceOverride</a>
<li> <a href="/hooks-reference/everything-else/#premiumpricerecalculationoverride">PremiumPriceRecalculationOverride</a>
<li> <a href="/hooks-reference/everything-else/#vatnumberverification">VatNumberVerification</a>
</ul>
</div></div>