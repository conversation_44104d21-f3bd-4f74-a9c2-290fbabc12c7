<?php
/**
 * Lahza Payment Gateway - Enhanced Logging System
 * 
 * @package    WHMCS
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @version    1.0.0
 */

// Prevent direct access
if (!defined("WHMCS")) {
    die("Direct access prohibited");
}

/**
 * Enhanced logging class for Lahza payment gateway
 */
class LahzaLogger {
    
    private $logDir;
    private $maxFileSize;
    private $maxFiles;
    private $logLevels;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->logDir = __DIR__ . '/../logs';
        $this->maxFileSize = 5 * 1024 * 1024; // 5MB
        $this->maxFiles = 10;
        $this->logLevels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'];
        
        $this->ensureLogDirectory();
    }
    
    /**
     * Ensure log directory exists and is secure
     */
    private function ensureLogDirectory() {
        if (!file_exists($this->logDir)) {
            if (!@mkdir($this->logDir, 0755, true)) {
                throw new Exception('Failed to create log directory: ' . $this->logDir);
            }
        }
        
        // Create .htaccess to protect log files
        $htaccessFile = $this->logDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            $htaccessContent = "Order deny,allow\nDeny from all\n";
            @file_put_contents($htaccessFile, $htaccessContent);
        }
        
        // Create index.php to prevent directory listing
        $indexFile = $this->logDir . '/index.php';
        if (!file_exists($indexFile)) {
            @file_put_contents($indexFile, '<?php die("Access denied"); ?>');
        }
    }
    
    /**
     * Log a message with specified level
     * 
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category (default: 'general')
     */
    public function log($level, $message, $context = [], $category = 'general') {
        $level = strtoupper($level);
        
        if (!in_array($level, $this->logLevels)) {
            $level = 'INFO';
        }
        
        $logFile = $this->logDir . '/lahza_' . $category . '.log';
        
        // Rotate log if necessary
        $this->rotateLogIfNeeded($logFile);
        
        // Sanitize sensitive data
        $context = $this->sanitizeContext($context);
        
        // Create log entry
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'request_id' => $this->getRequestId(),
            'ip' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . PHP_EOL;
        
        // Atomic write with file locking
        $result = @file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        if ($result === false) {
            // Fallback to PHP error log
            error_log("Lahza Gateway - Failed to write to log file: {$logFile}");
            error_log("Lahza Gateway - {$level}: {$message}");
        }
    }
    
    /**
     * Log debug message
     */
    public function debug($message, $context = [], $category = 'general') {
        $this->log('DEBUG', $message, $context, $category);
    }
    
    /**
     * Log info message
     */
    public function info($message, $context = [], $category = 'general') {
        $this->log('INFO', $message, $context, $category);
    }
    
    /**
     * Log warning message
     */
    public function warning($message, $context = [], $category = 'general') {
        $this->log('WARNING', $message, $context, $category);
    }
    
    /**
     * Log error message
     */
    public function error($message, $context = [], $category = 'general') {
        $this->log('ERROR', $message, $context, $category);
    }
    
    /**
     * Log critical message
     */
    public function critical($message, $context = [], $category = 'general') {
        $this->log('CRITICAL', $message, $context, $category);
    }
    
    /**
     * Log payment transaction
     */
    public function logTransaction($action, $data, $status = 'INFO') {
        $this->log($status, "Transaction {$action}", $data, 'transactions');
    }
    
    /**
     * Log security event
     */
    public function logSecurity($event, $data = []) {
        $this->log('WARNING', "Security event: {$event}", $data, 'security');
    }
    
    /**
     * Log API call
     */
    public function logApiCall($endpoint, $method, $responseCode, $duration, $data = []) {
        $context = array_merge($data, [
            'endpoint' => $endpoint,
            'method' => $method,
            'response_code' => $responseCode,
            'duration_ms' => $duration
        ]);
        
        $this->log('INFO', "API call to {$endpoint}", $context, 'api');
    }
    
    /**
     * Rotate log file if it exceeds maximum size
     */
    private function rotateLogIfNeeded($logFile) {
        if (!file_exists($logFile) || filesize($logFile) < $this->maxFileSize) {
            return;
        }
        
        // Rotate existing files
        for ($i = $this->maxFiles - 1; $i >= 1; $i--) {
            $oldFile = $logFile . '.' . $i;
            $newFile = $logFile . '.' . ($i + 1);
            
            if (file_exists($oldFile)) {
                if ($i === $this->maxFiles - 1) {
                    @unlink($oldFile); // Delete oldest file
                } else {
                    @rename($oldFile, $newFile);
                }
            }
        }
        
        // Move current log to .1
        @rename($logFile, $logFile . '.1');
    }
    
    /**
     * Sanitize context data to remove sensitive information
     */
    private function sanitizeContext($context) {
        $sensitiveKeys = [
            'secretKey', 'secret_key', 'api_key', 'apiKey',
            'password', 'pass', 'token', 'auth',
            'credit_card', 'creditcard', 'cc_number',
            'cvv', 'cvc', 'pin'
        ];
        
        foreach ($context as $key => $value) {
            $lowerKey = strtolower($key);
            
            // Check if key contains sensitive information
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (strpos($lowerKey, $sensitiveKey) !== false) {
                    if (is_string($value) && strlen($value) > 8) {
                        $context[$key] = substr($value, 0, 4) . '***' . substr($value, -4);
                    } else {
                        $context[$key] = '***REDACTED***';
                    }
                    break;
                }
            }
            
            // Recursively sanitize arrays
            if (is_array($value)) {
                $context[$key] = $this->sanitizeContext($value);
            }
        }
        
        return $context;
    }
    
    /**
     * Get unique request ID for tracking
     */
    private function getRequestId() {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = uniqid('req_', true);
        }
        
        return $requestId;
    }
    
    /**
     * Get client IP address
     */
    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Get log statistics
     */
    public function getLogStats($category = 'general', $hours = 24) {
        $logFile = $this->logDir . '/lahza_' . $category . '.log';
        
        if (!file_exists($logFile)) {
            return ['total' => 0, 'by_level' => []];
        }
        
        $stats = ['total' => 0, 'by_level' => []];
        $cutoffTime = time() - ($hours * 3600);
        
        $handle = fopen($logFile, 'r');
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                $entry = json_decode($line, true);
                if ($entry && isset($entry['timestamp'], $entry['level'])) {
                    $timestamp = strtotime($entry['timestamp']);
                    if ($timestamp >= $cutoffTime) {
                        $stats['total']++;
                        $level = $entry['level'];
                        $stats['by_level'][$level] = ($stats['by_level'][$level] ?? 0) + 1;
                    }
                }
            }
            fclose($handle);
        }
        
        return $stats;
    }
}
