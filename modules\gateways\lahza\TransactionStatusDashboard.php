<?php

/**
 * Transaction Status Dashboard for WIDDX
 * 
 * Provides a comprehensive dashboard for monitoring and managing transaction statuses
 * 
 * @package WIDDX
 * @subpackage PaymentGateway
 * @version 1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

require_once __DIR__ . '/EnhancedTransactionManager.php';
require_once __DIR__ . '/Logger.php';

class TransactionStatusDashboard {
    
    private $transactionManager;
    private $logger;
    
    public function __construct($gatewayParams) {
        $this->transactionManager = new EnhancedTransactionManager($gatewayParams);
        $this->logger = new LahzaLogger();
    }
    
    /**
     * Get dashboard data
     */
    public function getDashboardData() {
        return [
            'summary' => $this->getTransactionSummary(),
            'recent_transactions' => $this->getRecentTransactions(),
            'status_distribution' => $this->getStatusDistribution(),
            'performance_metrics' => $this->getPerformanceMetrics(),
            'alerts' => $this->getActiveAlerts()
        ];
    }
    
    /**
     * Get transaction summary
     */
    private function getTransactionSummary() {
        try {
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $thisMonth = date('Y-m-01');
            $lastMonth = date('Y-m-01', strtotime('-1 month'));
            
            // Today's transactions
            $todayStats = Capsule::table('lahza_transactions')
                ->whereDate('created_at', $today)
                ->selectRaw('
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as completed_amount,
                    COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_count,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_count,
                    COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_count
                ')
                ->first();
            
            // Yesterday's transactions for comparison
            $yesterdayStats = Capsule::table('lahza_transactions')
                ->whereDate('created_at', $yesterday)
                ->selectRaw('
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as completed_amount
                ')
                ->first();
            
            // This month's transactions
            $monthStats = Capsule::table('lahza_transactions')
                ->where('created_at', '>=', $thisMonth)
                ->selectRaw('
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as completed_amount,
                    AVG(CASE WHEN status = "completed" THEN amount END) as avg_amount
                ')
                ->first();
            
            return [
                'today' => [
                    'total_transactions' => $todayStats->total_count ?? 0,
                    'completed_amount' => $todayStats->completed_amount ?? 0,
                    'completed_count' => $todayStats->completed_count ?? 0,
                    'failed_count' => $todayStats->failed_count ?? 0,
                    'pending_count' => $todayStats->pending_count ?? 0,
                    'success_rate' => $todayStats->total_count > 0 ? 
                        round(($todayStats->completed_count / $todayStats->total_count) * 100, 2) : 0
                ],
                'yesterday' => [
                    'total_transactions' => $yesterdayStats->total_count ?? 0,
                    'completed_amount' => $yesterdayStats->completed_amount ?? 0
                ],
                'month' => [
                    'total_transactions' => $monthStats->total_count ?? 0,
                    'completed_amount' => $monthStats->completed_amount ?? 0,
                    'average_amount' => $monthStats->avg_amount ?? 0
                ],
                'growth' => [
                    'transactions' => $this->calculateGrowth(
                        $todayStats->total_count ?? 0,
                        $yesterdayStats->total_count ?? 0
                    ),
                    'revenue' => $this->calculateGrowth(
                        $todayStats->completed_amount ?? 0,
                        $yesterdayStats->completed_amount ?? 0
                    )
                ]
            ];
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get transaction summary', [
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return [];
        }
    }
    
    /**
     * Get recent transactions
     */
    private function getRecentTransactions($limit = 20) {
        try {
            $transactions = Capsule::table('lahza_transactions')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
            
            return $transactions->map(function($transaction) {
                return [
                    'transaction_id' => $transaction->transaction_id,
                    'invoice_id' => $transaction->invoice_id,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'payment_method' => $transaction->payment_method,
                    'created_at' => $transaction->created_at,
                    'updated_at' => $transaction->updated_at,
                    'status_class' => $this->getStatusClass($transaction->status)
                ];
            })->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get recent transactions', [
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return [];
        }
    }
    
    /**
     * Get status distribution
     */
    private function getStatusDistribution() {
        try {
            $distribution = Capsule::table('lahza_transactions')
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->orderBy('count', 'desc')
                ->get();
            
            $total = $distribution->sum('count');
            
            return $distribution->map(function($item) use ($total) {
                return [
                    'status' => $item->status,
                    'count' => $item->count,
                    'percentage' => $total > 0 ? round(($item->count / $total) * 100, 2) : 0,
                    'color' => $this->getStatusColor($item->status)
                ];
            })->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get status distribution', [
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return [];
        }
    }
    
    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics() {
        try {
            $last24Hours = date('Y-m-d H:i:s', strtotime('-24 hours'));
            $last7Days = date('Y-m-d H:i:s', strtotime('-7 days'));
            
            // Average processing time
            $avgProcessingTime = Capsule::table('lahza_transactions')
                ->where('created_at', '>=', $last24Hours)
                ->where('completed_at', '!=', null)
                ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, created_at, completed_at)) as avg_seconds')
                ->first();
            
            // Success rate over last 7 days
            $successRate = Capsule::table('lahza_transactions')
                ->where('created_at', '>=', $last7Days)
                ->selectRaw('
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = "completed" THEN 1 END) as completed
                ')
                ->first();
            
            // Peak transaction times
            $peakHours = Capsule::table('lahza_transactions')
                ->where('created_at', '>=', $last7Days)
                ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
                ->groupBy('hour')
                ->orderBy('count', 'desc')
                ->limit(3)
                ->get();
            
            return [
                'avg_processing_time' => round($avgProcessingTime->avg_seconds ?? 0, 2),
                'success_rate' => $successRate->total > 0 ? 
                    round(($successRate->completed / $successRate->total) * 100, 2) : 0,
                'peak_hours' => $peakHours->pluck('hour')->toArray(),
                'total_volume_7d' => $successRate->total ?? 0
            ];
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get performance metrics', [
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return [];
        }
    }
    
    /**
     * Get active alerts
     */
    private function getActiveAlerts() {
        $alerts = [];
        
        try {
            // Check for high failure rate
            $last1Hour = date('Y-m-d H:i:s', strtotime('-1 hour'));
            $recentStats = Capsule::table('lahza_transactions')
                ->where('created_at', '>=', $last1Hour)
                ->selectRaw('
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed
                ')
                ->first();
            
            if ($recentStats->total > 10 && $recentStats->failed > 0) {
                $failureRate = ($recentStats->failed / $recentStats->total) * 100;
                if ($failureRate > 20) {
                    $alerts[] = [
                        'type' => 'danger',
                        'title' => 'High Failure Rate',
                        'message' => "Failure rate is {$failureRate}% in the last hour",
                        'action' => 'Check payment gateway status'
                    ];
                }
            }
            
            // Check for stuck transactions
            $stuckTransactions = Capsule::table('lahza_transactions')
                ->where('status', 'processing')
                ->where('created_at', '<', date('Y-m-d H:i:s', strtotime('-30 minutes')))
                ->count();
            
            if ($stuckTransactions > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Stuck Transactions',
                    'message' => "{$stuckTransactions} transactions stuck in processing",
                    'action' => 'Review processing queue'
                ];
            }
            
            // Check for expired transactions
            $expiredCount = $this->transactionManager->processExpiredTransactions();
            if ($expiredCount > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Expired Transactions',
                    'message' => "{$expiredCount} transactions marked as expired",
                    'action' => 'Review timeout settings'
                ];
            }
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get active alerts', [
                'error' => $e->getMessage()
            ], 'dashboard');
        }
        
        return $alerts;
    }
    
    /**
     * Get transaction details for modal
     */
    public function getTransactionDetails($transactionId) {
        try {
            $transaction = $this->transactionManager->getTransaction($transactionId);
            if (!$transaction) {
                return null;
            }
            
            $statusHistory = $this->transactionManager->getStatusHistory($transactionId);
            
            return [
                'transaction' => $transaction,
                'status_history' => $statusHistory,
                'metadata' => json_decode($transaction['metadata'] ?? '{}', true)
            ];
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get transaction details', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return null;
        }
    }
    
    /**
     * Search transactions
     */
    public function searchTransactions($filters = []) {
        try {
            $query = Capsule::table('lahza_transactions');
            
            if (!empty($filters['transaction_id'])) {
                $query->where('transaction_id', 'like', '%' . $filters['transaction_id'] . '%');
            }
            
            if (!empty($filters['invoice_id'])) {
                $query->where('invoice_id', 'like', '%' . $filters['invoice_id'] . '%');
            }
            
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (!empty($filters['date_from'])) {
                $query->where('created_at', '>=', $filters['date_from']);
            }
            
            if (!empty($filters['date_to'])) {
                $query->where('created_at', '<=', $filters['date_to']);
            }
            
            if (!empty($filters['amount_min'])) {
                $query->where('amount', '>=', $filters['amount_min']);
            }
            
            if (!empty($filters['amount_max'])) {
                $query->where('amount', '<=', $filters['amount_max']);
            }
            
            $transactions = $query->orderBy('created_at', 'desc')
                ->limit($filters['limit'] ?? 100)
                ->get();
            
            return $transactions->map(function($transaction) {
                return [
                    'transaction_id' => $transaction->transaction_id,
                    'invoice_id' => $transaction->invoice_id,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'payment_method' => $transaction->payment_method,
                    'created_at' => $transaction->created_at,
                    'updated_at' => $transaction->updated_at,
                    'status_class' => $this->getStatusClass($transaction->status)
                ];
            })->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to search transactions', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return [];
        }
    }
    
    /**
     * Export transactions to CSV
     */
    public function exportTransactions($filters = []) {
        try {
            $transactions = $this->searchTransactions(array_merge($filters, ['limit' => 10000]));
            
            $csv = "Transaction ID,Invoice ID,Amount,Currency,Status,Payment Method,Created At,Updated At\n";
            
            foreach ($transactions as $transaction) {
                $csv .= sprintf(
                    "%s,%s,%s,%s,%s,%s,%s,%s\n",
                    $transaction['transaction_id'],
                    $transaction['invoice_id'],
                    $transaction['amount'],
                    $transaction['currency'],
                    $transaction['status'],
                    $transaction['payment_method'] ?? '',
                    $transaction['created_at'],
                    $transaction['updated_at']
                );
            }
            
            return $csv;
            
        } catch (Exception $e) {
            $this->logger->error('Failed to export transactions', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return false;
        }
    }
    
    /**
     * Get status class for styling
     */
    private function getStatusClass($status) {
        $statusClasses = [
            'pending' => 'warning',
            'processing' => 'info',
            'completed' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
            'refunded' => 'warning',
            'expired' => 'dark',
            'disputed' => 'danger'
        ];
        
        return $statusClasses[$status] ?? 'secondary';
    }
    
    /**
     * Get status color for charts
     */
    private function getStatusColor($status) {
        $statusColors = [
            'pending' => '#ffc107',
            'processing' => '#17a2b8',
            'completed' => '#28a745',
            'failed' => '#dc3545',
            'cancelled' => '#6c757d',
            'refunded' => '#fd7e14',
            'expired' => '#343a40',
            'disputed' => '#e83e8c'
        ];
        
        return $statusColors[$status] ?? '#6c757d';
    }
    
    /**
     * Calculate growth percentage
     */
    private function calculateGrowth($current, $previous) {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 2);
    }
    
    /**
     * Get hourly transaction data for charts
     */
    public function getHourlyTransactionData($date = null) {
        try {
            $date = $date ?: date('Y-m-d');
            
            $data = Capsule::table('lahza_transactions')
                ->whereDate('created_at', $date)
                ->selectRaw('
                    HOUR(created_at) as hour,
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = "completed" THEN 1 END) as completed,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed
                ')
                ->groupBy('hour')
                ->orderBy('hour')
                ->get();
            
            // Fill missing hours with zeros
            $hourlyData = [];
            for ($i = 0; $i < 24; $i++) {
                $hourlyData[$i] = [
                    'hour' => $i,
                    'total' => 0,
                    'completed' => 0,
                    'failed' => 0
                ];
            }
            
            foreach ($data as $row) {
                $hourlyData[$row->hour] = [
                    'hour' => $row->hour,
                    'total' => $row->total,
                    'completed' => $row->completed,
                    'failed' => $row->failed
                ];
            }
            
            return array_values($hourlyData);
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get hourly transaction data', [
                'date' => $date,
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return [];
        }
    }
    
    /**
     * Get daily transaction data for charts
     */
    public function getDailyTransactionData($days = 30) {
        try {
            $startDate = date('Y-m-d', strtotime("-{$days} days"));
            
            $data = Capsule::table('lahza_transactions')
                ->where('created_at', '>=', $startDate)
                ->selectRaw('
                    DATE(created_at) as date,
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = "completed" THEN 1 END) as completed,
                    SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as revenue
                ')
                ->groupBy('date')
                ->orderBy('date')
                ->get();
            
            return $data->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get daily transaction data', [
                'days' => $days,
                'error' => $e->getMessage()
            ], 'dashboard');
            
            return [];
        }
    }
}
