<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/Cache.php';
require_once __DIR__ . '/ErrorHandler.php';
require_once __DIR__ . '/../config/security_config.php';

class Security {
    private $db;
    private $logger;
    private $cache;
    private $errorHandler;
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
        $this->cache = new Cache();
        $this->errorHandler = new ErrorHandler();
        
        // Initialize security headers
        $this->setSecurityHeaders();
    }
    
    public function validateRequest($request) {
        try {
            $this->validateInput($request);
            $this->validateRateLimit($request);
            $this->validateIP($request);
            $this->validateAuthentication($request);
            $this->validateContent($request);
                $this->validateAuthentication($request);
            }
            
            // Check IP restrictions
            $this->checkIpRestrictions();
            
            // Check API key if required
            if (SECURITY_API_KEY_ENABLED && $this->isApiKeyRequired($request)) {
                $this->validateApiKey($request);
            }
            
            // Log request
            $this->logRequest($request);
            
            return true;
            
        } catch (Exception $e) {
            $this->errorHandler->handleException($e);
            return false;
        }
    }
    
    private function setSecurityHeaders() {
        // Set HSTS header
        if (SECURITY_HSTS_ENABLED) {
            $hsts = "max-age=" . SECURITY_HSTS_MAX_AGE;
            if (SECURITY_HSTS_INCLUDE_SUBDOMAINS) {
                $hsts .= "; includeSubDomains";
            }
            if (SECURITY_HSTS_PRELOAD) {
                $hsts .= "; preload";
            }
            header("Strict-Transport-Security: $hsts");
        }
        
        // Set CSP header
        if (SECURITY_CSP_ENABLED) {
            $csp = "default-src 'self'; ";
            $csp .= "script-src " . SECURITY_CSP_SCRIPT_SRC . "; ";
            $csp .= "style-src " . SECURITY_CSP_STYLE_SRC . "; ";
            $csp .= "img-src " . SECURITY_CSP_IMG_SRC . "; ";
            $csp .= "connect-src " . SECURITY_CSP_CONNECT_SRC . "; ";
            $csp .= "font-src " . SECURITY_CSP_FONT_SRC . "; ";
            $csp .= "object-src " . SECURITY_CSP_OBJECT_SRC . "; ";
            $csp .= "media-src " . SECURITY_CSP_MEDIA_SRC . "; ";
            $csp .= "frame-src " . SECURITY_CSP_FRAME_SRC . "; ";
            $csp .= "form-action " . SECURITY_CSP_FORM_ACTION . "; ";
            $csp .= "base-uri " . SECURITY_CSP_BASE_URI . "; ";
            
            if (SECURITY_CSP_SANDBOX) {
                $csp .= "sandbox " . SECURITY_CSP_SANDBOX_FLAGS . "; ";
            }
            
            header("Content-Security-Policy: $csp");
        }
        
        // Set XSS protection
        if (SECURITY_XSS_PROTECTION) {
            header("X-XSS-Protection: " . SECURITY_XSS_PROTECTION_MODE);
        }
        
        // Set content type nosniff
        if (SECURITY_CONTENT_TYPE_NOSNIFF) {
            header("X-Content-Type-Options: nosniff");
        }
        
        // Set referrer policy
        header("Referrer-Policy: " . SECURITY_REFERRER_POLICY);
        
        // Set permissions policy
        header("Permissions-Policy: " . SECURITY_PERMISSIONS_POLICY);
        
        // Set CORS headers
        if (SECURITY_CORS_ENABLED) {
            header("Access-Control-Allow-Origin: " . implode(', ', SECURITY_CORS_ALLOW_ORIGIN));
            header("Access-Control-Allow-Methods: " . implode(', ', SECURITY_CORS_ALLOW_METHODS));
            header("Access-Control-Allow-Headers: " . implode(', ', SECURITY_CORS_ALLOW_HEADERS));
            header("Access-Control-Allow-Credentials: " . (SECURITY_CORS_ALLOW_CREDENTIALS ? 'true' : 'false'));
            header("Access-Control-Expose-Headers: " . implode(', ', SECURITY_CORS_EXPOSE_HEADERS));
            header("Access-Control-Max-Age: " . SECURITY_CORS_MAX_AGE);
        }
    }
    
    private function checkRateLimit($request) {
        $key = $this->getRateLimitKey($request);
        $count = $this->cache->increment($key, 1);
        
        if ($count > SECURITY_RATE_LIMIT_MAX) {
            $this->logger->log('WARNING', 'Rate limit exceeded', [
                'ip' => $_SERVER['REMOTE_ADDR'],
                'request' => $request
            ]);
            return false;
        }
        
        return true;
    }
    
    private function validateInput($request) {
        if (strlen($request) > SECURITY_INPUT_MAX_LENGTH) {
            throw new Exception('Input too long');
        }
        
        if (SECURITY_INPUT_SANITIZE) {
            $request = $this->sanitizeInput($request);
        }
        
        return $request;
    }
    
    private function validateCsrfToken($request) {
        $token = $this->getCsrfToken($request);
        if (!$this->cache->exists($token)) {
            throw new Exception('Invalid CSRF token');
        }
    }
    
    private function validateAuthentication($request) {
        // Implement authentication validation
        // This will depend on the chosen authentication method
    }
    
    private function checkIpRestrictions() {
        $ip = $_SERVER['REMOTE_ADDR'];
        
        if (SECURITY_IP_WHITELIST_ENABLED && !in_array($ip, SECURITY_IP_WHITELIST)) {
            throw new Exception('IP not whitelisted');
        }
        
        if (SECURITY_IP_BLACKLIST_ENABLED && in_array($ip, SECURITY_IP_BLACKLIST)) {
            throw new Exception('IP blacklisted');
        }
    }
    
    private function validateApiKey($request) {
        $apiKey = $this->getApiKey($request);
        if (!$this->db->validateApiKey($apiKey)) {
            throw new Exception('Invalid API key');
        }
    }
    
    private function logRequest($request) {
        $this->logger->log('INFO', 'Request received', [
            'ip' => $_SERVER['REMOTE_ADDR'],
            'request' => $request,
            'timestamp' => time()
        ]);
    }
    
    private function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input);
        
        if (SECURITY_INPUT_SANITIZE) {
            $input = filter_var($input, FILTER_SANITIZE_STRING);
        }
        
        return $input;
    }
    
    private function getRateLimitKey($request) {
        return SECURITY_RATE_LIMIT_KEY_PREFIX . $_SERVER['REMOTE_ADDR'];
    }
    
    private function getCsrfToken($request) {
        return $_POST[SECURITY_CSRF_TOKEN_NAME] ?? 
               $_GET[SECURITY_CSRF_TOKEN_NAME] ?? 
               $_SERVER[SECURITY_CSRF_TOKEN_HEADER] ?? '';
    }
    
    private function getApiKey($request) {
        return $_POST[SECURITY_API_KEY_QUERY_PARAM] ?? 
               $_GET[SECURITY_API_KEY_QUERY_PARAM] ?? 
               $_SERVER[SECURITY_API_KEY_HEADER] ?? '';
    }
    
    private function isCsrfProtected($request) {
        // Implement CSRF protection logic
        return true;
    }
    
    private function isAuthRequired($request) {
        // Implement authentication requirement logic
        return true;
    }
    
    private function isApiKeyRequired($request) {
        // Implement API key requirement logic
        return true;
    }
}
