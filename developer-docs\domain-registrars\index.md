+++
chapter = true
icon = "<i class='fa fa-globe fa-fw'></i>"
next = "/domain-registrars/getting-started/"
title = "Domain Registrars"
weight = 0

+++

## Introduction

**Registrar Modules** allow for the registration and management of domains within WHMCS.

Registrar Modules are also referred to as **Domain Modules**.

The core function of a registrar module is registering, transferring and renewing of domains. These are triggered when payments are made within WHMCS for domain purchases or renewals.

Other functionality a registrar module can provide includes the following:

* checking availability of domains
* providing domain name suggestions
* viewing and updating of nameservers
* viewing and updating of WHOIS information
* viewing and management of DNS Host Records
* viewing and management of Email Forwarding services
* fetching of the EPP Code / Domain Release
* management of Registrar Lock status
* registering, modification and deletion of Private Nameservers
* enable/disable of ID Protection
* domain expiry date and status synchronization
* transfer status monitoring
* deletion requests

Unlimited custom additional functionality can also be implemented using custom methods.
