<?php
/**
 * <PERSON>hza Payment Gateway for WHMCS
 *
 * @package    WHMCS
 * <AUTHOR> Team
 * @copyright  Copyright (c) 2025 Lahza
 * @license    https://www.lahza.io/terms
 * @version    2.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Define module related metadata
 * @return array
 */
function lahza_MetaData() {
    return [
        'DisplayName' => 'Lahza Payment Gateway',
        'APIVersion' => '2.0',
        'DisableLocalCredtCardInput' => true,
        'TokenisedStorage' => false,
    ];
}

/**
 * Define gateway configuration options
 * @return array
 */
function lahza_config() {
    return [
        'FriendlyName' => [
            'Type' => 'System',
            'Value' => 'Lahza Payment',
        ],
        'publicKey' => [
            'FriendlyName' => 'Public Key',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your Lahza Public Key',
        ],
        'secretKey' => [
            'FriendlyName' => 'Secret Key',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your Lahza Secret Key',
        ],
        'webhookSecret' => [
            'FriendlyName' => 'Webhook Secret',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your Lahza Webhook Secret',
        ],
        'testMode' => [
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Enable to use test mode',
        ],
    ];
}

/**
 * Payment link
 * @param array $params Payment Gateway Module Parameters
 * @return string
 */
function lahza_link($params) {
    // Gateway Configuration Parameters
    $publicKey = $params['publicKey'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];
    
    // Invoice Parameters
    $invoiceId = $params['invoiceid'];
    $description = $params["description"];
    $amount = $params['amount'];
    $currency = $params['currency'];
    
    // Client Parameters
    $firstname = $params['clientdetails']['firstname'];
    $lastname = $params['clientdetails']['lastname'];
    $email = $params['clientdetails']['email'];
    $phone = $params['clientdetails']['phonenumber'];
    
    // System Parameters
    $systemUrl = $params['systemurl'];
    $returnUrl = $params['returnurl'];
    $callbackUrl = $systemUrl . 'modules/gateways/callback/lahza.php';
    
    // Prepare API request
    $apiUrl = $testMode ? 'https://api.sandbox.lahza.io/v1/payments' : 'https://api.lahza.io/v1/payments';
    
    $payload = [
        'amount' => (int)($amount * 100), // Convert to smallest currency unit
        'currency' => $currency,
        'order_id' => (string)$invoiceId,
        'description' => $description,
        'customer' => [
            'first_name' => $firstname,
            'last_name' => $lastname,
            'email' => $email,
            'phone' => $phone,
        ],
        'return_url' => $returnUrl,
        'callback_url' => $callbackUrl,
        'metadata' => [
            'whmcs_invoice_id' => $invoiceId,
        ],
    ];
    
    $payloadJson = json_encode($payload);
    $timestamp = time();
    $signature = hash_hmac('sha256', $publicKey . $timestamp . $payloadJson, $secretKey);
    
    // Make API request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'X-Lahza-Public-Key: ' . $publicKey,
        'X-Lahza-Timestamp: ' . $timestamp,
        'X-Lahza-Signature: ' . $signature,
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payloadJson);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Log the request
    logModuleCall('lahza', 'Payment Initiation', $payload, $response, $error, [$publicKey]);
    
    if ($httpCode === 201) {
        $responseData = json_decode($response, true);
        if (isset($responseData['data']['payment_url'])) {
            // Redirect to Lahza payment page
            header('Location: ' . $responseData['data']['payment_url']);
            exit;
        }
    }
    
    // If we get here, something went wrong
    return 'Error initiating payment. Please try again or contact support.';
}

/**
 * Refund transaction
 * @param array $params Payment Gateway Module Parameters
 * @return array Transaction response status
 */
function lahza_refund($params) {
    // Gateway Configuration Parameters
    $publicKey = $params['publicKey'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];
    
    // Transaction Parameters
    $transactionId = $params['transid'];
    $amount = $params['amount'];
    $currency = $params['currency'];
    
    // Prepare API request
    $apiUrl = ($testMode ? 'https://api.sandbox.lahza.io' : 'https://api.lahza.io') . 
              "/v1/payments/{$transactionId}/refund";
    
    $payload = [
        'amount' => (int)($amount * 100), // Convert to smallest currency unit
        'currency' => $currency,
        'reason' => 'Refund requested via WHMCS',
    ];
    
    $payloadJson = json_encode($payload);
    $timestamp = time();
    $signature = hash_hmac('sha256', $publicKey . $timestamp . $payloadJson, $secretKey);
    
    // Make API request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'X-Lahza-Public-Key: ' . $publicKey,
        'X-Lahza-Timestamp: ' . $timestamp,
        'X-Lahza-Signature: ' . $signature,
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payloadJson);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Log the request
    logModuleCall('lahza', 'Refund Request', $payload, $response, $error, [$publicKey]);
    
    if ($httpCode === 200) {
        $responseData = json_decode($response, true);
        if (isset($responseData['success']) && $responseData['success']) {
            return [
                'status' => 'success',
                'rawdata' => $responseData,
                'transid' => $responseData['data']['refund_id'] ?? $transactionId,
                'fees' => 0, // You may need to adjust this based on your fee structure
            ];
        }
    }
    
    return [
        'status' => 'error',
        'rawdata' => $response,
        'transid' => $transactionId,
    ];
}
