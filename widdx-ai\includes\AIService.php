<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Cache.php';
require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/../config/ai_config.php';

class AIService {
    private $db;
    private $cache;
    private $logger;
    private $models = [];
    private $security;

    public function __construct() {
        $this->db = new Database();
        $this->cache = new Cache();
        $this->logger = new Logger();
        $this->security = new Security();
        $this->initializeModels();
    }

    private function initializeModels() {
        foreach (AI_MODELS as $model => $config) {
            if ($config['enabled']) {
                $this->models[$model] = $config;
            }
        }
    }

    public function processQuestion($question, $context = null) {
        try {
            // Validate and sanitize input
            $question = $this->security->validateRequest($question);
            
            // Check cache first
            $cachedResponse = $this->checkCache($question, $context);
            if ($cachedResponse) {
                return $this->formatResponse($cachedResponse, 'CACHED');
            }
            
            // Check database for similar questions
            $similarQuestion = $this->findSimilarQuestion($question);
            if ($similarQuestion) {
                return $this->formatResponse($similarQuestion['answer_text'], $similarQuestion['source_model']);
            }
            
            // Query AI models
            $responses = $this->queryModels($question, $context);
            
            // Select best response
            $bestResponse = $this->selectBestResponse($responses);
            
            // Store in database and cache
            $this->storeQuestion($question, $bestResponse['answer'], $bestResponse['model']);
            $this->cache->set($this->getCacheKey($question, $context), [
                'answer' => $bestResponse['answer'],
                'model' => $bestResponse['model'],
                'timestamp' => time()
            ], AI_CACHE_TTL);
            
            // Log the response
            $this->logger->log('INFO', 'Processed question successfully', [
                'question' => $question,
                'model' => $bestResponse['model'],
                'response_length' => strlen($bestResponse['answer'])
            ]);
            
            return $this->formatResponse($bestResponse['answer'], $bestResponse['model']);
            
        } catch (Exception $e) {
            $this->logger->log('ERROR', 'Error processing question', [
                'question' => $question,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function validateQuestion($question) {
        if (empty($question)) {
            throw new InvalidArgumentException(AI_ERROR_MESSAGES['invalid_input']);
        }
        
        if (strlen($question) > AI_VALIDATION_RULES['question']['max_length']) {
            throw new InvalidArgumentException(AI_ERROR_MESSAGES['invalid_input']);
        }
        
        return $this->sanitizeInput($question);
    }

    private function checkCache($question, $context) {
        $cacheKey = $this->getCacheKey($question, $context);
        if ($this->cache->exists($cacheKey)) {
            $cachedData = $this->cache->get($cacheKey);
            return [
                'answer' => $cachedData['answer'],
                'model' => $cachedData['model'],
                'timestamp' => $cachedData['timestamp']
            ];
        }
        return null;
    }

    private function findSimilarQuestion($question) {
        $db = $this->db->getConnection();
        $stmt = $db->prepare("
            SELECT *
            FROM questions
            WHERE MATCH(question_text) AGAINST(:question IN NATURAL LANGUAGE MODE)
            ORDER BY similarity DESC
            LIMIT 1
        ");
        
        $stmt->execute([':question' => $question]);
        return $stmt->fetch();
    }

    private function queryModels($question, $context) {
        $responses = [];
        
        foreach ($this->models as $model => $config) {
            try {
                $response = $this->queryModel($model, $question, $context);
                $responses[$model] = [
                    'answer' => $response['answer'],
                    'confidence' => $response['confidence'] ?? 0.7,
                    'length' => strlen($response['answer']),
                    'model' => $model,
                    'timestamp' => time()
                ];
                
                // Log individual model response
                $this->logger->log('DEBUG', 'Model response received', [
                    'model' => $model,
                    'response_length' => $response['answer']
                ]);
            } catch (Exception $e) {
                $this->logger->log('WARNING', 'Error querying model', [
                    'model' => $model,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }
        
        return $responses;
    }

    private function queryModel($model, $question, $context) {
        $config = $this->models[$model];
        $prompt = $this->buildPrompt($model, $question, $context);
        
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $config['api_key']
        ];
        
        $data = [
            'model' => $config['model'],
            'messages' => [
                ['role' => 'system', 'content' => $prompt['system']],
                ['role' => 'user', 'content' => $prompt['user']],
                ['role' => 'assistant', 'content' => '']
            ],
            'temperature' => $config['temperature'],
            'max_tokens' => $config['max_tokens']
        ];
        
        $ch = curl_init($config['endpoint']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, $config['timeout']);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception($error);
        }
        
        $result = json_decode($response, true);
        if (isset($result['error'])) {
            throw new Exception($result['error']['message']);
        }
        
        return [
            'answer' => $result['choices'][0]['message']['content'],
            'confidence' => $result['choices'][0]['finish_reason'] === 'stop' ? 1.0 : 0.8
        ];
    }

    private function buildPrompt($model, $question, $context) {
        $template = AI_PROMPT_TEMPLATES['default'];
        if ($context && isset(AI_PROMPT_TEMPLATES[$context])) {
            $template = AI_PROMPT_TEMPLATES[$context];
        }
        
        return [
            'system' => $template['system'],
            'user' => str_replace('{question}', $question, $template['user'])
        ];
    }

    private function selectBestResponse($responses) {
        $bestResponse = null;
        $bestScore = 0;
        
        foreach ($responses as $response) {
            $score = $this->calculateScore($response);
            if ($score > $bestScore) {
                $bestScore = $score;
                $bestResponse = $response;
            }
        }
        
        return $bestResponse;
    }

    private function calculateScore($response) {
        $criteria = AI_RESPONSE_SELECTION_CRITERIA;
        return (
            $response['confidence'] * $criteria['confidence'] +
            ($response['length'] / 2048) * $criteria['length']
        );
    }

    private function storeQuestion($question, $answer, $model) {
        $db = $this->db->getConnection();
        $stmt = $db->prepare("
            INSERT INTO questions 
            (question_text, answer_text, source_model, created_at)
            VALUES (:question, :answer, :model, NOW())
        ");
        
        $stmt->execute([
            ':question' => $question,
            ':answer' => $answer,
            ':model' => $model
        ]);
    }

    private function formatResponse($answer, $model) {
        return [
            'answer' => $answer,
            'model' => $model,
            'timestamp' => time()
        ];
    }

    private function getCacheKey($question, $context) {
        return AI_CACHE_PREFIX . md5($question . (string)$context);
    }

    private function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input);
        
        if (AI_SECURITY_INPUT_VALIDATION) {
            $input = filter_var($input, FILTER_SANITIZE_STRING);
        }
        
        return $input;
    }

    public function getMetrics() {
        $metrics = [
            'models' => count($this->models),
            'cache_hits' => $this->cache->getStats()['hits'] ?? 0,
            'cache_misses' => $this->cache->getStats()['misses'] ?? 0,
            'questions_count' => $this->getQuestionsCount(),
            'average_response_time' => $this->getAverageResponseTime(),
            'error_rate' => $this->getErrorRate()
        ];
        
        return $metrics;
    }

    private function getQuestionsCount() {
        $db = $this->db->getConnection();
        $stmt = $db->query("SELECT COUNT(*) FROM questions");
        return $stmt->fetchColumn();
    }

    private function getAverageResponseTime() {
        $db = $this->db->getConnection();
        $stmt = $db->query("
            SELECT AVG(TIMESTAMPDIFF(MICROSECOND, created_at, answered_at)) / 1000 as avg_time
            FROM questions
            WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        return $stmt->fetchColumn() ?? 0;
    }

    private function getErrorRate() {
        $db = $this->db->getConnection();
        $stmt = $db->query("
            SELECT 
                COUNT(CASE WHEN error IS NOT NULL THEN 1 END) / COUNT(*) as error_rate
            FROM questions
            WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        return $stmt->fetchColumn() ?? 0;
    }
}
