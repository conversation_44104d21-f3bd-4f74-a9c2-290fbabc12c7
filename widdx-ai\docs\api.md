# وثائق API - WIDDX

## مقدمة

WIDDX هو مساعد ذكي يدمج بين نماذج الذكاء الاصطناعي المتقدمة لتوفير إجابات دقيقة وموثوقة على أسئلتك. هذه الوثائق تشرح كيفية استخدام API الخاص بـ WIDDX.

## متطلبات الأمان

- يجب استخدام HTTPS في جميع الطلبات
- يجب تمرير رمز CSRF مع كل طلب POST
- يجب تمرير مفتاح API صالح
- جميع البيانات يجب أن تكون مرمزة باستخدام UTF-8

## نقاط النهاية (Endpoints)

### 1. طلب الإجابة

**URL**: `/api/ask.php`

**النوع**: POST

**الوصف**: يرسل سؤالاً إلى المساعد ويستلم الإجابة

**المعلمات**:

| المعلمات | النوع | المطلوبة | الوصف |
|-----------|--------|-----------|--------|
| question | string | نعم | السؤال الذي تريد طرحه |
| language | string | لا | لغة السؤال (افتراضي: ar) |
| source | array | لا | مصادر مفضلة (DEEPSEEK, GEMINI) |

**مثال للطلب**:
```json
{
    "question": "ما هو الذكاء الاصطناعي؟",
    "language": "ar",
    "source": ["DEEPSEEK", "GEMINI"]
}
```

**مثال للرد**:
```json
{
    "status": "success",
    "data": {
        "answer": "الذكاء الاصطناعي هو...",
        "source": "DEEPSEEK",
        "cached": true,
        "confidence": 0.95
    }
}
```

### 2. إحصائيات الاستخدام

**URL**: `/api/stats.php`

**النوع**: GET

**الوصف**: يوفر إحصائيات حول استخدام المساعد

**مثال للرد**:
```json
{
    "status": "success",
    "data": {
        "total_questions": 12345,
        "cached_responses": 8500,
        "api_calls": {
            "DEEPSEEK": 2000,
            "GEMINI": 1845
        },
        "cache_stats": {
            "size": "2.5MB",
            "hits": 8500,
            "misses": 3845
        }
    }
}
```

### 3. تحقق من حالة النظام

**URL**: `/api/status.php`

**النوع**: GET

**الوصف**: يتحقق من حالة النظام وخدمات API

**مثال للرد**:
```json
{
    "status": "success",
    "data": {
        "system": {
            "version": "1.0.0",
            "uptime": "2d 5h 30m",
            "memory_usage": "128MB"
        },
        "services": {
            "redis": "healthy",
            "database": "healthy",
            "cache": "healthy"
        }
    }
}
```

## أخطاء API

جميع الأخطاء ترد في الصيغة التالية:

```json
{
    "status": "error",
    "message": "وصف الخطأ",
    "code": "خطأ_محدد",
    "details": {
        "field": "value"
    }
}
```

## أمثلة على الأخطاء

```json
{
    "status": "error",
    "message": "السؤال مطلوب",
    "code": "QUESTION_REQUIRED",
    "details": {
        "field": "question"
    }
}

{
    "status": "error",
    "message": "مفتاح API غير صالح",
    "code": "INVALID_API_KEY",
    "details": {
        "key": "your_api_key"
    }
}
```

## أفضل الممارسات

1. استخدم HTTPS دائماً
2. تخزين مفاتيح API في مكان آمن
3. تمرير رمز CSRF مع كل طلب POST
4. تحقق من حالة API قبل الاستخدام
5. التعامل مع الأخطاء بشكل صحيح
6. استخدام التخزين المؤقت لتحسين الأداء

## دعم ومساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- البريد الإلكتروني: <EMAIL>
- الموقع: https://widdx.ai
- الوثائق: https://docs.widdx.ai
