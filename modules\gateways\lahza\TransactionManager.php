<?php
/**
 * Lahza Transaction Status Manager
 * Comprehensive transaction lifecycle management
 * 
 * @package    WHMCS
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @version    1.0.0
 */

// Prevent direct access
if (!defined("WHMCS")) {
    die("Direct access prohibited");
}

/**
 * Transaction Status Manager for Lahza Gateway
 */
class LahzaTransactionManager {
    
    private $logger;
    private $gatewayParams;
    
    // Transaction status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_AUTHORIZED = 'authorized';
    const STATUS_CAPTURED = 'captured';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_PARTIALLY_REFUNDED = 'partially_refunded';
    const STATUS_DISPUTED = 'disputed';
    const STATUS_EXPIRED = 'expired';
    
    // 3D Secure status constants
    const TDS_STATUS_REQUIRED = '3ds_required';
    const TDS_STATUS_AUTHENTICATED = '3ds_authenticated';
    const TDS_STATUS_ATTEMPTED = '3ds_attempted';
    const TDS_STATUS_FAILED = '3ds_failed';
    const TDS_STATUS_UNAVAILABLE = '3ds_unavailable';
    
    /**
     * Constructor
     */
    public function __construct($gatewayParams, $logger = null) {
        $this->gatewayParams = $gatewayParams;
        $this->logger = $logger ?: new LahzaLogger();
    }
    
    /**
     * Create a new transaction record
     */
    public function createTransaction($invoiceId, $amount, $currency, $metadata = []) {
        $transactionId = $this->generateTransactionId($invoiceId);
        
        $transaction = [
            'transaction_id' => $transactionId,
            'invoice_id' => $invoiceId,
            'amount' => $amount,
            'currency' => $currency,
            'status' => self::STATUS_PENDING,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'metadata' => $metadata,
            'status_history' => [
                [
                    'status' => self::STATUS_PENDING,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'message' => 'Transaction created'
                ]
            ]
        ];
        
        $this->saveTransaction($transaction);
        
        $this->logger->info('Transaction created', [
            'transaction_id' => $transactionId,
            'invoice_id' => $invoiceId,
            'amount' => $amount,
            'currency' => $currency
        ], 'transactions');
        
        return $transactionId;
    }
    
    /**
     * Update transaction status
     */
    public function updateStatus($transactionId, $newStatus, $message = '', $metadata = []) {
        $transaction = $this->getTransaction($transactionId);
        
        if (!$transaction) {
            $this->logger->error('Transaction not found for status update', [
                'transaction_id' => $transactionId,
                'new_status' => $newStatus
            ], 'transactions');
            return false;
        }
        
        $oldStatus = $transaction['status'];
        
        // Validate status transition
        if (!$this->isValidStatusTransition($oldStatus, $newStatus)) {
            $this->logger->warning('Invalid status transition attempted', [
                'transaction_id' => $transactionId,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ], 'transactions');
            return false;
        }
        
        // Update transaction
        $transaction['status'] = $newStatus;
        $transaction['updated_at'] = date('Y-m-d H:i:s');
        
        // Add to status history
        $transaction['status_history'][] = [
            'status' => $newStatus,
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message ?: "Status changed from {$oldStatus} to {$newStatus}",
            'metadata' => $metadata
        ];
        
        // Merge metadata
        if (!empty($metadata)) {
            $transaction['metadata'] = array_merge($transaction['metadata'], $metadata);
        }
        
        $this->saveTransaction($transaction);
        
        $this->logger->info('Transaction status updated', [
            'transaction_id' => $transactionId,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'message' => $message
        ], 'transactions');
        
        // Handle status-specific actions
        $this->handleStatusChange($transaction, $oldStatus, $newStatus);
        
        return true;
    }
    
    /**
     * Get transaction by ID
     */
    public function getTransaction($transactionId) {
        $filePath = $this->getTransactionFilePath($transactionId);
        
        if (!file_exists($filePath)) {
            return null;
        }
        
        $content = file_get_contents($filePath);
        return json_decode($content, true);
    }
    
    /**
     * Get transactions by invoice ID
     */
    public function getTransactionsByInvoice($invoiceId) {
        $transactions = [];
        $pattern = $this->getTransactionDir() . "/inv_{$invoiceId}_*.json";
        
        foreach (glob($pattern) as $file) {
            $content = file_get_contents($file);
            $transaction = json_decode($content, true);
            if ($transaction) {
                $transactions[] = $transaction;
            }
        }
        
        // Sort by created date
        usort($transactions, function($a, $b) {
            return strtotime($a['created_at']) - strtotime($b['created_at']);
        });
        
        return $transactions;
    }
    
    /**
     * Process payment completion
     */
    public function processPaymentCompletion($transactionId, $paymentData) {
        $transaction = $this->getTransaction($transactionId);
        
        if (!$transaction) {
            $this->logger->error('Transaction not found for payment completion', [
                'transaction_id' => $transactionId
            ], 'transactions');
            return false;
        }
        
        // Update transaction with payment data
        $this->updateStatus(
            $transactionId,
            self::STATUS_COMPLETED,
            'Payment completed successfully',
            [
                'payment_id' => $paymentData['payment_id'] ?? '',
                'gateway_transaction_id' => $paymentData['gateway_transaction_id'] ?? '',
                'payment_method' => $paymentData['payment_method'] ?? '',
                'completed_at' => date('Y-m-d H:i:s')
            ]
        );
        
        // Add payment to WHMCS
        $invoiceId = $transaction['invoice_id'];
        $amount = $transaction['amount'];
        $transactionId = $paymentData['gateway_transaction_id'] ?? $transactionId;
        
        addInvoicePayment(
            $invoiceId,
            $transactionId,
            $amount,
            0, // fees
            $this->gatewayParams['name']
        );
        
        $this->logger->info('Payment completed and added to WHMCS', [
            'transaction_id' => $transactionId,
            'invoice_id' => $invoiceId,
            'amount' => $amount
        ], 'transactions');
        
        return true;
    }
    
    /**
     * Process refund
     */
    public function processRefund($transactionId, $refundAmount, $reason = '') {
        $transaction = $this->getTransaction($transactionId);
        
        if (!$transaction) {
            return false;
        }
        
        $totalAmount = $transaction['amount'];
        $isPartialRefund = $refundAmount < $totalAmount;
        
        $newStatus = $isPartialRefund ? self::STATUS_PARTIALLY_REFUNDED : self::STATUS_REFUNDED;
        
        $this->updateStatus(
            $transactionId,
            $newStatus,
            "Refund processed: {$refundAmount} {$transaction['currency']}",
            [
                'refund_amount' => $refundAmount,
                'refund_reason' => $reason,
                'refunded_at' => date('Y-m-d H:i:s')
            ]
        );
        
        return true;
    }
    
    /**
     * Validate status transition
     */
    private function isValidStatusTransition($oldStatus, $newStatus) {
        $validTransitions = [
            self::STATUS_PENDING => [
                self::STATUS_PROCESSING,
                self::STATUS_AUTHORIZED,
                self::STATUS_FAILED,
                self::STATUS_CANCELLED,
                self::STATUS_EXPIRED,
                self::TDS_STATUS_REQUIRED
            ],
            self::STATUS_PROCESSING => [
                self::STATUS_AUTHORIZED,
                self::STATUS_CAPTURED,
                self::STATUS_COMPLETED,
                self::STATUS_FAILED,
                self::TDS_STATUS_REQUIRED
            ],
            self::STATUS_AUTHORIZED => [
                self::STATUS_CAPTURED,
                self::STATUS_COMPLETED,
                self::STATUS_CANCELLED,
                self::STATUS_EXPIRED
            ],
            self::STATUS_CAPTURED => [
                self::STATUS_COMPLETED,
                self::STATUS_REFUNDED,
                self::STATUS_PARTIALLY_REFUNDED,
                self::STATUS_DISPUTED
            ],
            self::STATUS_COMPLETED => [
                self::STATUS_REFUNDED,
                self::STATUS_PARTIALLY_REFUNDED,
                self::STATUS_DISPUTED
            ],
            self::TDS_STATUS_REQUIRED => [
                self::TDS_STATUS_AUTHENTICATED,
                self::TDS_STATUS_ATTEMPTED,
                self::TDS_STATUS_FAILED,
                self::TDS_STATUS_UNAVAILABLE
            ],
            self::TDS_STATUS_AUTHENTICATED => [
                self::STATUS_PROCESSING,
                self::STATUS_AUTHORIZED,
                self::STATUS_COMPLETED
            ],
            self::TDS_STATUS_ATTEMPTED => [
                self::STATUS_PROCESSING,
                self::STATUS_AUTHORIZED,
                self::STATUS_COMPLETED
            ]
        ];
        
        return isset($validTransitions[$oldStatus]) && 
               in_array($newStatus, $validTransitions[$oldStatus]);
    }
    
    /**
     * Handle status-specific actions
     */
    private function handleStatusChange($transaction, $oldStatus, $newStatus) {
        switch ($newStatus) {
            case self::STATUS_FAILED:
                $this->handleFailedPayment($transaction);
                break;
                
            case self::STATUS_COMPLETED:
                $this->handleCompletedPayment($transaction);
                break;
                
            case self::STATUS_REFUNDED:
            case self::STATUS_PARTIALLY_REFUNDED:
                $this->handleRefund($transaction);
                break;
        }
    }
    
    /**
     * Handle failed payment
     */
    private function handleFailedPayment($transaction) {
        // Log the failure
        logTransaction($this->gatewayParams['name'], [
            'invoice_id' => $transaction['invoice_id'],
            'transaction_id' => $transaction['transaction_id'],
            'error' => 'Payment failed'
        ], 'Failed');
        
        // Send notification if configured
        $this->sendFailureNotification($transaction);
    }
    
    /**
     * Handle completed payment
     */
    private function handleCompletedPayment($transaction) {
        // Log the success
        logTransaction($this->gatewayParams['name'], [
            'invoice_id' => $transaction['invoice_id'],
            'transaction_id' => $transaction['transaction_id'],
            'amount' => $transaction['amount']
        ], 'Success');
        
        // Send confirmation if configured
        $this->sendSuccessNotification($transaction);
    }
    
    /**
     * Handle refund
     */
    private function handleRefund($transaction) {
        // Log the refund
        logTransaction($this->gatewayParams['name'], [
            'invoice_id' => $transaction['invoice_id'],
            'transaction_id' => $transaction['transaction_id'],
            'refund_amount' => $transaction['metadata']['refund_amount'] ?? 0
        ], 'Refund');
    }
    
    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId($invoiceId) {
        return $invoiceId . '_' . time() . '_' . substr(md5(uniqid()), 0, 8);
    }
    
    /**
     * Save transaction to file
     */
    private function saveTransaction($transaction) {
        $filePath = $this->getTransactionFilePath($transaction['transaction_id']);
        $dir = dirname($filePath);
        
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        file_put_contents($filePath, json_encode($transaction, JSON_PRETTY_PRINT), LOCK_EX);
    }
    
    /**
     * Get transaction file path
     */
    private function getTransactionFilePath($transactionId) {
        return $this->getTransactionDir() . '/' . $transactionId . '.json';
    }
    
    /**
     * Get transaction directory
     */
    private function getTransactionDir() {
        return __DIR__ . '/../logs/transactions';
    }
    
    /**
     * Send failure notification
     */
    private function sendFailureNotification($transaction) {
        // Implementation would depend on notification preferences
        $this->logger->info('Payment failure notification sent', [
            'transaction_id' => $transaction['transaction_id'],
            'invoice_id' => $transaction['invoice_id']
        ], 'notifications');
    }
    
    /**
     * Send success notification
     */
    private function sendSuccessNotification($transaction) {
        // Implementation would depend on notification preferences
        $this->logger->info('Payment success notification sent', [
            'transaction_id' => $transaction['transaction_id'],
            'invoice_id' => $transaction['invoice_id']
        ], 'notifications');
    }
}
