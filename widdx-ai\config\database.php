<?php
// Security Configuration
if (!defined('SECURE_DATABASE_CONFIG_LOADED')) {
    define('SECURE_DATABASE_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables utility
require_once __DIR__ . '/../includes/Env.php';

// Database configuration
const DB_HOST = Env::get('DB_HOST', 'localhost');
const DB_USER = Env::get('DB_USERNAME');
const DB_PASS = Env::get('DB_PASSWORD');
const DB_NAME = Env::get('DB_DATABASE');
const DB_PORT = Env::get('DB_PORT', '3306');
const DB_CHARSET = Env::get('DB_CHARSET', 'utf8mb4');

// Database connection settings
const DB_PERSISTENT = Env::get('DB_PERSISTENT', true);
const DB_TIMEOUT = Env::get('DB_TIMEOUT', 30);
const DB_RECONNECT_ATTEMPTS = Env::get('DB_RECONNECT_ATTEMPTS', 3);

// API configuration
const DEEPSEEK_API_URL = Env::get('DEEPSEEK_API_URL', 'https://api.deepseek.com/answer');
const GEMINI_API_URL = Env::get('GEMINI_API_URL', 'https://api.gemini.com/answer');
const API_KEY_DEEPSEEK = Env::get('DEEPSEEK_API_KEY');
const API_KEY_GEMINI = Env::get('GEMINI_API_KEY');

// Validate API keys
if (empty(API_KEY_DEEPSEEK) || empty(API_KEY_GEMINI)) {
    trigger_error('API keys are not properly configured', E_USER_WARNING);
}

// Error reporting configuration
if (Env::get('APP_ENV') === 'production') {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/../logs/error.log');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/../logs/error.log');
}

// Create logs directory with proper permissions
$logDir = __DIR__ . '/../logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

// Set proper permissions
chmod($logDir, 0755);

// Database connection validation
if (!DB_HOST || !DB_USER || !DB_PASS || !DB_NAME) {
    trigger_error('Database configuration is incomplete', E_USER_ERROR);
}

// Validate database port
if (!is_numeric(DB_PORT)) {
    trigger_error('Invalid database port number', E_USER_ERROR);
}

// Validate database charset
$validCharsets = ['utf8', 'utf8mb4', 'latin1', 'ascii'];
if (!in_array(DB_CHARSET, $validCharsets)) {
    trigger_error('Invalid database charset', E_USER_WARNING);
}
