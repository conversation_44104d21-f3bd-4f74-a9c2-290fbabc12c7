/*!
 * WIDDX - Merged JavaScript
 * Combines all functionality into a single, optimized file
 * Version: 1.0.0
 */

// ===== CORE UTILITIES =====
const WIDDX = (function() {
    // Performance tracking
    const perfStart = performance.now();
    
    // Global error handling
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
    });

    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
    });

    // Animation metrics
    const animationMetrics = {
        activeAnimations: 0,
        maxActive: 0,
        startTime: performance.now(),
        logAnimation: function(name) {
            this.activeAnimations++;
            if (this.activeAnimations > this.maxActive) {
                this.maxActive = this.activeAnimations;
            }
            console.log(`🔄 Animation started: ${name} (Active: ${this.activeAnimations})`);
        },
        endAnimation: function(name) {
            this.activeAnimations--;
            console.log(`✅ Animation completed: ${name} (Active: ${this.activeAnimations})`);
        },
        report: function() {
            const duration = (performance.now() - this.startTime) / 1000;
            console.log(`📊 Animation Metrics:
- Max concurrent animations: ${this.maxActive}
- Total animation time: ${duration.toFixed(2)}s
- Current active: ${this.activeAnimations}`);
        }
    };

    // Utility functions
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Expose public methods
    return {
        animationMetrics,
        debounce,
        throttle,
        perfStart
    };
})();

// ===== MODERN BACKGROUND SYSTEM =====
class ModernBackground {
    constructor() {
        this.particles = [];
        this.particleCount = 50;
        this.animationId = null;
        this.isInitialized = false;
        this.performanceMode = this.detectPerformanceMode();
        this.init();
    }

    detectPerformanceMode() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) return 'low';

        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            if (renderer.includes('Intel') || renderer.includes('Mobile')) {
                return 'medium';
            }
        }

        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            return 'low';
        }

        return 'high';
    }

    init() {
        if (this.isInitialized) return;
        
        this.setupCanvas();
        this.createParticles();
        this.animate();
        this.setupResizeHandler();
        this.isInitialized = true;
    }

    // ... (rest of ModernBackground implementation)
}

// ===== GSAP ANIMATIONS =====
const GSAPAnimations = (function() {
    let scrollTriggers = [];
    let isInitialized = false;

    function init() {
        if (isInitialized || typeof gsap === 'undefined') return;
        
        setupScrollAnimations();
        setupHoverAnimations();
        setupPageTransitions();
        
        isInitialized = true;
    }

    function cleanup() {
        scrollTriggers.forEach(trigger => trigger.kill());
        scrollTriggers = [];
        isInitialized = false;
    }

    // ... (animation implementations)

    return {
        init,
        cleanup
    };
})();

// ===== UI COMPONENTS =====
const UI = (function() {
    function initThemeToggle() {
        const toggle = document.querySelector('.theme-toggle');
        if (!toggle) return;

        const currentTheme = localStorage.getItem('theme') || 'dark';
        document.documentElement.setAttribute('data-theme', currentTheme);

        toggle.addEventListener('click', () => {
            const newTheme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });
    }

    function initMobileMenu() {
        const menuToggle = document.querySelector('.mobile-menu-toggle');
        const menu = document.querySelector('.mobile-menu');
        
        if (!menuToggle || !menu) return;

        menuToggle.addEventListener('click', () => {
            menu.classList.toggle('active');
            menuToggle.classList.toggle('active');
        });
    }

    // ... (other UI components)

    return {
        initThemeToggle,
        initMobileMenu
    };
})();

// ===== MAIN INITIALIZATION =====
function initializeApp() {
    console.log('🚀 Initializing WIDDX App...');

    // Initialize critical UI components
    UI.initThemeToggle();
    UI.initMobileMenu();

    // Initialize animations
    if (typeof gsap !== 'undefined') {
        GSAPAnimations.init();
    }

    // Initialize modern background
    const modernBackground = new ModernBackground();

    // Performance monitoring
    if (process.env.NODE_ENV === 'development') {
        setInterval(() => {
            console.log('Performance check:', {
                fps: Math.round(gsap.ticker.fps()),
                memory: performance.memory ? (performance.memory.usedJSHeapSize / 1048576).toFixed(2) + 'MB' : 'N/A'
            });
        }, 5000);
    }
}

// Start the app when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    GSAPAnimations.cleanup();
    // Additional cleanup
});

// Export for debugging
window.WIDDX = WIDDX;
