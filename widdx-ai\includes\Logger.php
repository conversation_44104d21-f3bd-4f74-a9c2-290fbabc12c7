<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/ErrorHandler.php';

class Logger {
    private $db;
    private $errorHandler;
    private $logLevels = [
        'DEBUG' => 100,
        'INFO' => 200,
        'NOTICE' => 250,
        'WARNING' => 300,
        'ERROR' => 400,
        'CRITICAL' => 500,
        'ALERT' => 550,
        'EMERGENCY' => 600
    ];

    public function __construct() {
        $this->db = new Database();
        $this->errorHandler = new ErrorHandler();
    }

    public function log($level, $message, $context = []) {
        if (!$this->isValidLevel($level)) {
            throw new InvalidArgumentException("Invalid log level: $level");
        }

        try {
            $logEntry = [
                'level' => $level,
                'message' => $message,
                'context' => json_encode($context),
                'timestamp' => time(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ];

            $this->writeToDatabase($logEntry);
            $this->writeToFile($logEntry);
            
            if ($this->shouldNotify($level)) {
                $this->notify($logEntry);
            }

        } catch (Exception $e) {
            $this->errorHandler->handleException($e);
        }
    }

    private function isValidLevel($level) {
        return array_key_exists(strtoupper($level), $this->logLevels);
    }

    private function writeToDatabase($logEntry) {
        try {
            $stmt = $this->db->getConnection()->prepare("
                INSERT INTO logs (level, message, context, timestamp, ip, user_agent)
                VALUES (:level, :message, :context, :timestamp, :ip, :user_agent)
            ");
            
            $stmt->execute([
                ':level' => $logEntry['level'],
                ':message' => $logEntry['message'],
                ':context' => $logEntry['context'],
                ':timestamp' => $logEntry['timestamp'],
                ':ip' => $logEntry['ip'],
                ':user_agent' => $logEntry['user_agent']
            ]);
        } catch (Exception $e) {
            // If database logging fails, at least write to file
            error_log("Database logging failed: " . $e->getMessage());
        }
    }

    private function writeToFile($logEntry) {
        $logFile = __DIR__ . '/../logs/app.log';
        $logMessage = sprintf(
            "%s [%s] %s %s\n",
            date('Y-m-d H:i:s'),
            $logEntry['level'],
            $logEntry['message'],
            $logEntry['context']
        );
        
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }

    private function shouldNotify($level) {
        $severity = $this->logLevels[strtoupper($level)];
        return $severity >= 400; // Notify for ERROR and above
    }

    private function notify($logEntry) {
        // Implement notification system (email, Slack, etc.)
        // For now, just write to error log
        error_log("NOTIFICATION: " . $logEntry['message']);
    }

    public function getLogs($level = null, $limit = 100) {
        try {
            $query = "SELECT * FROM logs ORDER BY timestamp DESC LIMIT :limit";
            
            if ($level) {
                $query = "SELECT * FROM logs WHERE level = :level ORDER BY timestamp DESC LIMIT :limit";
            }

            $stmt = $this->db->getConnection()->prepare($query);
            $stmt->execute([':limit' => $limit]);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            $this->errorHandler->handleException($e);
            return [];
        }
    }

    public function getErrorCount($hours = 24) {
        try {
            $stmt = $this->db->getConnection()->prepare("
                SELECT COUNT(*) as count
                FROM logs
                WHERE level IN ('ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY')
                AND timestamp > DATE_SUB(NOW(), INTERVAL :hours HOUR)
            ");
            
            $stmt->execute([':hours' => $hours]);
            $result = $stmt->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            $this->errorHandler->handleException($e);
            return 0;
        }
    }

    public function getRecentErrors($limit = 10) {
        try {
            $stmt = $this->db->getConnection()->prepare("
                SELECT *
                FROM logs
                WHERE level IN ('ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY')
                ORDER BY timestamp DESC
                LIMIT :limit
            ");
            
            $stmt->execute([':limit' => $limit]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            $this->errorHandler->handleException($e);
            return [];
        }
    }

    public function clearLogs($days = 30) {
        try {
            $stmt = $this->db->getConnection()->prepare("
                DELETE FROM logs
                WHERE timestamp < DATE_SUB(NOW(), INTERVAL :days DAY)
            ");
            
            $stmt->execute([':days' => $days]);
            return $stmt->rowCount();
        } catch (Exception $e) {
            $this->errorHandler->handleException($e);
            return 0;
        }
    }
}
