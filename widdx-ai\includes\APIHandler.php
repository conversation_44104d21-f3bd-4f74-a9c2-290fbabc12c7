<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Cache.php';
require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/Security.php';
require_once __DIR__ . '/../config/api_config.php';

class APIHandler {
    private $db;
    private $cache;
    private $logger;
    private $security;
    private $routes;

    public function __construct() {
        $this->db = new Database();
        $this->cache = new Cache();
        $this->logger = new Logger();
        $this->security = new Security();
        $this->routes = API_ROUTES;
    }

    public function handleRequest($method, $path, $data = []) {
        try {
            // Validate request
            $this->validateRequest($method, $path, $data);
            
            // Check rate limiting
            $this->checkRateLimit($method, $path);
            
            // Authenticate if required
            if ($this->isAuthRequired($method, $path)) {
                $this->authenticateRequest();
            }
            
            // Dispatch to appropriate controller
            $response = $this->dispatchRequest($method, $path, $data);
            
            // Log successful request
            $this->logger->log('INFO', 'API request processed successfully', [
                'method' => $method,
                'path' => $path,
                'response_size' => strlen(json_encode($response))
            ]);
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->log('ERROR', 'API request error', [
                'method' => $method,
                'path' => $path,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function validateRequest($method, $path, $data) {
        // Check if route exists
        if (!$this->routeExists($method, $path)) {
            throw new Exception(API_ERROR_MESSAGES['not_found']);
        }
        
        // Validate input data
        $this->validateInput($data);
        
        // Check request size
        $this->checkRequestSize($data);
        
        // Validate content type
        $this->validateContentType();
    }

    private function checkRateLimit($method, $path) {
        $key = $this->getRateLimitKey($method, $path);
        $count = $this->cache->increment($key, 1);
        
        if ($count > API_RATE_LIMIT_MAX) {
            throw new Exception(API_ERROR_MESSAGES['rate_limit']);
        }
    }

    private function authenticateRequest() {
        // Implement authentication logic
        // This will depend on the chosen authentication method
    }

    private function dispatchRequest($method, $path, $data) {
        $route = $this->getRoute($method, $path);
        $controller = $route['controller'];
        $method = $route['method'];
        
        if (!class_exists($controller)) {
            throw new Exception(API_ERROR_MESSAGES['api_error']);
        }
        
        $controllerInstance = new $controller();
        if (!method_exists($controllerInstance, $method)) {
            throw new Exception(API_ERROR_MESSAGES['api_error']);
        }
        
        return $controllerInstance->$method($data);
    }

    private function validateInput($data) {
        if (empty($data)) {
            throw new Exception(API_ERROR_MESSAGES['invalid_input']);
        }
        
        // Add more specific input validation rules
    }

    private function checkRequestSize($data) {
        $size = strlen(json_encode($data));
        if ($size > API_REQUEST_MAX_BODY_SIZE) {
            throw new Exception(API_ERROR_MESSAGES['invalid_input']);
        }
    }

    private function validateContentType() {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (!in_array($contentType, API_CORS_ALLOW_HEADERS)) {
            throw new Exception(API_ERROR_MESSAGES['invalid_input']);
        }
    }

    private function routeExists($method, $path) {
        return isset($this->routes[$method][$path]);
    }

    private function isAuthRequired($method, $path) {
        $route = $this->getRoute($method, $path);
        return $route['auth_required'] ?? false;
    }

    private function getRoute($method, $path) {
        return $this->routes[$method][$path] ?? null;
    }

    private function getRateLimitKey($method, $path) {
        return API_RATE_LIMIT_KEY_PREFIX . $method . '_' . $path . '_' . $_SERVER['REMOTE_ADDR'];
    }

    public function getMetrics() {
        return [
            'requests' => $this->cache->get('api_requests') ?? 0,
            'errors' => $this->cache->get('api_errors') ?? 0,
            'response_time' => $this->cache->get('api_response_time') ?? 0,
            'cache_hits' => $this->cache->get('api_cache_hits') ?? 0,
            'cache_misses' => $this->cache->get('api_cache_misses') ?? 0
        ];
    }

    public function getAnalytics() {
        return [
            'users' => $this->db->getActiveUsers(),
            'questions' => $this->db->getQuestionsCount(),
            'response_time' => $this->db->getAverageResponseTime(),
            'error_rate' => $this->db->getErrorRate()
        ];
    }

    public function checkHealth() {
        $checks = [
            'database' => $this->db->checkConnection(),
            'cache' => $this->cache->checkConnection(),
            'redis' => $this->checkRedisConnection(),
            'models' => $this->checkAIModels()
        ];
        
        return [
            'status' => 'healthy',
            'checks' => $checks,
            'timestamp' => time()
        ];
    }

    private function checkRedisConnection() {
        try {
            $redis = new \Redis();
            $redis->connect(REDIS_HOST, REDIS_PORT);
            return [
                'status' => 'healthy',
                'info' => $redis->info()
            ];
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function checkAIModels() {
        $checks = [];
        foreach (AI_MODELS as $model => $config) {
            if ($config['enabled']) {
                $checks[$model] = $this->checkModel($model, $config);
            }
        }
        return $checks;
    }

    private function checkModel($model, $config) {
        try {
            $headers = [
                'Authorization' => 'Bearer ' . $config['api_key']
            ];
            
            $ch = curl_init($config['endpoint']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            
            $response = curl_exec($ch);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                return [
                    'status' => 'unhealthy',
                    'error' => $error
                ];
            }
            
            return [
                'status' => 'healthy',
                'response_time' => curl_getinfo($ch, CURLINFO_TOTAL_TIME)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }
}
