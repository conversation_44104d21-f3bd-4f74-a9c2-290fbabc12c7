#!/bin/bash
#
# WIDDX Stable Build Deployment Script
# Deploys WIDDX theme, Lahza gateway, and order forms to production
#
# @package    WIDDX Production Deployment
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration
WHMCS_PATH="/var/www/html/whmcs"
DEPLOYMENT_PATH="/var/deployment/widdx"
LOG_PATH="/var/log/widdx-deployment"
BACKUP_PATH="/var/backups/whmcs"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Database configuration
DB_HOST=""
DB_NAME=""
DB_USER=""
DB_PASS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/deployment.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/deployment.log"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/deployment.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/deployment.log"
}

# Create directories
mkdir -p "${LOG_PATH}" "${DEPLOYMENT_PATH}"

log "🚀 Starting WIDDX Stable Build Deployment"

# 1. Pre-deployment Validation
log "🔍 Running pre-deployment validation..."

# Check if WHMCS is accessible
if [[ ! -f "${WHMCS_PATH}/init.php" ]]; then
    error "WHMCS installation not found or inaccessible"
fi

# Read database configuration
if [[ ! -f "${WHMCS_PATH}/configuration.php" ]]; then
    error "WHMCS configuration file not found"
fi

DB_HOST=$(grep -oP "(?<=\\\$db_host = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "localhost")
DB_NAME=$(grep -oP "(?<=\\\$db_name = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database name not found")
DB_USER=$(grep -oP "(?<=\\\$db_username = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database user not found")
DB_PASS=$(grep -oP "(?<=\\\$db_password = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database password not found")

# Test database connection
if ! mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "USE ${DB_NAME}; SELECT 1;" >/dev/null 2>&1; then
    error "Cannot connect to database"
fi

log "✅ Pre-deployment validation passed"

# 2. Enable Maintenance Mode
log "🚧 Enabling maintenance mode..."

# Create maintenance page
cat > "${WHMCS_PATH}/maintenance.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Maintenance - WIDDX Upgrade</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
            color: white;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }
        .maintenance-container {
            max-width: 600px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        .message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .progress {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        .progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: progress 3s ease-in-out infinite;
        }
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        .eta {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="logo">WIDDX</div>
        <div class="message">
            We're upgrading our system to serve you better.<br>
            This includes enhanced security, improved performance, and a modern interface.
        </div>
        <div class="progress">
            <div class="progress-bar"></div>
        </div>
        <div class="eta">
            Estimated completion: 15-30 minutes<br>
            Thank you for your patience!
        </div>
    </div>
</body>
</html>
EOF

# Enable maintenance mode in WHMCS
mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
INSERT INTO tblconfiguration (setting, value) VALUES ('MaintenanceMode', 'on') 
ON DUPLICATE KEY UPDATE value = 'on';
"

log "✅ Maintenance mode enabled"

# 3. Create Deployment Checkpoint
log "💾 Creating deployment checkpoint..."

# Quick backup before deployment
mysqldump --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" \
    --single-transaction --quick "${DB_NAME}" | gzip > "${BACKUP_PATH}/pre_deployment_${TIMESTAMP}.sql.gz"

# Backup current templates
tar -czf "${BACKUP_PATH}/templates_pre_deployment_${TIMESTAMP}.tar.gz" \
    -C "${WHMCS_PATH}" templates/ 2>/dev/null || true

log "✅ Deployment checkpoint created"

# 4. Deploy WIDDX Theme
log "🎨 Deploying WIDDX theme..."

# Create theme directory
mkdir -p "${WHMCS_PATH}/templates/WIDDX"

# Deploy theme files (assuming they're in the deployment directory)
if [[ -d "${DEPLOYMENT_PATH}/templates/WIDDX" ]]; then
    rsync -av --checksum "${DEPLOYMENT_PATH}/templates/WIDDX/" "${WHMCS_PATH}/templates/WIDDX/"
else
    # Create theme files from our implementation
    mkdir -p "${WHMCS_PATH}/templates/WIDDX/css"
    mkdir -p "${WHMCS_PATH}/templates/WIDDX/js"
    mkdir -p "${WHMCS_PATH}/templates/WIDDX/includes"
    
    # Copy theme files from our development
    cp -r ../templates/WIDDX/* "${WHMCS_PATH}/templates/WIDDX/" 2>/dev/null || true
fi

# Set proper permissions
chown -R www-data:www-data "${WHMCS_PATH}/templates/WIDDX"
find "${WHMCS_PATH}/templates/WIDDX" -type d -exec chmod 755 {} \;
find "${WHMCS_PATH}/templates/WIDDX" -type f -exec chmod 644 {} \;

log "✅ WIDDX theme deployed"

# 5. Deploy Lahza Payment Gateway
log "💳 Deploying Lahza payment gateway..."

# Deploy main gateway file
if [[ -f "${DEPLOYMENT_PATH}/modules/gateways/lahza.php" ]]; then
    cp "${DEPLOYMENT_PATH}/modules/gateways/lahza.php" "${WHMCS_PATH}/modules/gateways/"
else
    cp ../modules/gateways/lahza.php "${WHMCS_PATH}/modules/gateways/"
fi

# Deploy callback files
mkdir -p "${WHMCS_PATH}/modules/gateways/callback"
if [[ -f "${DEPLOYMENT_PATH}/modules/gateways/callback/lahza.php" ]]; then
    cp "${DEPLOYMENT_PATH}/modules/gateways/callback/lahza.php" "${WHMCS_PATH}/modules/gateways/callback/"
    cp "${DEPLOYMENT_PATH}/modules/gateways/callback/lahza_3ds.php" "${WHMCS_PATH}/modules/gateways/callback/"
else
    cp ../modules/gateways/callback/lahza.php "${WHMCS_PATH}/modules/gateways/callback/"
    cp ../modules/gateways/callback/lahza_3ds.php "${WHMCS_PATH}/modules/gateways/callback/"
fi

# Deploy support files
mkdir -p "${WHMCS_PATH}/modules/gateways/lahza"
if [[ -d "${DEPLOYMENT_PATH}/modules/gateways/lahza" ]]; then
    rsync -av "${DEPLOYMENT_PATH}/modules/gateways/lahza/" "${WHMCS_PATH}/modules/gateways/lahza/"
else
    cp -r ../modules/gateways/lahza/* "${WHMCS_PATH}/modules/gateways/lahza/"
fi

# Create logs directory
mkdir -p "${WHMCS_PATH}/modules/gateways/logs"
chmod 750 "${WHMCS_PATH}/modules/gateways/logs"
chown www-data:www-data "${WHMCS_PATH}/modules/gateways/logs"

# Set proper permissions
chown -R www-data:www-data "${WHMCS_PATH}/modules/gateways/lahza"
chmod 644 "${WHMCS_PATH}/modules/gateways/lahza.php"
chmod 644 "${WHMCS_PATH}/modules/gateways/callback/lahza*.php"

log "✅ Lahza payment gateway deployed"

# 6. Deploy WIDDX Modern Order Form
log "🛒 Deploying WIDDX Modern order form..."

# Create order form directory
mkdir -p "${WHMCS_PATH}/templates/orderforms/widdx_modern"

# Deploy order form files
if [[ -d "${DEPLOYMENT_PATH}/templates/orderforms/widdx_modern" ]]; then
    rsync -av "${DEPLOYMENT_PATH}/templates/orderforms/widdx_modern/" "${WHMCS_PATH}/templates/orderforms/widdx_modern/"
else
    cp -r ../templates/orderforms/widdx_modern/* "${WHMCS_PATH}/templates/orderforms/widdx_modern/"
fi

# Set proper permissions
chown -R www-data:www-data "${WHMCS_PATH}/templates/orderforms/widdx_modern"
find "${WHMCS_PATH}/templates/orderforms/widdx_modern" -type d -exec chmod 755 {} \;
find "${WHMCS_PATH}/templates/orderforms/widdx_modern" -type f -exec chmod 644 {} \;

log "✅ WIDDX Modern order form deployed"

# 7. Update WHMCS Configuration
log "⚙️ Updating WHMCS configuration..."

# Update template setting
mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
UPDATE tblconfiguration SET value = 'WIDDX' WHERE setting = 'Template';
"

# Update order form template
mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
UPDATE tblconfiguration SET value = 'widdx_modern' WHERE setting = 'OrderFormTemplate';
"

# Enable SSL if not already enabled
mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
UPDATE tblconfiguration SET value = 'on' WHERE setting = 'SystemSSLURL';
UPDATE tblconfiguration SET value = 'on' WHERE setting = 'ForceSSL';
"

log "✅ WHMCS configuration updated"

# 8. Configure Lahza Payment Gateway
log "💳 Configuring Lahza payment gateway..."

# Check if Lahza gateway already exists
LAHZA_EXISTS=$(mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
SELECT COUNT(*) FROM tblpaymentgateways WHERE gateway = 'lahza';
" | tail -n 1)

if [[ "${LAHZA_EXISTS}" -eq 0 ]]; then
    # Insert default Lahza configuration
    mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
    USE ${DB_NAME};
    INSERT INTO tblpaymentgateways (gateway, setting, value) VALUES
    ('lahza', 'name', 'Lahza Payment Gateway'),
    ('lahza', 'publicKey', ''),
    ('lahza', 'secretKey', ''),
    ('lahza', 'webhookSecret', ''),
    ('lahza', 'testMode', 'on'),
    ('lahza', 'enable3DS', 'on'),
    ('lahza', '3dsTimeout', '300');
    "
    log "✅ Lahza gateway configuration created (test mode enabled)"
else
    log "✅ Existing Lahza configuration preserved"
fi

# 9. Run Post-Deployment Tests
log "🧪 Running post-deployment validation..."

# Test theme deployment
if [[ ! -f "${WHMCS_PATH}/templates/WIDDX/theme.yaml" ]]; then
    error "WIDDX theme deployment failed - theme.yaml not found"
fi

# Test gateway deployment
if [[ ! -f "${WHMCS_PATH}/modules/gateways/lahza.php" ]]; then
    error "Lahza gateway deployment failed - lahza.php not found"
fi

# Test order form deployment
if [[ ! -f "${WHMCS_PATH}/templates/orderforms/widdx_modern/theme.yaml" ]]; then
    error "WIDDX Modern order form deployment failed - theme.yaml not found"
fi

# Test database configuration
CURRENT_TEMPLATE=$(mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
SELECT value FROM tblconfiguration WHERE setting = 'Template';
" | tail -n 1)

if [[ "${CURRENT_TEMPLATE}" != "WIDDX" ]]; then
    error "Template configuration update failed"
fi

log "✅ Post-deployment validation passed"

# 10. Clear Caches and Optimize
log "🧹 Clearing caches and optimizing..."

# Clear WHMCS caches
rm -rf "${WHMCS_PATH}/storage/cache/*" 2>/dev/null || true
rm -rf "${WHMCS_PATH}/storage/framework/cache/*" 2>/dev/null || true
rm -rf "${WHMCS_PATH}/storage/framework/views/*" 2>/dev/null || true

# Clear compiled templates
rm -rf "${WHMCS_PATH}/storage/compiled/*" 2>/dev/null || true

# Optimize database
mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
OPTIMIZE TABLE tblconfiguration;
OPTIMIZE TABLE tblpaymentgateways;
"

log "✅ Caches cleared and database optimized"

# 11. Disable Maintenance Mode
log "🟢 Disabling maintenance mode..."

# Remove maintenance mode
mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
UPDATE tblconfiguration SET value = 'off' WHERE setting = 'MaintenanceMode';
"

# Remove maintenance page
rm -f "${WHMCS_PATH}/maintenance.html"

log "✅ Maintenance mode disabled"

# 12. Generate Deployment Report
log "📊 Generating deployment report..."

cat > "${LOG_PATH}/deployment_report_${TIMESTAMP}.txt" << EOF
WIDDX Stable Build Deployment Report
===================================
Deployment ID: ${TIMESTAMP}
Completed: $(date)

DEPLOYMENT STATUS: ✅ SUCCESSFUL

Components Deployed:
- ✅ WIDDX Theme: Deployed to templates/WIDDX/
- ✅ Lahza Payment Gateway: Deployed to modules/gateways/
- ✅ WIDDX Modern Order Form: Deployed to templates/orderforms/widdx_modern/
- ✅ Database Configuration: Updated successfully
- ✅ SSL Configuration: Enabled and enforced

Configuration Updates:
- Template: Changed to 'WIDDX'
- Order Form: Changed to 'widdx_modern'
- SSL: Enabled and forced
- Lahza Gateway: $([ ${LAHZA_EXISTS} -eq 0 ] && echo "Configured (test mode)" || echo "Preserved existing configuration")

File Permissions:
- Theme files: 644 (www-data:www-data)
- Gateway files: 644 (www-data:www-data)
- Order form files: 644 (www-data:www-data)
- Log directories: 750 (www-data:www-data)

Post-Deployment Validation: ✅ PASSED
- Theme files: Present and accessible
- Gateway files: Present and accessible
- Order form files: Present and accessible
- Database configuration: Updated correctly
- Cache clearing: Completed successfully

NEXT STEPS:
1. Configure Lahza API credentials in WHMCS admin
2. Test payment processing functionality
3. Verify theme display and responsiveness
4. Run comprehensive system tests
5. Monitor system performance and logs

ROLLBACK INFORMATION:
- Database backup: pre_deployment_${TIMESTAMP}.sql.gz
- Template backup: templates_pre_deployment_${TIMESTAMP}.tar.gz
- Rollback script: Available in deployment directory

SUPPORT:
- Deployment logs: ${LOG_PATH}/deployment.log
- System logs: ${WHMCS_PATH}/storage/logs/
- Gateway logs: ${WHMCS_PATH}/modules/gateways/logs/
EOF

log "✅ Deployment report generated"

# Summary
log "🎉 WIDDX Stable Build Deployment Completed Successfully!"
log ""
log "📋 Deployment Summary:"
log "   - Deployment ID: ${TIMESTAMP}"
log "   - Theme: WIDDX (activated)"
log "   - Payment Gateway: Lahza (deployed)"
log "   - Order Form: WIDDX Modern (activated)"
log "   - SSL: Enabled and enforced"
log "   - Maintenance Mode: Disabled"
log ""
log "📁 Important files:"
log "   - Deployment report: ${LOG_PATH}/deployment_report_${TIMESTAMP}.txt"
log "   - Deployment logs: ${LOG_PATH}/deployment.log"
log "   - Database backup: ${BACKUP_PATH}/pre_deployment_${TIMESTAMP}.sql.gz"
log ""
log "⚠️  Next steps:"
log "   1. Configure Lahza API credentials"
log "   2. Test payment functionality"
log "   3. Verify theme display"
log "   4. Run system validation tests"
log ""
log "🌐 Your WIDDX-powered website is now live!"

info "Stable build deployment completed successfully. Check ${LOG_PATH}/deployment.log for detailed logs."
EOF
