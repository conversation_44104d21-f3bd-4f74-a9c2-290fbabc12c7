<?php
/**
 * WIDDX Master Test Runner
 * Executes all test suites and generates comprehensive report
 * 
 * @package    WIDDX Testing
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @version    1.0.0
 */

// Prevent direct access
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

require_once __DIR__ . '/WIDDXTestSuite.php';
require_once __DIR__ . '/SecurityTestSuite.php';
require_once __DIR__ . '/AccessibilityTestSuite.php';

/**
 * Master test runner class
 */
class WIDDXMasterTestRunner {
    
    private $results = [];
    private $startTime;
    private $totalTests = 0;
    private $totalPassed = 0;
    private $totalFailed = 0;
    
    public function __construct() {
        $this->startTime = microtime(true);
    }
    
    /**
     * Run all test suites
     */
    public function runAllTests() {
        echo "🚀 WIDDX Master Test Runner - Comprehensive System Validation\n";
        echo "=" . str_repeat("=", 80) . "\n";
        echo "Starting comprehensive testing at " . date('Y-m-d H:i:s') . "\n";
        echo "=" . str_repeat("=", 80) . "\n\n";
        
        // Run functional tests
        $this->runFunctionalTests();
        
        // Run security tests
        $this->runSecurityTests();
        
        // Run accessibility tests
        $this->runAccessibilityTests();
        
        // Generate master report
        $this->generateMasterReport();
        
        // Determine deployment readiness
        $this->assessDeploymentReadiness();
    }
    
    /**
     * Run functional test suite
     */
    private function runFunctionalTests() {
        echo "🧪 PHASE 1: FUNCTIONAL TESTING\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        try {
            ob_start();
            $functionalSuite = new WIDDXTestSuite();
            $functionalSuite->runAllTests();
            $output = ob_get_clean();
            
            // Parse results from output
            $this->parseFunctionalResults($output);
            
            echo $output;
            
        } catch (Exception $e) {
            echo "❌ Functional testing failed: " . $e->getMessage() . "\n";
            $this->results['functional'] = [
                'status' => 'FAILED',
                'error' => $e->getMessage(),
                'tests' => 0,
                'passed' => 0,
                'failed' => 1
            ];
        }
        
        echo "\n" . str_repeat("-", 80) . "\n\n";
    }
    
    /**
     * Run security test suite
     */
    private function runSecurityTests() {
        echo "🛡️ PHASE 2: SECURITY TESTING\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        try {
            ob_start();
            $securitySuite = new SecurityTestSuite();
            $securitySuite->runSecurityTests();
            $output = ob_get_clean();
            
            // Parse results from output
            $this->parseSecurityResults($output);
            
            echo $output;
            
        } catch (Exception $e) {
            echo "❌ Security testing failed: " . $e->getMessage() . "\n";
            $this->results['security'] = [
                'status' => 'FAILED',
                'error' => $e->getMessage(),
                'tests' => 0,
                'passed' => 0,
                'failed' => 1,
                'vulnerabilities' => 1
            ];
        }
        
        echo "\n" . str_repeat("-", 80) . "\n\n";
    }
    
    /**
     * Run accessibility test suite
     */
    private function runAccessibilityTests() {
        echo "♿ PHASE 3: ACCESSIBILITY TESTING\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        try {
            ob_start();
            $accessibilitySuite = new AccessibilityTestSuite();
            $accessibilitySuite->runAccessibilityTests();
            $output = ob_get_clean();
            
            // Parse results from output
            $this->parseAccessibilityResults($output);
            
            echo $output;
            
        } catch (Exception $e) {
            echo "❌ Accessibility testing failed: " . $e->getMessage() . "\n";
            $this->results['accessibility'] = [
                'status' => 'FAILED',
                'error' => $e->getMessage(),
                'tests' => 0,
                'passed' => 0,
                'failed' => 1,
                'issues' => 1
            ];
        }
        
        echo "\n" . str_repeat("-", 80) . "\n\n";
    }
    
    /**
     * Parse functional test results
     */
    private function parseFunctionalResults($output) {
        // Extract test counts from output
        if (preg_match('/Total Tests: (\d+)/', $output, $matches)) {
            $total = intval($matches[1]);
        } else {
            $total = 0;
        }
        
        if (preg_match('/Passed: (\d+)/', $output, $matches)) {
            $passed = intval($matches[1]);
        } else {
            $passed = 0;
        }
        
        if (preg_match('/Failed: (\d+)/', $output, $matches)) {
            $failed = intval($matches[1]);
        } else {
            $failed = 0;
        }
        
        $this->results['functional'] = [
            'status' => $failed === 0 ? 'PASSED' : 'FAILED',
            'tests' => $total,
            'passed' => $passed,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($passed / $total) * 100, 2) : 0
        ];
        
        $this->totalTests += $total;
        $this->totalPassed += $passed;
        $this->totalFailed += $failed;
    }
    
    /**
     * Parse security test results
     */
    private function parseSecurityResults($output) {
        // Extract security test counts
        if (preg_match('/Security Tests: (\d+)/', $output, $matches)) {
            $total = intval($matches[1]);
        } else {
            $total = 0;
        }
        
        if (preg_match('/Passed: (\d+)/', $output, $matches)) {
            $passed = intval($matches[1]);
        } else {
            $passed = 0;
        }
        
        if (preg_match('/Failed: (\d+)/', $output, $matches)) {
            $failed = intval($matches[1]);
        } else {
            $failed = 0;
        }
        
        if (preg_match('/Vulnerabilities Found: (\d+)/', $output, $matches)) {
            $vulnerabilities = intval($matches[1]);
        } else {
            $vulnerabilities = 0;
        }
        
        if (preg_match('/Security Score: ([\d.]+)/', $output, $matches)) {
            $score = floatval($matches[1]);
        } else {
            $score = 0;
        }
        
        $this->results['security'] = [
            'status' => $failed === 0 && $vulnerabilities === 0 ? 'PASSED' : 'FAILED',
            'tests' => $total,
            'passed' => $passed,
            'failed' => $failed,
            'vulnerabilities' => $vulnerabilities,
            'security_score' => $score
        ];
        
        $this->totalTests += $total;
        $this->totalPassed += $passed;
        $this->totalFailed += $failed;
    }
    
    /**
     * Parse accessibility test results
     */
    private function parseAccessibilityResults($output) {
        // Extract accessibility test counts
        if (preg_match('/Accessibility Tests: (\d+)/', $output, $matches)) {
            $total = intval($matches[1]);
        } else {
            $total = 0;
        }
        
        if (preg_match('/Passed: (\d+)/', $output, $matches)) {
            $passed = intval($matches[1]);
        } else {
            $passed = 0;
        }
        
        if (preg_match('/Failed: (\d+)/', $output, $matches)) {
            $failed = intval($matches[1]);
        } else {
            $failed = 0;
        }
        
        if (preg_match('/Issues Found: (\d+)/', $output, $matches)) {
            $issues = intval($matches[1]);
        } else {
            $issues = 0;
        }
        
        if (preg_match('/WCAG 2\.1 Compliance Score: ([\d.]+)/', $output, $matches)) {
            $score = floatval($matches[1]);
        } else {
            $score = 0;
        }
        
        $this->results['accessibility'] = [
            'status' => $failed === 0 && $issues === 0 ? 'PASSED' : 'FAILED',
            'tests' => $total,
            'passed' => $passed,
            'failed' => $failed,
            'issues' => $issues,
            'wcag_score' => $score,
            'compliance_level' => $score >= 85 ? 'AA' : ($score >= 70 ? 'A' : 'Non-compliant')
        ];
        
        $this->totalTests += $total;
        $this->totalPassed += $passed;
        $this->totalFailed += $failed;
    }
    
    /**
     * Generate master test report
     */
    private function generateMasterReport() {
        $duration = microtime(true) - $this->startTime;
        
        echo "📊 MASTER TEST REPORT\n";
        echo "=" . str_repeat("=", 80) . "\n";
        
        echo "Test Execution Summary:\n";
        echo "  Duration: " . round($duration, 2) . " seconds\n";
        echo "  Total Tests: {$this->totalTests}\n";
        echo "  Total Passed: {$this->totalPassed}\n";
        echo "  Total Failed: {$this->totalFailed}\n";
        echo "  Overall Success Rate: " . round(($this->totalPassed / max($this->totalTests, 1)) * 100, 2) . "%\n\n";
        
        // Individual suite results
        echo "Test Suite Results:\n";
        
        foreach ($this->results as $suite => $result) {
            $status = $result['status'];
            $statusIcon = $status === 'PASSED' ? '✅' : '❌';
            
            echo "  {$statusIcon} " . ucfirst($suite) . " Tests: {$status}\n";
            echo "    Tests: {$result['tests']} | Passed: {$result['passed']} | Failed: {$result['failed']}\n";
            
            if (isset($result['success_rate'])) {
                echo "    Success Rate: {$result['success_rate']}%\n";
            }
            if (isset($result['security_score'])) {
                echo "    Security Score: {$result['security_score']}/100\n";
            }
            if (isset($result['wcag_score'])) {
                echo "    WCAG Score: {$result['wcag_score']}/100 ({$result['compliance_level']})\n";
            }
            echo "\n";
        }
        
        // Save detailed report
        $reportData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'duration' => round($duration, 2),
            'summary' => [
                'total_tests' => $this->totalTests,
                'total_passed' => $this->totalPassed,
                'total_failed' => $this->totalFailed,
                'success_rate' => round(($this->totalPassed / max($this->totalTests, 1)) * 100, 2)
            ],
            'suites' => $this->results
        ];
        
        $reportFile = __DIR__ . '/reports/master_report_' . date('Y-m-d_H-i-s') . '.json';
        @mkdir(dirname($reportFile), 0755, true);
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT));
        
        echo "📄 Master report saved to: {$reportFile}\n\n";
    }
    
    /**
     * Assess deployment readiness
     */
    private function assessDeploymentReadiness() {
        echo "🎯 DEPLOYMENT READINESS ASSESSMENT\n";
        echo "=" . str_repeat("=", 80) . "\n";
        
        $readinessScore = 0;
        $maxScore = 0;
        $criticalIssues = [];
        $warnings = [];
        
        // Functional tests assessment
        if (isset($this->results['functional'])) {
            $functional = $this->results['functional'];
            $maxScore += 40;
            
            if ($functional['status'] === 'PASSED') {
                $readinessScore += 40;
                echo "✅ Functional Tests: READY FOR DEPLOYMENT\n";
            } else {
                $criticalIssues[] = "Functional tests failed ({$functional['failed']} failures)";
                echo "❌ Functional Tests: CRITICAL ISSUES FOUND\n";
            }
        }
        
        // Security tests assessment
        if (isset($this->results['security'])) {
            $security = $this->results['security'];
            $maxScore += 35;
            
            if ($security['security_score'] >= 90) {
                $readinessScore += 35;
                echo "✅ Security Tests: READY FOR DEPLOYMENT\n";
            } elseif ($security['security_score'] >= 75) {
                $readinessScore += 25;
                $warnings[] = "Security score below 90% ({$security['security_score']}%)";
                echo "⚠️ Security Tests: ACCEPTABLE WITH WARNINGS\n";
            } else {
                $criticalIssues[] = "Security score too low ({$security['security_score']}%)";
                echo "❌ Security Tests: CRITICAL SECURITY ISSUES\n";
            }
        }
        
        // Accessibility tests assessment
        if (isset($this->results['accessibility'])) {
            $accessibility = $this->results['accessibility'];
            $maxScore += 25;
            
            if ($accessibility['wcag_score'] >= 85) {
                $readinessScore += 25;
                echo "✅ Accessibility Tests: WCAG 2.1 AA COMPLIANT\n";
            } elseif ($accessibility['wcag_score'] >= 70) {
                $readinessScore += 15;
                $warnings[] = "WCAG compliance below AA level ({$accessibility['compliance_level']})";
                echo "⚠️ Accessibility Tests: WCAG 2.1 A COMPLIANT\n";
            } else {
                $criticalIssues[] = "WCAG compliance insufficient ({$accessibility['wcag_score']}%)";
                echo "❌ Accessibility Tests: NON-COMPLIANT\n";
            }
        }
        
        // Calculate final readiness score
        $finalScore = round(($readinessScore / max($maxScore, 1)) * 100, 1);
        
        echo "\nDeployment Readiness Score: {$finalScore}/100\n\n";
        
        // Final recommendation
        if (empty($criticalIssues) && $finalScore >= 90) {
            echo "🟢 DEPLOYMENT RECOMMENDATION: APPROVED FOR PRODUCTION\n";
            echo "   System meets all requirements for production deployment.\n";
        } elseif (empty($criticalIssues) && $finalScore >= 75) {
            echo "🟡 DEPLOYMENT RECOMMENDATION: APPROVED WITH CONDITIONS\n";
            echo "   System is acceptable for deployment with monitoring.\n";
            if (!empty($warnings)) {
                echo "   Warnings to address:\n";
                foreach ($warnings as $warning) {
                    echo "   - {$warning}\n";
                }
            }
        } else {
            echo "🔴 DEPLOYMENT RECOMMENDATION: NOT APPROVED\n";
            echo "   Critical issues must be resolved before deployment.\n";
            if (!empty($criticalIssues)) {
                echo "   Critical issues:\n";
                foreach ($criticalIssues as $issue) {
                    echo "   - {$issue}\n";
                }
            }
        }
        
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "Testing completed at " . date('Y-m-d H:i:s') . "\n";
        echo "Total execution time: " . round(microtime(true) - $this->startTime, 2) . " seconds\n";
        echo str_repeat("=", 80) . "\n";
    }
}

// Run all tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $masterRunner = new WIDDXMasterTestRunner();
    $masterRunner->runAllTests();
}
