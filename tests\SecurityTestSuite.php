<?php
/**
 * WIDDX Security Test Suite
 * Comprehensive security testing and vulnerability assessment
 * 
 * @package    WIDDX Security Testing
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @version    1.0.0
 */

require_once __DIR__ . '/../modules/gateways/lahza/Logger.php';

/**
 * Security-focused test suite
 */
class SecurityTestSuite {
    
    private $logger;
    private $vulnerabilities = [];
    private $testCount = 0;
    private $passCount = 0;
    private $failCount = 0;
    
    public function __construct() {
        $this->logger = new LahzaLogger();
    }
    
    /**
     * Run all security tests
     */
    public function runSecurityTests() {
        echo "🛡️ WIDDX Security Test Suite - Vulnerability Assessment\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
        
        $this->testInputValidation();
        $this->testSQLInjection();
        $this->testXSSPrevention();
        $this->testCSRFProtection();
        $this->testFileInclusion();
        $this->testAuthenticationSecurity();
        $this->testDataSanitization();
        $this->testRateLimiting();
        $this->testCryptographicSecurity();
        $this->testSessionSecurity();
        
        $this->generateSecurityReport();
    }
    
    /**
     * Test input validation security
     */
    private function testInputValidation() {
        echo "🔍 Testing Input Validation Security\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        // Test SQL injection attempts in input validation
        $this->securityTest('SQL Injection in Invoice ID', function() {
            $maliciousInputs = [
                "1'; DROP TABLE tblinvoices; --",
                "1 UNION SELECT * FROM tblclients",
                "1' OR '1'='1",
                "'; EXEC xp_cmdshell('dir'); --"
            ];
            
            foreach ($maliciousInputs as $input) {
                try {
                    lahza_validateInput([
                        'invoiceid' => $input,
                        'amount' => '100.00',
                        'currency' => 'USD',
                        'clientdetails' => ['email' => '<EMAIL>']
                    ]);
                    throw new Exception("SQL injection attempt not blocked: {$input}");
                } catch (InvalidArgumentException $e) {
                    // Expected behavior - input should be rejected
                }
            }
            
            return true;
        });
        
        // Test XSS attempts in input validation
        $this->securityTest('XSS Prevention in Input', function() {
            $xssPayloads = [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>",
                "';alert('XSS');//"
            ];
            
            foreach ($xssPayloads as $payload) {
                try {
                    lahza_validateInput([
                        'invoiceid' => '12345',
                        'amount' => '100.00',
                        'currency' => 'USD',
                        'clientdetails' => ['email' => $payload]
                    ]);
                    throw new Exception("XSS payload not blocked: {$payload}");
                } catch (InvalidArgumentException $e) {
                    // Expected behavior - malicious input should be rejected
                }
            }
            
            return true;
        });
        
        // Test buffer overflow attempts
        $this->securityTest('Buffer Overflow Protection', function() {
            $longString = str_repeat('A', 10000);
            
            try {
                lahza_validateInput([
                    'invoiceid' => $longString,
                    'amount' => '100.00',
                    'currency' => 'USD',
                    'clientdetails' => ['email' => '<EMAIL>']
                ]);
                throw new Exception("Buffer overflow attempt not blocked");
            } catch (InvalidArgumentException $e) {
                // Expected behavior
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test SQL injection prevention
     */
    private function testSQLInjection() {
        echo "💉 Testing SQL Injection Prevention\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Parameterized Queries', function() {
            // Check that all database queries use parameterized statements
            $gatewayFile = file_get_contents(__DIR__ . '/../modules/gateways/lahza.php');
            $callbackFile = file_get_contents(__DIR__ . '/../modules/gateways/callback/lahza.php');
            
            // Look for dangerous patterns
            $dangerousPatterns = [
                '/\$.*query.*=.*["\'].*\$.*["\']/',  // String concatenation in queries
                '/mysql_query\s*\(/',                // Old MySQL functions
                '/mysqli_query\s*\([^,]*,\s*["\'].*\$/', // Direct variable insertion
            ];
            
            foreach ($dangerousPatterns as $pattern) {
                if (preg_match($pattern, $gatewayFile) || preg_match($pattern, $callbackFile)) {
                    throw new Exception("Potential SQL injection vulnerability found");
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test XSS prevention
     */
    private function testXSSPrevention() {
        echo "🕷️ Testing XSS Prevention\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Template Output Escaping', function() {
            $templateFiles = glob(__DIR__ . '/../templates/WIDDX/*.tpl');
            $templateFiles = array_merge($templateFiles, glob(__DIR__ . '/../templates/WIDDX/**/*.tpl'));
            
            foreach ($templateFiles as $file) {
                $content = file_get_contents($file);
                
                // Check for unescaped output
                if (preg_match('/\{\$[^}]+\}(?!\|escape)/', $content)) {
                    // Allow some exceptions for known safe outputs
                    $safeExceptions = ['WEB_ROOT', 'template', 'systemurl'];
                    $matches = [];
                    preg_match_all('/\{\$([^}|]+)\}/', $content, $matches);
                    
                    foreach ($matches[1] as $variable) {
                        if (!in_array($variable, $safeExceptions)) {
                            $this->vulnerabilities[] = "Potential XSS in {$file}: unescaped variable \${$variable}";
                        }
                    }
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test CSRF protection
     */
    private function testCSRFProtection() {
        echo "🎭 Testing CSRF Protection\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('CSRF Token Validation', function() {
            // Check that forms include CSRF tokens
            $orderFormFile = __DIR__ . '/../templates/orderforms/widdx_modern/products.tpl';
            if (file_exists($orderFormFile)) {
                $content = file_get_contents($orderFormFile);
                
                // Look for CSRF token usage
                if (strpos($content, 'csrfToken') === false && strpos($content, '{$token}') === false) {
                    throw new Exception("CSRF protection not found in order form");
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test file inclusion vulnerabilities
     */
    private function testFileInclusion() {
        echo "📁 Testing File Inclusion Security\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Local File Inclusion Prevention', function() {
            $phpFiles = array_merge(
                glob(__DIR__ . '/../modules/gateways/*.php'),
                glob(__DIR__ . '/../modules/gateways/**/*.php')
            );
            
            foreach ($phpFiles as $file) {
                $content = file_get_contents($file);
                
                // Check for dangerous include/require patterns
                if (preg_match('/(?:include|require)(?:_once)?\s*\(\s*\$/', $content)) {
                    // Check if there's proper validation
                    if (strpos($content, 'realpath') === false && strpos($content, 'basename') === false) {
                        $this->vulnerabilities[] = "Potential LFI vulnerability in {$file}";
                    }
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test authentication security
     */
    private function testAuthenticationSecurity() {
        echo "🔐 Testing Authentication Security\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Signature Verification Strength', function() {
            // Test weak signature attempts
            $weakSignatures = [
                '',
                'weak',
                '123456',
                md5('test'),  // MD5 is weak
            ];
            
            $testData = ['amount' => 100, 'currency' => 'USD'];
            $strongSecret = 'strong_secret_key_with_sufficient_entropy';
            
            foreach ($weakSignatures as $weakSig) {
                $result = lahza_verifySignature($testData, $weakSig, $strongSecret);
                if ($result) {
                    throw new Exception("Weak signature accepted: {$weakSig}");
                }
            }
            
            return true;
        });
        
        $this->securityTest('Timing Attack Prevention', function() {
            $testData = ['amount' => 100];
            $secret = 'test_secret';
            $validSignature = hash_hmac('sha256', json_encode($testData), $secret);
            $invalidSignature = 'invalid_signature_of_same_length_as_valid_one';
            
            // Measure timing for valid signature
            $start = microtime(true);
            lahza_verifySignature($testData, $validSignature, $secret);
            $validTime = microtime(true) - $start;
            
            // Measure timing for invalid signature
            $start = microtime(true);
            lahza_verifySignature($testData, $invalidSignature, $secret);
            $invalidTime = microtime(true) - $start;
            
            // Times should be similar (within 10ms) to prevent timing attacks
            $timeDiff = abs($validTime - $invalidTime);
            if ($timeDiff > 0.01) {
                $this->vulnerabilities[] = "Potential timing attack vulnerability: {$timeDiff}s difference";
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test data sanitization
     */
    private function testDataSanitization() {
        echo "🧹 Testing Data Sanitization\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Sensitive Data Redaction', function() {
            $logger = new LahzaLogger();
            
            // Test that sensitive data is properly redacted in logs
            $sensitiveData = [
                'secretKey' => 'sk_test_sensitive_key',
                'publicKey' => 'pk_test_public_key',
                'credit_card' => '****************',
                'cvv' => '123',
                'password' => 'user_password'
            ];
            
            $logger->info('Test log with sensitive data', $sensitiveData, 'security_test');
            
            // Check the log file to ensure data is redacted
            $logFile = __DIR__ . '/../modules/gateways/logs/lahza_security_test.log';
            if (file_exists($logFile)) {
                $logContent = file_get_contents($logFile);
                
                // These should be redacted
                if (strpos($logContent, 'sk_test_sensitive_key') !== false) {
                    throw new Exception('Secret key not redacted in logs');
                }
                if (strpos($logContent, '****************') !== false) {
                    throw new Exception('Credit card number not redacted in logs');
                }
                if (strpos($logContent, 'user_password') !== false) {
                    throw new Exception('Password not redacted in logs');
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test rate limiting security
     */
    private function testRateLimiting() {
        echo "🚦 Testing Rate Limiting\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Rate Limiting Effectiveness', function() {
            $testIdentifier = 'security_test_' . time();
            
            // Make requests up to the limit
            for ($i = 0; $i < 60; $i++) {
                $result = lahza_checkRateLimit($testIdentifier);
                if (!$result && $i < 59) {
                    throw new Exception("Rate limiting triggered too early at request {$i}");
                }
            }
            
            // Next request should be blocked
            $result = lahza_checkRateLimit($testIdentifier);
            if ($result) {
                throw new Exception('Rate limiting not enforced after limit exceeded');
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test cryptographic security
     */
    private function testCryptographicSecurity() {
        echo "🔒 Testing Cryptographic Security\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Strong Cryptographic Algorithms', function() {
            // Check that strong algorithms are used
            if (LAHZA_SIGNATURE_ALGORITHM !== 'sha256') {
                throw new Exception('Weak signature algorithm in use');
            }
            
            // Test entropy of generated IDs
            $ids = [];
            for ($i = 0; $i < 100; $i++) {
                $manager = new LahzaTransactionManager([], $this->logger);
                $id = $manager->createTransaction('test', '100', 'USD');
                $ids[] = $id;
            }
            
            // Check for duplicates (should be extremely rare)
            if (count($ids) !== count(array_unique($ids))) {
                throw new Exception('Duplicate transaction IDs generated - insufficient entropy');
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test session security
     */
    private function testSessionSecurity() {
        echo "🍪 Testing Session Security\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->securityTest('Session Configuration', function() {
            // Check PHP session security settings
            $secureSettings = [
                'session.cookie_httponly' => '1',
                'session.cookie_secure' => '1',
                'session.use_strict_mode' => '1'
            ];
            
            foreach ($secureSettings as $setting => $expectedValue) {
                $currentValue = ini_get($setting);
                if ($currentValue !== $expectedValue) {
                    $this->vulnerabilities[] = "Insecure session setting: {$setting} = {$currentValue} (should be {$expectedValue})";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Run individual security test
     */
    private function securityTest($name, $callback) {
        $this->testCount++;
        
        try {
            $result = $callback();
            if ($result) {
                echo "✅ {$name}\n";
                $this->passCount++;
            } else {
                echo "❌ {$name} - Test returned false\n";
                $this->failCount++;
            }
        } catch (Exception $e) {
            echo "❌ {$name} - {$e->getMessage()}\n";
            $this->failCount++;
            $this->vulnerabilities[] = "{$name}: {$e->getMessage()}";
        }
    }
    
    /**
     * Generate security report
     */
    private function generateSecurityReport() {
        echo "\n" . "=" . str_repeat("=", 60) . "\n";
        echo "🛡️ Security Assessment Report\n";
        echo "=" . str_repeat("=", 60) . "\n";
        
        echo "Security Tests: {$this->testCount}\n";
        echo "Passed: {$this->passCount}\n";
        echo "Failed: {$this->failCount}\n";
        echo "Vulnerabilities Found: " . count($this->vulnerabilities) . "\n\n";
        
        if (!empty($this->vulnerabilities)) {
            echo "🚨 Security Issues Found:\n";
            foreach ($this->vulnerabilities as $vuln) {
                echo "  - {$vuln}\n";
            }
            echo "\n";
        }
        
        // Security score
        $securityScore = ($this->passCount / $this->testCount) * 100;
        $vulnerabilityPenalty = count($this->vulnerabilities) * 5;
        $finalScore = max(0, $securityScore - $vulnerabilityPenalty);
        
        echo "Security Score: " . round($finalScore, 1) . "/100\n";
        
        if ($finalScore >= 90) {
            echo "🟢 Security Status: EXCELLENT\n";
        } elseif ($finalScore >= 75) {
            echo "🟡 Security Status: GOOD\n";
        } elseif ($finalScore >= 60) {
            echo "🟠 Security Status: FAIR - Improvements needed\n";
        } else {
            echo "🔴 Security Status: POOR - Critical issues must be addressed\n";
        }
        
        // Save security report
        $reportData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'security_score' => round($finalScore, 1),
            'tests_passed' => $this->passCount,
            'tests_failed' => $this->failCount,
            'vulnerabilities' => $this->vulnerabilities
        ];
        
        $reportFile = __DIR__ . '/reports/security_report_' . date('Y-m-d_H-i-s') . '.json';
        @mkdir(dirname($reportFile), 0755, true);
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT));
        
        echo "\n📄 Security report saved to: {$reportFile}\n";
    }
}

// Run security tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $securitySuite = new SecurityTestSuite();
    $securitySuite->runSecurityTests();
}
