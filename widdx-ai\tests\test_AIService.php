<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Database.php';
require_once __DIR__ . '/../includes/AIService.php';
require_once __DIR__ . '/../includes/Cache.php';
require_once __DIR__ . '/../includes/ErrorHandler.php';

use PHPUnit\Framework\TestCase;

class AIServiceTest extends TestCase {
    private $aiService;
    private $db;
    private $cache;

    protected function setUp(): void {
        $this->db = new Database();
        $this->cache = new Cache();
        $this->aiService = new AIService();
    }

    protected function tearDown(): void {
        // Clear test data
        $this->db->getConnection()->query("DELETE FROM questions WHERE question_text LIKE '%test_%'");
        $this->cache->clear();
    }

    public function testProcessQuestion() {
        // Test with a new question
        $question = "test_What is artificial intelligence?";
        $response = $this->aiService->processQuestion($question);
        
        $this->assertArrayHasKey('answer', $response);
        $this->assertArrayHasKey('source', $response);
        $this->assertArrayHasKey('cached', $response);
        $this->assertFalse($response['cached']);

        // Test with the same question again (should be cached)
        $response2 = $this->aiService->processQuestion($question);
        $this->assertTrue($response2['cached']);
    }

    public function testFindSimilarQuestion() {
        // Insert test data
        $stmt = $this->db->getConnection()->prepare("
            INSERT INTO questions (question_text, answer_text, source_model)
            VALUES (:question, :answer, :source)
        ");
        
        $stmt->execute([
            ':question' => 'test_What is AI?',
            ':answer' => 'Artificial Intelligence is...',
            ':source' => 'DEEPSEEK'
        ]);

        // Test similar question
        $result = $this->aiService->findSimilarQuestion('test_What is artificial intelligence?');
        $this->assertNotNull($result);
        $this->assertEquals('Artificial Intelligence is...', $result['answer_text']);
    }

    public function testQueryAIProviders() {
        $responses = $this->aiService->queryAIProviders('test_Hello');
        $this->assertIsArray($responses);
        $this->assertContainsOnly('array', $responses);
        
        foreach ($responses as $response) {
            $this->assertArrayHasKey('answer', $response);
            $this->assertArrayHasKey('source', $response);
        }
    }

    public function testChooseBestResponse() {
        $responses = [
            ['answer' => 'Short answer', 'source' => 'DEEPSEEK'],
            ['answer' => 'This is a much longer answer that provides more detailed information about the topic.', 'source' => 'GEMINI']
        ];

        $best = $this->aiService->chooseBestResponse($responses);
        $this->assertEquals('GEMINI', $best['source']);
    }

    public function testStoreQA() {
        $result = $this->aiService->storeQA(
            'test_Test question',
            'Test answer',
            'DEEPSEEK'
        );

        $this->assertIsInt($result);
        $this->assertGreaterThan(0, $result);
    }

    public function testErrorHandling() {
        // Test invalid question
        $this->expectException(Exception::class);
        $this->aiService->processQuestion('');
    }

    public function testRateLimiting() {
        // Test rate limiting by making multiple requests quickly
        for ($i = 0; $i < MAX_REQUESTS_PER_MINUTE + 1; $i++) {
            try {
                $this->aiService->processQuestion("test_Rate limit test $i");
            } catch (Exception $e) {
                if ($i >= MAX_REQUESTS_PER_MINUTE) {
                    $this->assertEquals('Too many requests. Please try again later.', $e->getMessage());
                } else {
                    throw $e;
                }
            }
        }
    }

    public function testCacheFunctionality() {
        // Test cache storage
        $this->cache->set('test_key', 'test_value');
        $this->assertEquals('test_value', $this->cache->get('test_key'));

        // Test cache expiration
        $this->cache->set('test_expire', 'test_value', 1);
        sleep(2);
        $this->assertNull($this->cache->get('test_expire'));
    }
}

// Run tests
$test = new AIServiceTest();
$test->run();
