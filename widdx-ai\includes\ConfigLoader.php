<?php
class ConfigLoader {
    private static $loaded = false;
    
    public static function load() {
        if (self::$loaded) {
            return;
        }
        
        // Load database configuration
        require __DIR__ . '/../config/database_config.php';
        
        // Load security configuration
        require __DIR__ . '/../config/security_config.php';
        
        // Load API configuration
        require __DIR__ . '/../config/api_config.php';
        
        // Load WebSocket configuration
        require __DIR__ . '/../config/ws_config.php';
        
        // Load AI configuration
        require __DIR__ . '/../config/ai_config.php';
        
        // Load logging configuration
        require __DIR__ . '/../config/logging.php';
        
        // Load cache configuration
        require __DIR__ . '/../config/cache.php';
        
        self::$loaded = true;
    }
}
