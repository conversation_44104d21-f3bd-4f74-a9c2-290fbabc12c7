{include file="orderforms/widdx_modern/common.tpl"}

<div class="widdx-order-form">
    <!-- Progress Indicator -->
    <div class="widdx-progress-container mb-4">
        <div class="widdx-progress-steps">
            <div class="widdx-step active" data-step="1">
                <div class="widdx-step-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="widdx-step-label">Choose Products</div>
            </div>
            <div class="widdx-step" data-step="2">
                <div class="widdx-step-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="widdx-step-label">Configure</div>
            </div>
            <div class="widdx-step" data-step="3">
                <div class="widdx-step-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="widdx-step-label">Payment</div>
            </div>
            <div class="widdx-step" data-step="4">
                <div class="widdx-step-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="widdx-step-label">Complete</div>
            </div>
        </div>
        <div class="widdx-progress-bar">
            <div class="widdx-progress-fill" style="width: 25%"></div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4">
                <div class="widdx-sidebar">
                    {include file="orderforms/widdx_modern/sidebar-categories.tpl"}
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <div class="widdx-main-content">
                    <!-- Hero Section -->
                    <div class="widdx-hero-section mb-5">
                        <div class="widdx-hero-content">
                            <h1 class="widdx-hero-title">Choose Your Perfect Hosting Solution</h1>
                            <p class="widdx-hero-subtitle">Professional hosting services with modern payment processing and 24/7 support</p>
                        </div>
                        <div class="widdx-hero-graphic">
                            <i class="fas fa-server fa-4x"></i>
                        </div>
                    </div>

                    <!-- Product Groups -->
                    {if $productGroups}
                        <div class="widdx-product-groups">
                            {foreach $productGroups as $productGroup}
                                <div class="widdx-product-group mb-5" id="group-{$productGroup->id}">
                                    <div class="widdx-group-header">
                                        <h2 class="widdx-group-title">{$productGroup->name}</h2>
                                        {if $productGroup->tagline}
                                            <p class="widdx-group-tagline">{$productGroup->tagline}</p>
                                        {/if}
                                    </div>

                                    <div class="widdx-products-grid">
                                        {foreach $productGroup->products as $product}
                                            <div class="widdx-product-card" data-product-id="{$product->id}">
                                                <div class="widdx-product-header">
                                                    {if $product->featureHighlight}
                                                        <div class="widdx-product-badge">
                                                            {$product->featureHighlight}
                                                        </div>
                                                    {/if}
                                                    <h3 class="widdx-product-name">{$product->name}</h3>
                                                    {if $product->tagLine}
                                                        <p class="widdx-product-tagline">{$product->tagLine}</p>
                                                    {/if}
                                                </div>

                                                <div class="widdx-product-pricing">
                                                    {if $product->bid}
                                                        <div class="widdx-price-container">
                                                            <span class="widdx-price-amount">{$product->pricing->minPrice->price}</span>
                                                            <span class="widdx-price-cycle">/{$product->pricing->minPrice->cycle}</span>
                                                        </div>
                                                        {if $product->pricing->minPrice->setupFee->toNumeric() > 0}
                                                            <div class="widdx-setup-fee">
                                                                Setup: {$product->pricing->minPrice->setupFee}
                                                            </div>
                                                        {/if}
                                                    {else}
                                                        <div class="widdx-price-container">
                                                            <span class="widdx-price-amount">Free</span>
                                                        </div>
                                                    {/if}
                                                </div>

                                                <div class="widdx-product-features">
                                                    {if $product->features}
                                                        <ul class="widdx-feature-list">
                                                            {foreach $product->features as $feature}
                                                                <li class="widdx-feature-item">
                                                                    <i class="fas fa-check text-success"></i>
                                                                    {$feature->feature}
                                                                </li>
                                                            {/foreach}
                                                        </ul>
                                                    {/if}
                                                </div>

                                                <div class="widdx-product-actions">
                                                    {if $product->isFree}
                                                        <a href="{$product->productUrl}" class="btn btn-widdx-primary btn-block">
                                                            <i class="fas fa-download"></i>
                                                            Get Started Free
                                                        </a>
                                                    {else}
                                                        <a href="{$product->productUrl}" class="btn btn-widdx-primary btn-block">
                                                            <i class="fas fa-shopping-cart"></i>
                                                            Order Now
                                                        </a>
                                                    {/if}
                                                    {if $product->description}
                                                        <button type="button" class="btn btn-outline-secondary btn-sm mt-2" data-toggle="modal" data-target="#product-details-{$product->id}">
                                                            <i class="fas fa-info-circle"></i>
                                                            More Details
                                                        </button>
                                                    {/if}
                                                </div>
                                            </div>

                                            <!-- Product Details Modal -->
                                            {if $product->description}
                                                <div class="modal fade" id="product-details-{$product->id}" tabindex="-1" role="dialog" aria-labelledby="product-details-{$product->id}-title" aria-hidden="true">
                                                    <div class="modal-dialog modal-lg" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="product-details-{$product->id}-title">{$product->name}</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                {$product->description}
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                <a href="{$product->productUrl}" class="btn btn-widdx-primary">Order Now</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {/if}
                                        {/foreach}
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    {/if}

                    <!-- Domain Registration Section -->
                    {if $registerdomainenabled || $transferdomainenabled}
                        <div class="widdx-domain-section mt-5">
                            <div class="widdx-section-header text-center mb-4">
                                <h2 class="widdx-section-title">Domain Services</h2>
                                <p class="widdx-section-subtitle">Secure your perfect domain name</p>
                            </div>

                            <div class="row">
                                {if $registerdomainenabled}
                                    <div class="col-md-6 mb-4">
                                        <div class="widdx-domain-card">
                                            <div class="widdx-domain-icon">
                                                <i class="fas fa-globe fa-3x"></i>
                                            </div>
                                            <h3>Register Domain</h3>
                                            <p>Find and register your perfect domain name</p>
                                            <a href="{$WEB_ROOT}/cart.php?a=add&domain=register" class="btn btn-success btn-block">
                                                <i class="fas fa-search"></i>
                                                Search Domains
                                            </a>
                                        </div>
                                    </div>
                                {/if}
                                {if $transferdomainenabled}
                                    <div class="col-md-6 mb-4">
                                        <div class="widdx-domain-card">
                                            <div class="widdx-domain-icon">
                                                <i class="fas fa-exchange-alt fa-3x"></i>
                                            </div>
                                            <h3>Transfer Domain</h3>
                                            <p>Transfer your existing domain to us</p>
                                            <a href="{$WEB_ROOT}/cart.php?a=add&domain=transfer" class="btn btn-info btn-block">
                                                <i class="fas fa-arrow-right"></i>
                                                Transfer Domain
                                            </a>
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {/if}

                    <!-- Trust Indicators -->
                    <div class="widdx-trust-section mt-5">
                        <div class="widdx-trust-indicators">
                            <div class="widdx-trust-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Secure Payments</span>
                            </div>
                            <div class="widdx-trust-item">
                                <i class="fas fa-headset"></i>
                                <span>24/7 Support</span>
                            </div>
                            <div class="widdx-trust-item">
                                <i class="fas fa-rocket"></i>
                                <span>Fast Setup</span>
                            </div>
                            <div class="widdx-trust-item">
                                <i class="fas fa-undo"></i>
                                <span>30-Day Guarantee</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include order form specific JavaScript -->
<script src="{$WEB_ROOT}/templates/orderforms/widdx_modern/js/order-form.js"></script>
