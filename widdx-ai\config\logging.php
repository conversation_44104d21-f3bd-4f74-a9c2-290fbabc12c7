<?php
// Security Configuration
if (!defined('SECURE_LOGGING_LOADED')) {
    define('SECURE_LOGGING_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Logging Configuration

// Log levels
const LOG_LEVEL_DEBUG = 'debug';
const LOG_LEVEL_INFO = 'info';
const LOG_LEVEL_WARNING = 'warning';
const LOG_LEVEL_ERROR = 'error';
const LOG_LEVEL_CRITICAL = 'critical';

// Log level configuration
const DEFAULT_LOG_LEVEL = Env::get('DEFAULT_LOG_LEVEL', LOG_LEVEL_INFO);

// Log channels
const LOG_CHANNEL_FILE = 'file';
const LOG_CHANNEL_SYSLOG = 'syslog';
const LOG_CHANNEL_ERRORLOG = 'errorlog';
const LOG_CHANNEL_STACK = 'stack';

// Default log channel
const DEFAULT_LOG_CHANNEL = Env::get('DEFAULT_LOG_CHANNEL', LOG_CHANNEL_STACK);

// Log format
const LOG_FORMAT = '[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n';

// Log rotation settings
const LOG_MAX_FILES = Env::get('LOG_MAX_FILES', 30);
const LOG_MAX_SIZE = Env::get('LOG_MAX_SIZE', 10485760); // 10MB

// Log retention period (in days)
const LOG_RETENTION_DAYS = Env::get('LOG_RETENTION_DAYS', 30);

// Log file permissions
const LOG_FILE_PERMISSIONS = Env::get('LOG_FILE_PERMISSIONS', 0644);

// Log directory permissions
const LOG_DIR_PERMISSIONS = Env::get('LOG_DIR_PERMISSIONS', 0755);

// Log file name format
const LOG_FILE_NAME_FORMAT = Env::get('LOG_FILE_NAME_FORMAT', 'app-%date%.log');

// Log rotation configuration
const LOG_ROTATE_DAILY = true;
const LOG_COMPRESS_OLD = true;

// Error logging configuration
const ERROR_LOG_CHANNEL = Env::get('ERROR_LOG_CHANNEL', LOG_CHANNEL_FILE);
const ERROR_LOG_LEVEL = Env::get('ERROR_LOG_LEVEL', LOG_LEVEL_ERROR);

// Security logging configuration
const SECURITY_LOG_CHANNEL = Env::get('SECURITY_LOG_CHANNEL', LOG_CHANNEL_FILE);
const SECURITY_LOG_LEVEL = Env::get('SECURITY_LOG_LEVEL', LOG_LEVEL_WARNING);

// Performance logging configuration
const PERFORMANCE_LOG_CHANNEL = Env::get('PERFORMANCE_LOG_CHANNEL', LOG_CHANNEL_FILE);
const PERFORMANCE_LOG_LEVEL = Env::get('PERFORMANCE_LOG_LEVEL', LOG_LEVEL_INFO);

// Log filtering configuration
const FILTER_SENSITIVE_DATA = true;
const SENSITIVE_KEYS = [
    'password',
    'api_key',
    'token',
    'secret',
    'credential'
];

// Log cleanup configuration
const CLEANUP_INTERVAL = Env::get('CLEANUP_INTERVAL', 86400); // 24 hours
const CLEANUP_BATCH_SIZE = Env::get('CLEANUP_BATCH_SIZE', 1000);

// Original logging configuration
define('LOG_LEVEL', 'DEBUG'); // DEBUG, INFO, NOTICE, WARNING, ERROR, CRITICAL, ALERT, EMERGENCY
define('LOG_FILE_SIZE', 10485760); // 10MB
define('LOG_FILE_COUNT', 5); // Number of rotated log files
define('LOG_DIR', __DIR__ . '/../logs');
define('LOG_FORMAT_ORIGINAL', '{timestamp} [{level}] {message} {context}');

define('LOG_ROTATE_DAILY_ORIGINAL', true);
define('LOG_ROTATE_SIZE_ORIGINAL', true);
define('LOG_COMPRESS_ORIGINAL', true);

define('LOG_DB_ENABLED', true);
define('LOG_FILE_ENABLED', true);
define('LOG_SYSLOG_ENABLED', false);

define('LOG_NOTIFY_ERRORS', true);
define('LOG_NOTIFY_LEVELS', ['ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY']);
define('LOG_NOTIFY_METHOD', 'email'); // email, slack, webhook
define('LOG_NOTIFY_EMAIL', '<EMAIL>');

define('LOG_DB_TABLE', 'logs');
define('LOG_DB_TTL', 30); // Days to keep logs in database
define('LOG_DB_BATCH_SIZE', 1000);

define('LOG_FILE_PERMISSIONS', 0644);
define('LOG_DIR_PERMISSIONS', 0755);

define('LOG_ERROR_REPORTING', E_ALL);
define('LOG_DISPLAY_ERRORS', false);
define('LOG_LOG_ERRORS', true);

define('LOG_MAX_VARIABLE_SIZE', 1048576); // 1MB

define('LOG_CONTEXT_KEYS', [
    'request_id',
    'user_id',
    'session_id',
    'ip_address',
    'user_agent'
]);

define('LOG_MASK_KEYS', [
    'password',
    'api_key',
    'auth_token',
    'credit_card',
    'ssn'
]);

define('LOG_MASK_CHAR', '*');
define('LOG_MASK_LENGTH', 4);

define('LOG_RATE_LIMIT', true);
define('LOG_RATE_LIMIT_WINDOW', 60); // seconds
define('LOG_RATE_LIMIT_MAX', 1000);

define('LOG_ASYNC_ENABLED', true);
define('LOG_QUEUE_SIZE', 1000);
define('LOG_QUEUE_TIMEOUT', 5); // seconds

define('LOG_DEBUG_ENABLED', APP_DEBUG);
define('LOG_DEBUG_FILE', __DIR__ . '/../logs/debug.log');
define('LOG_DEBUG_LEVEL', 'DEBUG');

define('LOG_ERROR_FILE', __DIR__ . '/../logs/error.log');
define('LOG_ERROR_LEVEL', 'ERROR');

define('LOG_ACCESS_FILE', __DIR__ . '/../logs/access.log');
define('LOG_ACCESS_LEVEL', 'INFO');

define('LOG_SECURITY_FILE', __DIR__ . '/../logs/security.log');
define('LOG_SECURITY_LEVEL', 'WARNING');

define('LOG_AUDIT_FILE', __DIR__ . '/../logs/audit.log');
define('LOG_AUDIT_LEVEL', 'INFO');

define('LOG_RATE_LIMIT_FILE', __DIR__ . '/../logs/rate_limit.log');
define('LOG_RATE_LIMIT_LEVEL', 'WARNING');

define('LOG_PERFORMANCE_FILE', __DIR__ . '/../logs/performance.log');
define('LOG_PERFORMANCE_LEVEL', 'INFO');

define('LOG_APPLICATION_FILE', __DIR__ . '/../logs/app.log');
define('LOG_APPLICATION_LEVEL', 'INFO');
