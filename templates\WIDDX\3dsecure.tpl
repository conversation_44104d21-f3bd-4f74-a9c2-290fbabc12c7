{include file="$template/includes/alert.tpl" type="info" msg="{lang key='creditcard3dsecure'}" textcenter=true}

<div class="widdx-3ds-container" id="3ds-container" data-debug="false">
    <!-- Enhanced 3D Secure Status Display -->
    <div class="card widdx-3ds-card">
        <div class="card-header">
            <h4 class="card-title mb-0">
                <i class="fas fa-shield-alt text-success me-2"></i>
                Secure Authentication
            </h4>
        </div>
        <div class="card-body text-center">
            <!-- Status Display -->
            <div id="3ds-status" class="widdx-3ds-status mb-4">
                <div class="widdx-3ds-status-icon">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <p class="widdx-3ds-status-text">Initializing secure authentication...</p>
            </div>

            <!-- Progress Bar -->
            <div class="progress widdx-3ds-progress mb-4">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">
                </div>
            </div>

            <!-- Legacy 3DS Form (Hidden) -->
            <div id="frmThreeDAuth" class="d-none">
                {$code}
            </div>

            <!-- Enhanced 3DS Challenge Container -->
            <div id="widdx-3ds-challenge-container" class="d-none">
                <iframe name="3dauth"
                        class="widdx-3ds-iframe"
                        src="about:blank"
                        allow="payment"
                        sandbox="allow-scripts allow-same-origin allow-forms allow-top-navigation"
                        title="3D Secure Authentication">
                </iframe>
            </div>

            <!-- Mobile Challenge Overlay Template -->
            <div id="widdx-3ds-mobile-template" class="d-none">
                <div class="widdx-3ds-challenge-overlay">
                    <div class="widdx-3ds-challenge-container">
                        <div class="widdx-3ds-challenge-header">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                Secure Authentication
                            </h5>
                            <button type="button" class="btn-close widdx-3ds-challenge-close"
                                    aria-label="Close"></button>
                        </div>
                        <div class="widdx-3ds-challenge-body">
                            <iframe class="widdx-3ds-challenge-iframe"
                                    allow="payment"
                                    sandbox="allow-scripts allow-same-origin allow-forms allow-top-navigation"
                                    title="3D Secure Authentication">
                            </iframe>
                        </div>
                        <div class="widdx-3ds-challenge-footer">
                            <div class="widdx-3ds-challenge-timer">
                                <i class="fas fa-clock me-1"></i>
                                Time remaining: <span id="widdx-3ds-timer">5:00</span>
                            </div>
                            <div class="widdx-3ds-challenge-help">
                                <small class="text-muted">
                                    Complete authentication with your bank to proceed
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Information -->
            <div class="widdx-3ds-help mt-4">
                <div class="row">
                    <div class="col-md-4">
                        <div class="widdx-3ds-help-item">
                            <i class="fas fa-mobile-alt text-primary mb-2"></i>
                            <h6>Mobile Users</h6>
                            <small class="text-muted">Use your banking app for quick authentication</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="widdx-3ds-help-item">
                            <i class="fas fa-clock text-warning mb-2"></i>
                            <h6>Time Limit</h6>
                            <small class="text-muted">Complete within 5 minutes</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="widdx-3ds-help-item">
                            <i class="fas fa-question-circle text-info mb-2"></i>
                            <h6>Need Help?</h6>
                            <small class="text-muted">Contact your bank if you have issues</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Display -->
    <div id="widdx-3ds-error" class="alert alert-danger d-none" role="alert">
        <h6 class="alert-heading">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Authentication Error
        </h6>
        <p class="mb-0" id="widdx-3ds-error-message"></p>
        <hr>
        <div class="d-flex justify-content-between align-items-center">
            <small class="mb-0">You can try again or contact support for assistance.</small>
            <button type="button" class="btn btn-sm btn-outline-danger" id="widdx-3ds-retry">
                <i class="fas fa-redo me-1"></i>
                Retry
            </button>
        </div>
    </div>
</div>

<!-- Enhanced 3D Secure Styles -->
<style>
.widdx-3ds-container {
    max-width: 600px;
    margin: 0 auto;
}

.widdx-3ds-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.widdx-3ds-status {
    padding: 2rem;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.widdx-3ds-status-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.widdx-3ds-status.widdx-3ds-status-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.widdx-3ds-status.widdx-3ds-status-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.widdx-3ds-progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.widdx-3ds-iframe {
    width: 100%;
    height: 500px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.widdx-3ds-help-item {
    text-align: center;
    padding: 1rem;
}

.widdx-3ds-help-item i {
    font-size: 1.5rem;
    display: block;
}

/* Mobile Challenge Overlay */
.widdx-3ds-challenge-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.widdx-3ds-challenge-container {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.widdx-3ds-challenge-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: between;
    align-items: center;
    background: #f8f9fa;
}

.widdx-3ds-challenge-body {
    flex: 1;
    overflow: hidden;
}

.widdx-3ds-challenge-iframe {
    width: 100%;
    height: 400px;
    border: none;
}

.widdx-3ds-challenge-footer {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    text-align: center;
}

.widdx-3ds-challenge-timer {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widdx-3ds-container {
        margin: 0;
        padding: 0 1rem;
    }

    .widdx-3ds-card {
        border-radius: 8px;
    }

    .widdx-3ds-status {
        padding: 1.5rem;
    }

    .widdx-3ds-iframe {
        height: 400px;
    }

    .widdx-3ds-challenge-container {
        margin: 0.5rem;
        max-height: 95vh;
    }

    .widdx-3ds-challenge-iframe {
        height: 350px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .progress-bar-animated {
        animation: none;
    }

    .spinner-border {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .widdx-3ds-card {
        border: 2px solid #000;
    }

    .widdx-3ds-status {
        border: 1px solid #000;
    }
}
</style>

<!-- Enhanced 3D Secure JavaScript -->
<script src="{$WEB_ROOT}/templates/{$template}/js/3ds-enhanced.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced 3D Secure
    if (typeof Enhanced3DSecure !== 'undefined') {
        window.widdx3DS = new Enhanced3DSecure({
            challengeTimeout: 300000, // 5 minutes
            challengeWindowSize: '05', // Full screen
            statusUpdateInterval: 2000,
            maxRetries: 3,
            debug: {$smarty.const.WHMCS_DEBUG|default:false}
        });

        // Auto-submit legacy form if enhanced 3DS fails
        const legacyForm = document.querySelector("#frmThreeDAuth form");
        if (legacyForm) {
            legacyForm.setAttribute('target', '3dauth');

            // Fallback to legacy 3DS after 3 seconds
            setTimeout(function() {
                if (!window.widdx3DS.transactionId) {
                    console.log('Falling back to legacy 3D Secure');
                    document.getElementById('widdx-3ds-challenge-container').classList.remove('d-none');
                    autoSubmitFormByContainer('frmThreeDAuth');
                }
            }, 3000);
        }
    } else {
        // Fallback to legacy 3DS immediately
        console.log('Enhanced 3D Secure not available, using legacy');
        const legacyForm = document.querySelector("#frmThreeDAuth form");
        if (legacyForm) {
            legacyForm.setAttribute('target', '3dauth');
            document.getElementById('widdx-3ds-challenge-container').classList.remove('d-none');
            setTimeout(() => autoSubmitFormByContainer('frmThreeDAuth'), 1000);
        }
    }

    // Retry button functionality
    document.getElementById('widdx-3ds-retry')?.addEventListener('click', function() {
        location.reload();
    });
});
</script>
