<?php
// Test the Database class
require_once __DIR__ . '/includes/Env.php';
require_once __DIR__ . '/includes/Database.php';

echo "<h2>Testing Database Class</h2>";

try {
    // Test environment variables
    echo "<h3>Environment Variables:</h3>";
    $envVars = [
        'DB_HOST' => Env::get('DB_HOST', 'not set'),
        'DB_DATABASE' => Env::get('DB_DATABASE', 'not set'),
        'DB_USERNAME' => Env::get('DB_USERNAME', 'not set'),
        'DB_PASSWORD' => Env::get('DB_PASSWORD', 'not set'),
        'DB_PORT' => Env::get('DB_PORT', '3306')
    ];
    
    echo "<pre>";
    print_r($envVars);
    echo "</pre>";
    
    // Test database connection
    echo "<h3>Testing Database Connection:</h3>";
    $db = Database::getInstance()->getConnection();
    
    if ($db) {
        echo "<p style='color:green;'>Successfully connected to the database.</p>";
        
        // Test a simple query
        $stmt = $db->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "<p>Found " . count($tables) . " tables in the database:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No tables found in the database.</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color:red;'>";
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " (Line: " . $e->getLine() . ")</p>";
    echo "<p>Stack Trace:</p><pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
    
    // Check if PDO is available
    if (!extension_loaded('pdo')) {
        echo "<p style='color:red;'>PDO extension is not loaded. Please enable it in your php.ini file.</p>";
    } else {
        echo "<p>PDO extension is loaded.</p>";
        $drivers = PDO::getAvailableDrivers();
        echo "<p>Available PDO drivers: " . implode(', ', $drivers) . "</p>";
    }
}
?>
