<?php
// Simple script to check MySQL connection and list databases
$host = Env::get('DB_HOST', 'localhost');
$user = Env::get('DB_USER');
$pass = Env::get('DB_PASS');

echo "<h2>MySQL Connection Test</h2>";

// Check if MySQLi is available
if (!function_exists('mysqli_connect')) {
    die("MySQLi extension is not enabled in PHP. Please enable it in your php.ini file.");
}

echo "MySQLi extension is enabled.<br>";

// Try to connect to MySQL
$conn = @mysqli_connect($host, $user, $pass);

if (!$conn) {
    $error = mysqli_connect_error();
    $errno = mysqli_connect_errno();
    
    echo "<div style='color:red;'>";
    echo "Failed to connect to MySQL: " . htmlspecialchars($error) . "<br>";
    echo "Error code: " . $errno . "<br>";
    echo "</div>";
    
    // Log the error
    error_log("MySQL Connection Error: " . $error . " (Code: " . $errno . ")");
    
    // Don't show sensitive error details in production
    if (Env::get('APP_ENV', 'production') === 'production') {
        echo "<div class='alert alert-danger'>";
        echo "A database connection error occurred. Please contact support.";
        echo "</div>";
        exit;
    }
}

if (!$conn) {
    echo "<div style='color:red;'>";
    echo "Failed to connect to MySQL: " . mysqli_connect_error() . "<br>";
    echo "Error code: " . mysqli_connect_errno() . "<br>";
    echo "</div>";
    
    echo "<h3>Troubleshooting steps:</h3>";
    echo "1. Make sure MySQL service is running in XAMPP Control Panel<br>";
    echo "2. Verify the MySQL username and password in the .env file<br>";
    echo "3. Check if the MySQL user has proper permissions<br>";
    
    // Try to connect as root to check if the user exists
    echo "<h3>Testing with root user (if available):</h3>";
    $rootConn = @mysqli_connect($host, 'root', '');
    if ($rootConn) {
        echo "Successfully connected as root user.<br>";
        
        // Check if widdx user exists
        $result = mysqli_query($rootConn, "SELECT user FROM mysql.user WHERE user='widdx'");
        if ($result && mysqli_num_rows($result) > 0) {
            echo "User 'widdx' exists in MySQL.<br>";
            
            // Check user privileges
            $grants = mysqli_query($rootConn, "SHOW GRANTS FOR 'widdx'@'%'");
            if ($grants) {
                echo "<br>User privileges:<br>";
                while ($row = mysqli_fetch_row($grants)) {
                    echo htmlspecialchars($row[0]) . "<br>";
                }
            }
            
            // Check if database exists
            $dbCheck = mysqli_query($rootConn, "SHOW DATABASES LIKE 'widdx_ai'");
            if ($dbCheck && mysqli_num_rows($dbCheck) > 0) {
                echo "<br>Database 'widdx_ai' exists.<br>";
                
                // List tables in database
                mysqli_select_db($rootConn, 'widdx_ai');
                $tables = mysqli_query($rootConn, "SHOW TABLES");
                if ($tables && mysqli_num_rows($tables) > 0) {
                    echo "<br>Tables in widdx_ai database:<br>";
                    while ($table = mysqli_fetch_row($tables)) {
                        echo "- " . $table[0] . "<br>";
                    }
                } else {
                    echo "<br>No tables found in widdx_ai database.<br>";
                }
            } else {
                echo "<br>Database 'widdx_ai' does not exist.<br>";
            }
            
        } else {
            echo "User 'widdx' does not exist in MySQL.<br>";
        }
        
        mysqli_close($rootConn);
    } else {
        echo "Could not connect as root user. Error: " . mysqli_connect_error() . "<br>";
    }
    
    exit();
}

echo "Successfully connected to MySQL server.<br>";

// List all databases
$databases = mysqli_query($conn, "SHOW DATABASES");

if ($databases) {
    echo "<h3>Available databases:</h3>";
    while ($row = mysqli_fetch_row($databases)) {
        echo $row[0] . "<br>";
    }
}

// Check if our database exists
$dbExists = mysqli_query($conn, "SHOW DATABASES LIKE 'widdx_ai'");
if ($dbExists && mysqli_num_rows($dbExists) > 0) {
    echo "<p style='color:green;'>Database 'widdx_ai' exists.</p>";
    
    // Select the database
    if (mysqli_select_db($conn, 'widdx_ai')) {
        echo "Successfully selected database 'widdx_ai'.<br>";
        
        // List tables
        $tables = mysqli_query($conn, "SHOW TABLES");
        if ($tables && mysqli_num_rows($tables) > 0) {
            echo "<h3>Tables in widdx_ai:</h3>";
            while ($table = mysqli_fetch_row($tables)) {
                echo "- " . $table[0] . "<br>";
            }
        } else {
            echo "No tables found in database 'widdx_ai'.<br>";
        }
    } else {
        echo "<span style='color:red;'>Failed to select database 'widdx_ai': " . mysqli_error($conn) . "</span><br>";
    }
} else {
    echo "<p style='color:red;'>Database 'widdx_ai' does not exist.</p>";
}

mysqli_close($conn);
?>
