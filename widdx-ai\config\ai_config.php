<?php
// AI Configuration
if (!defined('SECURE_AI_CONFIG_LOADED')) {
    define('SECURE_AI_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// AI Settings
const AI_ENABLED = Env::get('AI_ENABLED', true);
const AI_MODELS = Env::get('AI_MODELS', [
    'DEEPSEEK' => [
        'enabled' => Env::get('DEEPSEEK_ENABLED', true),
        'api_key' => Env::get('DEEPSEEK_API_KEY', ''),
        'endpoint' => Env::get('DEEPSEEK_ENDPOINT', 'https://api.deepseek.com/v1/chat/completions'),
        'model' => Env::get('DEEPSEEK_MODEL', 'deepseek-chat'),
        'max_tokens' => Env::get('DEEPSEEK_MAX_TOKENS', 2048),
        'temperature' => Env::get('DEEPSEEK_TEMPERATURE', 0.7),
        'top_p' => Env::get('DEEPSEEK_TOP_P', 0.9),
        'frequency_penalty' => Env::get('DEEPSEEK_FREQUENCY_PENALTY', 0.5),
        'presence_penalty' => Env::get('DEEPSEEK_PRESENCE_PENALTY', 0.5),
        'timeout' => Env::get('DEEPSEEK_TIMEOUT', 30),
        'retry_count' => Env::get('DEEPSEEK_RETRY_COUNT', 3),
        'retry_delay' => Env::get('DEEPSEEK_RETRY_DELAY', 1000),
        'rate_limit' => Env::get('DEEPSEEK_RATE_LIMIT', 60),
        'rate_limit_window' => Env::get('DEEPSEEK_RATE_LIMIT_WINDOW', 60),
        'cache_enabled' => Env::get('DEEPSEEK_CACHE_ENABLED', true),
        'cache_ttl' => Env::get('DEEPSEEK_CACHE_TTL', 86400)
    ],
    'GEMINI' => [
        'enabled' => Env::get('GEMINI_ENABLED', true),
        'api_key' => Env::get('GEMINI_API_KEY', ''),
        'endpoint' => Env::get('GEMINI_ENDPOINT', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent'),
        'model' => Env::get('GEMINI_MODEL', 'gemini-pro'),
        'max_tokens' => Env::get('GEMINI_MAX_TOKENS', 2048),
        'temperature' => Env::get('GEMINI_TEMPERATURE', 0.7),
        'top_p' => Env::get('GEMINI_TOP_P', 0.9),
        'frequency_penalty' => Env::get('GEMINI_FREQUENCY_PENALTY', 0.5),
        'presence_penalty' => Env::get('GEMINI_PRESENCE_PENALTY', 0.5),
        'timeout' => Env::get('GEMINI_TIMEOUT', 30),
        'retry_count' => Env::get('GEMINI_RETRY_COUNT', 3),
        'retry_delay' => Env::get('GEMINI_RETRY_DELAY', 1000),
        'rate_limit' => Env::get('GEMINI_RATE_LIMIT', 60),
        'rate_limit_window' => Env::get('GEMINI_RATE_LIMIT_WINDOW', 60),
        'cache_enabled' => Env::get('GEMINI_CACHE_ENABLED', true),
        'cache_ttl' => Env::get('GEMINI_CACHE_TTL', 86400)
    ]
]);

// Fallback Settings
const AI_FALLBACK_ENABLED = Env::get('AI_FALLBACK_ENABLED', true);
const AI_FALLBACK_MODELS = Env::get('AI_FALLBACK_MODELS', ['DEEPSEEK', 'GEMINI']);
const AI_FALLBACK_THRESHOLD = Env::get('AI_FALLBACK_THRESHOLD', 0.7);
const AI_FALLBACK_TIMEOUT = Env::get('AI_FALLBACK_TIMEOUT', 5000);

// Response Settings
const AI_RESPONSE_FORMAT = Env::get('AI_RESPONSE_FORMAT', 'json');
const AI_RESPONSE_TIMEOUT = Env::get('AI_RESPONSE_TIMEOUT', 30);
const AI_RESPONSE_MAX_RETRIES = Env::get('AI_RESPONSE_MAX_RETRIES', 3);

// Security and Validation
if (AI_ENABLED) {
    // Validate API keys
    foreach (AI_MODELS as $model => $config) {
        if ($config['enabled'] && empty($config['api_key'])) {
            trigger_error("API key not configured for {$model} model", E_USER_WARNING);
        }
    }
    
    // Validate endpoints
    foreach (AI_MODELS as $model => $config) {
        if (!filter_var($config['endpoint'], FILTER_VALIDATE_URL)) {
            trigger_error("Invalid endpoint URL for {$model} model", E_USER_WARNING);
        }
    }
    
    // Validate fallback settings
    if (AI_FALLBACK_ENABLED && !is_array(AI_FALLBACK_MODELS)) {
        trigger_error('Invalid fallback models configuration', E_USER_WARNING);
    }
    
    // Validate response settings
    if (AI_RESPONSE_TIMEOUT < 1) {
        trigger_error('Invalid response timeout value', E_USER_WARNING);
    }
    
    if (AI_RESPONSE_MAX_RETRIES < 0) {
        trigger_error('Invalid maximum retries value', E_USER_WARNING);
    }
    
    // Validate rate limits
    foreach (AI_MODELS as $model => $config) {
        if ($config['rate_limit'] < 0 || $config['rate_limit_window'] < 1) {
            trigger_error("Invalid rate limit configuration for {$model} model", E_USER_WARNING);
        }
    }
}
define('AI_RESPONSE_RETRY_DELAY', 1000);

define('AI_LOGGING_ENABLED', true);
define('AI_LOGGING_LEVEL', 'INFO');
define('AI_LOGGING_FILE', __DIR__ . '/../logs/ai.log');
define('AI_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');

define('AI_METRICS_ENABLED', true);
define('AI_METRICS_INTERVAL', 60);
define('AI_METRICS_FILE', __DIR__ . '/../logs/ai_metrics.log');
define('AI_METRICS_FORMAT', '{timestamp} {metric} {value}');

define('AI_RATE_LIMIT_ENABLED', true);
define('AI_RATE_LIMIT_WINDOW', 60);
define('AI_RATE_LIMIT_MAX', 100);
define('AI_RATE_LIMIT_BURST', 200);
define('AI_RATE_LIMIT_RECOVER', 300);
define('AI_RATE_LIMIT_STORAGE', 'redis');
define('AI_RATE_LIMIT_KEY_PREFIX', 'ai_rate_limit_');

define('AI_CACHE_ENABLED', true);
define('AI_CACHE_PROVIDER', 'redis');
define('AI_CACHE_TTL', 86400);
define('AI_CACHE_PREFIX', 'ai_cache_');
define('AI_CACHE_COMPRESSION', true);
define('AI_CACHE_COMPRESSION_LEVEL', 9);

define('AI_ERROR_HANDLING_ENABLED', true);
define('AI_ERROR_RETRY_ENABLED', true);
define('AI_ERROR_RETRY_MAX_ATTEMPTS', 3);
define('AI_ERROR_RETRY_DELAY', 1000);
define('AI_ERROR_RETRY_BACKOFF', 2);
define('AI_ERROR_LOGGING_ENABLED', true);
define('AI_ERROR_LOGGING_LEVEL', 'ERROR');

define('AI_SECURITY_ENABLED', true);
define('AI_SECURITY_INPUT_VALIDATION', true);
define('AI_SECURITY_OUTPUT_VALIDATION', true);
define('AI_SECURITY_RATE_LIMITING', true);
define('AI_SECURITY_API_KEY_VALIDATION', true);
define('AI_SECURITY_IP_RESTRICTIONS', true);
define('AI_SECURITY_REQUEST_VALIDATION', true);

define('AI_DEBUG_ENABLED', APP_DEBUG);
define('AI_DEBUG_LOGGING_ENABLED', true);
define('AI_DEBUG_LOGGING_FILE', __DIR__ . '/../logs/ai_debug.log');
define('AI_DEBUG_LOGGING_LEVEL', 'DEBUG');

define('AI_PERFORMANCE_MONITORING_ENABLED', true);
define('AI_PERFORMANCE_METRICS_ENABLED', true);
define('AI_PERFORMANCE_LOGGING_ENABLED', true);
define('AI_PERFORMANCE_LOGGING_FILE', __DIR__ . '/../logs/ai_performance.log');
define('AI_PERFORMANCE_LOGGING_LEVEL', 'INFO');

define('AI_ANALYTICS_ENABLED', true);
define('AI_ANALYTICS_METRICS_ENABLED', true);
define('AI_ANALYTICS_LOGGING_ENABLED', true);
define('AI_ANALYTICS_LOGGING_FILE', __DIR__ . '/../logs/ai_analytics.log');
define('AI_ANALYTICS_LOGGING_LEVEL', 'INFO');

define('AI_VERSION', '1.0.0');
define('AI_BUILD_DATE', date('Y-m-d H:i:s'));
define('AI_COMMIT_HASH', 'git rev-parse HEAD');
define('AI_ENVIRONMENT', APP_ENV);
define('AI_INSTANCE_ID', uniqid());

define('AI_FEATURE_FLAGS', [
    'context_aware_responses' => true,
    'multi_model_fallback' => true,
    'real_time_monitoring' => true,
    'advanced_error_handling' => true,
    'performance_metrics' => true,
    'analytics_tracking' => true
]);

define('AI_MODEL_SELECTION_STRATEGY', 'round_robin'); // round_robin, weighted, random
define('AI_MODEL_SELECTION_WEIGHTS', [
    'DEEPSEEK' => 0.6,
    'GEMINI' => 0.4
]);

define('AI_RESPONSE_SELECTION_CRITERIA', [
    'confidence' => 0.7,
    'length' => 0.5,
    'relevance' => 0.8,
    'coherence' => 0.9
]);

define('AI_PROMPT_TEMPLATES', [
    'default' => [
        'system' => 'You are a helpful AI assistant named WIDDX.',
        'user' => 'User: {question}',
        'assistant' => 'Assistant: {answer}'
    ],
    'technical' => [
        'system' => 'You are a technical AI assistant specializing in programming and system architecture.',
        'user' => 'Developer: {question}',
        'assistant' => 'Technical Assistant: {answer}'
    ],
    'creative' => [
        'system' => 'You are a creative AI assistant specializing in writing and content creation.',
        'user' => 'Writer: {question}',
        'assistant' => 'Creative Assistant: {answer}'
    ]
]);

define('AI_RESPONSE_FORMATS', [
    'text' => [
        'enabled' => true,
        'max_length' => 2048,
        'encoding' => 'utf-8',
        'sanitize' => true
    ],
    'markdown' => [
        'enabled' => true,
        'max_length' => 4096,
        'encoding' => 'utf-8',
        'sanitize' => true
    ],
    'html' => [
        'enabled' => true,
        'max_length' => 8192,
        'encoding' => 'utf-8',
        'sanitize' => true
    ]
]);

define('AI_ERROR_MESSAGES', [
    'generic' => 'حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
    'timeout' => 'تمت معالجة الطلب لفترة طويلة. يرجى المحاولة مرة أخرى.',
    'rate_limit' => 'تم تجاوز حد معدل الطلبات. يرجى الانتظار قليلاً.',
    'invalid_input' => 'المدخلات غير صالحة. يرجى التأكد من صحة المدخلات.',
    'api_error' => 'حدث خطأ في خدمة API. يرجى المحاولة مرة أخرى.',
    'authentication' => 'فشل التحقق من الهوية. يرجى التأكد من بيانات تسجيل الدخول.',
    'permission' => 'ليس لديك صلاحيات كافية للوصول إلى هذه الميزة.',
    'not_found' => 'لم يتم العثور على المورد المطلوب.',
    'server_error' => 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.'
]);

define('AI_VALIDATION_RULES', [
    'question' => [
        'required' => true,
        'min_length' => 2,
        'max_length' => 2048,
        'allowed_chars' => 'a-zA-Z0-9\s\p{Arabic}\.\,\?\!\-\_\@\#\$\%\^\&\*\(\)\[\]\{\}\|\;\:\"\'\<\>\/\\',
        'sanitize' => true
    ],
    'answer' => [
        'required' => true,
        'min_length' => 2,
        'max_length' => 2048,
        'allowed_chars' => 'a-zA-Z0-9\s\p{Arabic}\.\,\?\!\-\_\@\#\$\%\^\&\*\(\)\[\]\{\}\|\;\:\"\'\<\>\/\\',
        'sanitize' => true
    ],
    'context' => [
        'required' => false,
        'min_length' => 2,
        'max_length' => 4096,
        'allowed_chars' => 'a-zA-Z0-9\s\p{Arabic}\.\,\?\!\-\_\@\#\$\%\^\&\*\(\)\[\]\{\}\|\;\:\"\'\<\>\/\\',
        'sanitize' => true
    ]
]);

define('AI_RATE_LIMITING_RULES', [
    'global' => [
        'window' => 60,
        'limit' => 100,
        'burst' => 200,
        'recover' => 300
    ],
    'per_user' => [
        'window' => 60,
        'limit' => 50,
        'burst' => 100,
        'recover' => 300
    ],
    'per_ip' => [
        'window' => 60,
        'limit' => 20,
        'burst' => 50,
        'recover' => 300
    ]
]);

define('AI_LOGGING_FORMATS', [
    'default' => '{timestamp} [{level}] {message} {context}',
    'debug' => '{timestamp} [{level}] {message} {context} {trace}',
    'error' => '{timestamp} [{level}] {message} {context} {error}',
    'performance' => '{timestamp} [{level}] {message} {duration}ms {memory}MB'
]);

define('AI_METRICS_FORMATS', [
    'default' => '{timestamp} {metric} {value}',
    'performance' => '{timestamp} {metric} {value}ms {memory}MB',
    'rate' => '{timestamp} {metric} {value}/s',
    'count' => '{timestamp} {metric} {value}'
]);

define('AI_API_KEYS', [
    'DEEPSEEK' => [
        'enabled' => true,
        'required' => true,
        'validate' => true,
        'rotate' => true
    ],
    'GEMINI' => [
        'enabled' => true,
        'required' => true,
        'validate' => true,
        'rotate' => true
    ]
]);

define('AI_IP_RESTRICTIONS', [
    'whitelist' => [
        'enabled' => true,
        'ips' => ['127.0.0.1', '::1']
    ],
    'blacklist' => [
        'enabled' => true,
        'ips' => []
    ]
]);

define('AI_REQUEST_VALIDATION', [
    'enabled' => true,
    'max_headers' => 100,
    'max_body_size' => 10485760,
    'timeout' => 30,
    'validate_content_type' => true,
    'validate_content_length' => true,
    'validate_headers' => true
]);
