<?php

/**
 * Transaction Status API for WIDDX
 * 
 * Provides RESTful API endpoints for transaction status management
 * 
 * @package WIDDX
 * @subpackage PaymentGateway
 * @version 1.0.0
 */

// Security headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// CORS headers for AJAX requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Initialize WHMCS environment
$init_path = __DIR__ . '/../../../../init.php';
if (file_exists($init_path)) {
    define('CLIENTAREA', true);
    require_once $init_path;
} else {
    http_response_code(500);
    echo json_encode(['error' => 'WHMCS environment not available']);
    exit;
}

require_once __DIR__ . '/../EnhancedTransactionManager.php';
require_once __DIR__ . '/../TransactionStatusDashboard.php';
require_once __DIR__ . '/../Logger.php';

class TransactionStatusAPI {
    
    private $transactionManager;
    private $dashboard;
    private $logger;
    private $gatewayParams;
    
    public function __construct() {
        // Load gateway parameters
        $this->gatewayParams = getGatewayVariables('lahza');
        
        $this->transactionManager = new EnhancedTransactionManager($this->gatewayParams);
        $this->dashboard = new TransactionStatusDashboard($this->gatewayParams);
        $this->logger = new LahzaLogger();
    }
    
    /**
     * Handle API request
     */
    public function handleRequest() {
        try {
            $method = $_SERVER['REQUEST_METHOD'];
            $path = $_GET['path'] ?? '';
            
            // Basic authentication check
            if (!$this->authenticate()) {
                $this->sendError('Unauthorized', 401);
                return;
            }
            
            // Rate limiting
            if (!$this->checkRateLimit()) {
                $this->sendError('Rate limit exceeded', 429);
                return;
            }
            
            // Route request
            switch ($method) {
                case 'GET':
                    $this->handleGetRequest($path);
                    break;
                    
                case 'POST':
                    $this->handlePostRequest($path);
                    break;
                    
                case 'PUT':
                    $this->handlePutRequest($path);
                    break;
                    
                case 'DELETE':
                    $this->handleDeleteRequest($path);
                    break;
                    
                default:
                    $this->sendError('Method not allowed', 405);
            }
            
        } catch (Exception $e) {
            $this->logger->error('API request failed', [
                'method' => $_SERVER['REQUEST_METHOD'],
                'path' => $_GET['path'] ?? '',
                'error' => $e->getMessage()
            ], 'api');
            
            $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * Handle GET requests
     */
    private function handleGetRequest($path) {
        switch ($path) {
            case 'dashboard':
                $this->sendResponse($this->dashboard->getDashboardData());
                break;
                
            case 'transaction':
                $transactionId = $_GET['id'] ?? '';
                if (empty($transactionId)) {
                    $this->sendError('Transaction ID required', 400);
                    return;
                }
                
                $transaction = $this->transactionManager->getTransaction($transactionId);
                if (!$transaction) {
                    $this->sendError('Transaction not found', 404);
                    return;
                }
                
                $this->sendResponse($transaction);
                break;
                
            case 'transaction/details':
                $transactionId = $_GET['id'] ?? '';
                if (empty($transactionId)) {
                    $this->sendError('Transaction ID required', 400);
                    return;
                }
                
                $details = $this->dashboard->getTransactionDetails($transactionId);
                if (!$details) {
                    $this->sendError('Transaction not found', 404);
                    return;
                }
                
                $this->sendResponse($details);
                break;
                
            case 'transaction/history':
                $transactionId = $_GET['id'] ?? '';
                if (empty($transactionId)) {
                    $this->sendError('Transaction ID required', 400);
                    return;
                }
                
                $history = $this->transactionManager->getStatusHistory($transactionId);
                $this->sendResponse($history);
                break;
                
            case 'transactions/search':
                $filters = [
                    'transaction_id' => $_GET['transaction_id'] ?? '',
                    'invoice_id' => $_GET['invoice_id'] ?? '',
                    'status' => $_GET['status'] ?? '',
                    'date_from' => $_GET['date_from'] ?? '',
                    'date_to' => $_GET['date_to'] ?? '',
                    'amount_min' => $_GET['amount_min'] ?? '',
                    'amount_max' => $_GET['amount_max'] ?? '',
                    'limit' => min(($_GET['limit'] ?? 50), 1000) // Max 1000 results
                ];
                
                $transactions = $this->dashboard->searchTransactions($filters);
                $this->sendResponse($transactions);
                break;
                
            case 'transactions/status':
                $status = $_GET['status'] ?? '';
                if (empty($status)) {
                    $this->sendError('Status required', 400);
                    return;
                }
                
                $limit = min(($_GET['limit'] ?? 100), 1000);
                $transactions = $this->transactionManager->getTransactionsByStatus($status, $limit);
                $this->sendResponse($transactions);
                break;
                
            case 'transactions/expired':
                $transactions = $this->transactionManager->getExpiredTransactions();
                $this->sendResponse($transactions);
                break;
                
            case 'statistics':
                $dateFrom = $_GET['date_from'] ?? null;
                $dateTo = $_GET['date_to'] ?? null;
                $stats = $this->transactionManager->getTransactionStatistics($dateFrom, $dateTo);
                $this->sendResponse($stats);
                break;
                
            case 'charts/hourly':
                $date = $_GET['date'] ?? null;
                $data = $this->dashboard->getHourlyTransactionData($date);
                $this->sendResponse($data);
                break;
                
            case 'charts/daily':
                $days = min(($_GET['days'] ?? 30), 365);
                $data = $this->dashboard->getDailyTransactionData($days);
                $this->sendResponse($data);
                break;
                
            case 'export':
                $this->handleExport();
                break;
                
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Handle POST requests
     */
    private function handlePostRequest($path) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($path) {
            case 'transaction/create':
                if (!isset($input['invoice_id'], $input['amount'], $input['currency'])) {
                    $this->sendError('Missing required fields', 400);
                    return;
                }
                
                $transactionId = $this->transactionManager->createTransaction(
                    $input['invoice_id'],
                    $input['amount'],
                    $input['currency'],
                    $input['metadata'] ?? []
                );
                
                if ($transactionId) {
                    $this->sendResponse(['transaction_id' => $transactionId], 201);
                } else {
                    $this->sendError('Failed to create transaction', 500);
                }
                break;
                
            case 'transaction/update-status':
                if (!isset($input['transaction_id'], $input['status'])) {
                    $this->sendError('Missing required fields', 400);
                    return;
                }
                
                $success = $this->transactionManager->updateStatus(
                    $input['transaction_id'],
                    $input['status'],
                    $input['reason'] ?? '',
                    $input['metadata'] ?? [],
                    $input['priority'] ?? EnhancedTransactionManager::PRIORITY_NORMAL
                );
                
                if ($success) {
                    $this->sendResponse(['success' => true]);
                } else {
                    $this->sendError('Failed to update status', 400);
                }
                break;
                
            case 'transactions/process-expired':
                $count = $this->transactionManager->processExpiredTransactions();
                $this->sendResponse(['processed_count' => $count]);
                break;
                
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Handle PUT requests
     */
    private function handlePutRequest($path) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($path) {
            case 'transaction':
                $transactionId = $_GET['id'] ?? '';
                if (empty($transactionId)) {
                    $this->sendError('Transaction ID required', 400);
                    return;
                }
                
                // Update transaction metadata or other fields
                // Implementation depends on specific requirements
                $this->sendResponse(['success' => true]);
                break;
                
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Handle DELETE requests
     */
    private function handleDeleteRequest($path) {
        switch ($path) {
            case 'transactions/cleanup':
                $days = $_GET['days'] ?? 365;
                $count = $this->transactionManager->cleanupOldTransactions($days);
                $this->sendResponse(['deleted_count' => $count]);
                break;
                
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Handle export requests
     */
    private function handleExport() {
        $filters = [
            'transaction_id' => $_GET['transaction_id'] ?? '',
            'invoice_id' => $_GET['invoice_id'] ?? '',
            'status' => $_GET['status'] ?? '',
            'date_from' => $_GET['date_from'] ?? '',
            'date_to' => $_GET['date_to'] ?? '',
            'amount_min' => $_GET['amount_min'] ?? '',
            'amount_max' => $_GET['amount_max'] ?? ''
        ];
        
        $csv = $this->dashboard->exportTransactions($filters);
        
        if ($csv) {
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="transactions_' . date('Y-m-d_H-i-s') . '.csv"');
            echo $csv;
        } else {
            $this->sendError('Export failed', 500);
        }
    }
    
    /**
     * Basic authentication
     */
    private function authenticate() {
        // Check for API key in header or query parameter
        $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $_GET['api_key'] ?? '';
        
        if (empty($apiKey)) {
            return false;
        }
        
        // Validate API key (implement your own validation logic)
        $validApiKey = $this->gatewayParams['api_key'] ?? '';
        
        return hash_equals($validApiKey, $apiKey);
    }
    
    /**
     * Rate limiting
     */
    private function checkRateLimit() {
        $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cacheKey = "api_rate_limit_{$clientIP}";
        
        // Simple file-based rate limiting
        $cacheFile = __DIR__ . '/../cache/' . md5($cacheKey) . '.cache';
        $maxRequests = 100; // per minute
        $timeWindow = 60; // seconds
        
        if (!is_dir(dirname($cacheFile))) {
            mkdir(dirname($cacheFile), 0755, true);
        }
        
        $now = time();
        $requests = [];
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data && isset($data['requests'])) {
                $requests = array_filter($data['requests'], function($timestamp) use ($now, $timeWindow) {
                    return ($now - $timestamp) < $timeWindow;
                });
            }
        }
        
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        $requests[] = $now;
        file_put_contents($cacheFile, json_encode(['requests' => $requests]), LOCK_EX);
        
        return true;
    }
    
    /**
     * Send JSON response
     */
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('c')
        ], JSON_PRETTY_PRINT);
    }
    
    /**
     * Send error response
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('c')
        ], JSON_PRETTY_PRINT);
    }
}

// Handle the request
$api = new TransactionStatusAPI();
$api->handleRequest();
