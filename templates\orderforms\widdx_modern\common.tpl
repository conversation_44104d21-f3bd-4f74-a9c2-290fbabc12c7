<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#2c5aa0">
    <title>{if $kbarticle.title}{$kbarticle.title} - {/if}{$pagetitle} - {$companyname}</title>
    
    <!-- WIDDX Order Form Styles -->
    <link href="{$WEB_ROOT}/templates/orderforms/widdx_modern/css/order-form.css" rel="stylesheet">
    <link href="{$WEB_ROOT}/templates/WIDDX/css/theme.css" rel="stylesheet">
    <link href="{$WEB_ROOT}/templates/WIDDX/css/accessibility.css" rel="stylesheet">
    
    <!-- Bootstrap and FontAwesome -->
    <link href="{$WEB_ROOT}/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="{$WEB_ROOT}/assets/css/fontawesome-all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- WHMCS Variables -->
    <script>
        var csrfToken = '{$token}',
            whmcsBaseUrl = '{$systemurl}',
            orderFormTemplate = 'widdx_modern';
    </script>
    
    <!-- jQuery and Bootstrap JS -->
    <script src="{$WEB_ROOT}/assets/js/jquery.min.js"></script>
    <script src="{$WEB_ROOT}/assets/js/bootstrap.bundle.min.js"></script>
    
    {$headoutput}
</head>

<body class="widdx-order-form-body">
    {$headeroutput}
    
    <!-- Skip Links for Accessibility -->
    <div class="widdx-skip-links">
        <a href="#main-content" class="widdx-skip-link">Skip to main content</a>
        <a href="#order-form" class="widdx-skip-link">Skip to order form</a>
    </div>
    
    <!-- Header -->
    <header class="widdx-order-header" role="banner">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="widdx-logo-container">
                        {if $logo}
                            <img src="{$logo}" alt="{$companyname}" class="widdx-logo">
                        {else}
                            <h1 class="widdx-company-name">{$companyname}</h1>
                        {/if}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="widdx-header-actions text-right">
                        {if $loggedin}
                            <div class="widdx-user-info">
                                <span class="widdx-welcome">Welcome, {$clientsdetails.firstname}!</span>
                                <a href="{$WEB_ROOT}/clientarea.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-user"></i> Client Area
                                </a>
                                <a href="{$WEB_ROOT}/logout.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a>
                            </div>
                        {else}
                            <div class="widdx-auth-links">
                                <a href="{$WEB_ROOT}/login.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </a>
                                <a href="{$WEB_ROOT}/register.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-plus"></i> Register
                                </a>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" role="main">
        <!-- Flash Messages -->
        {if $errormessage}
            <div class="container mt-3">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Error:</strong> {$errormessage}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        {/if}
        
        {if $successmessage}
            <div class="container mt-3">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i>
                    <strong>Success:</strong> {$successmessage}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        {/if}
        
        {if $infomessage}
            <div class="container mt-3">
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle"></i>
                    <strong>Info:</strong> {$infomessage}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        {/if}
    </main>

    <!-- Footer -->
    <footer class="widdx-order-footer" role="contentinfo">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="widdx-footer-info">
                        <p>&copy; {date('Y')} {$companyname}. All rights reserved.</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="widdx-footer-links text-right">
                        <a href="{$WEB_ROOT}/contact.php">Contact</a>
                        <a href="{$WEB_ROOT}/supporttickets.php">Support</a>
                        <a href="{$WEB_ROOT}/knowledgebase.php">Knowledge Base</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Security and Performance Scripts -->
    <script>
        // CSRF Protection
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            if (window.performance && window.performance.timing) {
                var loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
                console.log('Page load time:', loadTime + 'ms');
            }
        });
    </script>
    
    {$footeroutput}
</body>
</html>
