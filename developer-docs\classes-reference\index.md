+++
chapter = true
icon = "<b>iii. </b>"
next = "/next/path"
prev = "/prev/path"
title = "Classes Reference"
weight = 0

+++

### Classes

# Documentation

We make available a number of our internal model classes to make developing modules and hooks and working with the WHMCS database easier.

<p style="text-align: center;">
    {{% button href="http://docs.whmcs.com/classes/index.html" target="_blank" %}}<i class="fa fa-file-text-o"></i> Launch Class Documentation{{% /button %}}
</p>
