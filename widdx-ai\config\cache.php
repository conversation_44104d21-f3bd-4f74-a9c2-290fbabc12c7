<?php
// Cache Configuration
if (!defined('SECURE_CACHE_CONFIG_LOADED')) {
    define('SECURE_CACHE_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// Cache Settings
const CACHE_ENABLED = Env::get('CACHE_ENABLED', true);
const CACHE_PROVIDER = Env::get('CACHE_PROVIDER', 'redis');
const CACHE_TTL = Env::get('CACHE_TTL', 86400);
const CACHE_PREFIX = Env::get('CACHE_PREFIX', 'widdx_');
const CACHE_COMPRESSION = Env::get('CACHE_COMPRESSION', true);
const CACHE_COMPRESSION_LEVEL = Env::get('CACHE_COMPRESSION_LEVEL', 9);

// Cache Management
const CACHE_MAX_SIZE = Env::get('CACHE_MAX_SIZE', **********);
const CACHE_CLEAN_INTERVAL = Env::get('CACHE_CLEAN_INTERVAL', 3600);
const CACHE_CLEAN_THRESHOLD = Env::get('CACHE_CLEAN_THRESHOLD', 0.9);

// Cache Sharding and Locking
const CACHE_SHARDS = Env::get('CACHE_SHARDS', 16);
const CACHE_HASH_KEY = Env::get('CACHE_HASH_KEY', 'sha256');
const CACHE_LOCK_TIMEOUT = Env::get('CACHE_LOCK_TIMEOUT', 5);
const CACHE_LOCK_RETRIES = Env::get('CACHE_LOCK_RETRIES', 3);
const CACHE_LOCK_DELAY = Env::get('CACHE_LOCK_DELAY', 100);

// Cache Statistics
const CACHE_STATS_ENABLED = Env::get('CACHE_STATS_ENABLED', true);
const CACHE_STATS_INTERVAL = Env::get('CACHE_STATS_INTERVAL', 300);
const CACHE_STATS_FILE = Env::get('CACHE_STATS_FILE', __DIR__ . '/../logs/cache_stats.log');

// Rate Limiting
const CACHE_RATE_LIMIT_ENABLED = Env::get('CACHE_RATE_LIMIT_ENABLED', true);
const CACHE_RATE_LIMIT_WINDOW = Env::get('CACHE_RATE_LIMIT_WINDOW', 60);
const CACHE_RATE_LIMIT_MAX = Env::get('CACHE_RATE_LIMIT_MAX', 1000);

// Debugging
const CACHE_DEBUG_ENABLED = Env::get('CACHE_DEBUG_ENABLED', Env::get('APP_DEBUG', false));
const CACHE_DEBUG_FILE = Env::get('CACHE_DEBUG_FILE', __DIR__ . '/../logs/cache_debug.log');

// Cache Providers
const CACHE_MEMCACHED_SERVERS = Env::get('CACHE_MEMCACHED_SERVERS', [
    ['host' => '127.0.0.1', 'port' => 11211, 'weight' => 100]
]);

const CACHE_REDIS_SERVERS = Env::get('CACHE_REDIS_SERVERS', [
    ['host' => Env::get('REDIS_HOST', '127.0.0.1'), 
     'port' => Env::get('REDIS_PORT', 6379), 
     'database' => Env::get('REDIS_DATABASE', 0), 
     'password' => Env::get('REDIS_PASSWORD', '')]
]);

// File Cache Settings
const CACHE_FILE_PATH = Env::get('CACHE_FILE_PATH', __DIR__ . '/../cache');
const CACHE_FILE_PERMISSIONS = Env::get('CACHE_FILE_PERMISSIONS', 0644);
const CACHE_DIR_PERMISSIONS = Env::get('CACHE_DIR_PERMISSIONS', 0755);

// Cache Strategy
const CACHE_STRATEGY = Env::get('CACHE_STRATEGY', 'LRU');
const CACHE_WARMUP_ENABLED = Env::get('CACHE_WARMUP_ENABLED', true);
const CACHE_WARMUP_INTERVAL = Env::get('CACHE_WARMUP_INTERVAL', 3600);
const CACHE_WARMUP_KEYS = Env::get('CACHE_WARMUP_KEYS', [
    'questions',
    'answers',
    'stats',
    'metrics'
]);

// Security and Validation
if (CACHE_ENABLED) {
    // Validate cache provider
    if (!in_array(CACHE_PROVIDER, ['redis', 'file', 'memcached'])) {
        trigger_error('Invalid cache provider configured', E_USER_WARNING);
    }
    
    // Validate cache strategy
    if (!in_array(CACHE_STRATEGY, ['LRU', 'LFU', 'FIFO'])) {
        trigger_error('Invalid cache strategy configured', E_USER_WARNING);
    }
    
    // Validate permissions
    if (!is_dir(CACHE_FILE_PATH)) {
        mkdir(CACHE_FILE_PATH, CACHE_DIR_PERMISSIONS, true);
    }
    
    if (!is_writable(CACHE_FILE_PATH)) {
        trigger_error('Cache directory is not writable', E_USER_WARNING);
    }
    
    // Validate Redis configuration
    if (CACHE_PROVIDER === 'redis') {
        foreach (CACHE_REDIS_SERVERS as $server) {
            if (!isset($server['host']) || !isset($server['port'])) {
                trigger_error('Invalid Redis server configuration', E_USER_WARNING);
            }
        }
    }
    
    // Validate Memcached configuration
    if (CACHE_PROVIDER === 'memcached') {
        foreach (CACHE_MEMCACHED_SERVERS as $server) {
            if (!isset($server['host']) || !isset($server['port'])) {
                trigger_error('Invalid Memcached server configuration', E_USER_WARNING);
            }
        }
    }
}

// Create cache directory with proper permissions
if (!file_exists(CACHE_FILE_PATH)) {
    mkdir(CACHE_FILE_PATH, CACHE_DIR_PERMISSIONS, true);
}

// Set proper permissions
chmod(CACHE_FILE_PATH, CACHE_DIR_PERMISSIONS);
]);

define('CACHE_TAGGING_ENABLED', true);
define('CACHE_TAG_PREFIX', 'tag_');
define('CACHE_TAG_TTL', 604800); // 7 days

define('CACHE_VERSIONING_ENABLED', true);
define('CACHE_VERSION_KEY', 'cache_version');
define('CACHE_VERSION_TTL', 86400); // 24 hours

define('CACHE_INTEGRITY_CHECK_ENABLED', true);
define('CACHE_INTEGRITY_CHECK_INTERVAL', 3600); // 1 hour

define('CACHE_DISTRIBUTION_ENABLED', false);
define('CACHE_DISTRIBUTION_SERVERS', [
    ['host' => '127.0.0.1', 'port' => 6379, 'weight' => 100]
]);

define('CACHE_REPLICATION_ENABLED', false);
define('CACHE_REPLICATION_FACTOR', 2);
define('CACHE_REPLICATION_STRATEGY', 'master-slave');

define('CACHE_CLUSTER_ENABLED', false);
define('CACHE_CLUSTER_NODES', [
    ['host' => '127.0.0.1', 'port' => 6379]
]);

define('CACHE_SHARDING_ENABLED', false);
define('CACHE_SHARDING_STRATEGY', 'consistent-hashing');
define('CACHE_SHARDING_SLOTS', 16384);

define('CACHE_COMPRESSION_THRESHOLD', 1024); // 1KB
define('CACHE_COMPRESSION_FORMAT', 'gzip');
define('CACHE_COMPRESSION_LEVEL', 6);

define('CACHE_ENCRYPTION_ENABLED', false);
define('CACHE_ENCRYPTION_KEY', '');
define('CACHE_ENCRYPTION_ALGORITHM', 'aes-256-cbc');

define('CACHE_PERSISTENCE_ENABLED', true);
define('CACHE_PERSISTENCE_INTERVAL', 3600); // 1 hour
define('CACHE_PERSISTENCE_FILE', __DIR__ . '/../cache/persistence');

define('CACHE_BACKUP_ENABLED', true);
define('CACHE_BACKUP_INTERVAL', 86400); // 24 hours
define('CACHE_BACKUP_PATH', __DIR__ . '/../cache/backups');
define('CACHE_BACKUP_RETENTION', 7); // days

define('CACHE_RECOVERY_ENABLED', true);
define('CACHE_RECOVERY_PATH', __DIR__ . '/../cache/recovery');
define('CACHE_RECOVERY_INTERVAL', 300); // 5 minutes

define('CACHE_MONITORING_ENABLED', true);
define('CACHE_MONITORING_INTERVAL', 60); // 1 minute
define('CACHE_MONITORING_METRICS', [
    'hits',
    'misses',
    'size',
    'usage',
    'evictions'
]);

define('CACHE_LOGGING_ENABLED', true);
define('CACHE_LOG_FILE', __DIR__ . '/../logs/cache.log');
define('CACHE_LOG_LEVEL', 'INFO');

define('CACHE_RATE_LIMIT_WINDOW', 60); // seconds
define('CACHE_RATE_LIMIT_MAX', 1000);
define('CACHE_RATE_LIMIT_BURST', 2000);
define('CACHE_RATE_LIMIT_RECOVER', 300); // 5 minutes
