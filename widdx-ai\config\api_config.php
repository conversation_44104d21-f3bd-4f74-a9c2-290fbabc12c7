<?php
// API Configuration
if (!defined('SECURE_API_CONFIG_LOADED')) {
    define('SECURE_API_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// API Settings
const API_ENABLED = Env::get('API_ENABLED', true);
const API_VERSION = Env::get('API_VERSION', 'v1');
const API_BASE_PATH = Env::get('API_BASE_PATH', '/api');
const API_SSL_REQUIRED = Env::get('API_SSL_REQUIRED', true);
const API_SSL_REDIRECT = Env::get('API_SSL_REDIRECT', true);

// Rate Limiting
const API_RATE_LIMIT_ENABLED = Env::get('API_RATE_LIMIT_ENABLED', true);
const API_RATE_LIMIT_WINDOW = Env::get('API_RATE_LIMIT_WINDOW', 60);
const API_RATE_LIMIT_MAX = Env::get('API_RATE_LIMIT_MAX', 100);
const API_RATE_LIMIT_BURST = Env::get('API_RATE_LIMIT_BURST', 200);
const API_RATE_LIMIT_RECOVER = Env::get('API_RATE_LIMIT_RECOVER', 300);
const API_RATE_LIMIT_STORAGE = Env::get('API_RATE_LIMIT_STORAGE', 'redis');

// Authentication
const API_AUTH_ENABLED = Env::get('API_AUTH_ENABLED', true);
const API_AUTH_METHOD = Env::get('API_AUTH_METHOD', 'jwt');
const API_AUTH_TOKEN_NAME = Env::get('API_AUTH_TOKEN_NAME', 'api_token');
const API_AUTH_COOKIE_NAME = Env::get('API_AUTH_COOKIE_NAME', 'api_auth');
const API_AUTH_TOKEN_TTL = Env::get('API_AUTH_TOKEN_TTL', 3600);
const API_AUTH_REFRESH_TTL = Env::get('API_AUTH_REFRESH_TTL', 86400);
const API_AUTH_COOKIE_SECURE = Env::get('API_AUTH_COOKIE_SECURE', true);
const API_AUTH_COOKIE_HTTPONLY = Env::get('API_AUTH_COOKIE_HTTPONLY', true);
const API_AUTH_COOKIE_SAMESITE = Env::get('API_AUTH_COOKIE_SAMESITE', 'Strict');
const API_AUTH_PASSWORD_MIN_LENGTH = Env::get('API_AUTH_PASSWORD_MIN_LENGTH', 8);
const API_AUTH_PASSWORD_MAX_LENGTH = Env::get('API_AUTH_PASSWORD_MAX_LENGTH', 128);
const API_AUTH_PASSWORD_REQUIRE_UPPERCASE = Env::get('API_AUTH_PASSWORD_REQUIRE_UPPERCASE', true);
const API_AUTH_PASSWORD_REQUIRE_LOWERCASE = Env::get('API_AUTH_PASSWORD_REQUIRE_LOWERCASE', true);
const API_AUTH_PASSWORD_REQUIRE_NUMBER = Env::get('API_AUTH_PASSWORD_REQUIRE_NUMBER', true);
const API_AUTH_PASSWORD_REQUIRE_SPECIAL = Env::get('API_AUTH_PASSWORD_REQUIRE_SPECIAL', true);
const API_AUTH_PASSWORD_SPECIAL_CHARS = Env::get('API_AUTH_PASSWORD_SPECIAL_CHARS', '!@#$%^&*()');

// Security
const API_SECURITY_ENABLED = Env::get('API_SECURITY_ENABLED', true);
const API_SECURITY_INPUT_VALIDATION = Env::get('API_SECURITY_INPUT_VALIDATION', true);
const API_SECURITY_OUTPUT_VALIDATION = Env::get('API_SECURITY_OUTPUT_VALIDATION', true);
const API_SECURITY_RATE_LIMITING = Env::get('API_SECURITY_RATE_LIMITING', true);
const API_SECURITY_API_KEY_VALIDATION = Env::get('API_SECURITY_API_KEY_VALIDATION', true);
const API_SECURITY_IP_RESTRICTIONS = Env::get('API_SECURITY_IP_RESTRICTIONS', true);
const API_SECURITY_REQUEST_VALIDATION = Env::get('API_SECURITY_REQUEST_VALIDATION', true);

// Logging
const API_LOGGING_ENABLED = Env::get('API_LOGGING_ENABLED', true);
const API_LOGGING_LEVEL = Env::get('API_LOGGING_LEVEL', 'INFO');
const API_LOGGING_FILE = Env::get('API_LOGGING_FILE', __DIR__ . '/../logs/api.log');
const API_LOGGING_FORMAT = Env::get('API_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');

// Metrics
const API_METRICS_ENABLED = Env::get('API_METRICS_ENABLED', true);
const API_METRICS_INTERVAL = Env::get('API_METRICS_INTERVAL', 60);
const API_METRICS_FILE = Env::get('API_METRICS_FILE', __DIR__ . '/../logs/api_metrics.log');
const API_METRICS_FORMAT = Env::get('API_METRICS_FORMAT', '{timestamp} {metric} {value}');

// Security and Validation
if (API_ENABLED) {
    // Validate base path
    if (!preg_match('/^\/[^\s]+$/', API_BASE_PATH)) {
        trigger_error('Invalid API base path configuration', E_USER_WARNING);
    }
    
    // Validate authentication settings
    if (API_AUTH_ENABLED && !in_array(API_AUTH_METHOD, ['jwt', 'session', 'basic'])) {
        trigger_error('Invalid authentication method configured', E_USER_WARNING);
    }
    
    // Validate password requirements
    if (API_AUTH_PASSWORD_MIN_LENGTH < 8 || API_AUTH_PASSWORD_MAX_LENGTH > 128) {
        trigger_error('Invalid password length requirements', E_USER_WARNING);
    }
    
    // Validate rate limiting
    if (API_RATE_LIMIT_MAX < 0 || API_RATE_LIMIT_WINDOW < 1) {
        trigger_error('Invalid rate limit configuration', E_USER_WARNING);
    }
    
    // Validate logging settings
    if (!in_array(API_LOGGING_LEVEL, ['DEBUG', 'INFO', 'WARNING', 'ERROR'])) {
        trigger_error('Invalid logging level configuration', E_USER_WARNING);
    }
    
    // Validate metrics interval
    if (API_METRICS_INTERVAL < 1) {
        trigger_error('Invalid metrics interval configuration', E_USER_WARNING);
    }
    
    // Create log directories with proper permissions
    $logDir = dirname(API_LOGGING_FILE);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    chmod($logDir, 0755);
}

// Create metrics directory with proper permissions
$metricsDir = dirname(API_METRICS_FILE);
if (!file_exists($metricsDir)) {
    mkdir($metricsDir, 0755, true);
}

chmod($metricsDir, 0755);

define('API_ERROR_HANDLING_ENABLED', true);
define('API_ERROR_RETRY_ENABLED', true);
define('API_ERROR_RETRY_MAX_ATTEMPTS', 3);
define('API_ERROR_RETRY_DELAY', 1000);
define('API_ERROR_RETRY_BACKOFF', 2);
define('API_ERROR_LOGGING_ENABLED', true);
define('API_ERROR_LOGGING_LEVEL', 'ERROR');

define('API_CORS_ENABLED', true);
define('API_CORS_ALLOW_ORIGIN', ['https://widdx.ai']);
define('API_CORS_ALLOW_METHODS', ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']);
define('API_CORS_ALLOW_HEADERS', ['Content-Type', 'Authorization', 'X-Requested-With']);
define('API_CORS_ALLOW_CREDENTIALS', true);
define('API_CORS_EXPOSE_HEADERS', ['Content-Length', 'Content-Type']);
define('API_CORS_MAX_AGE', 3600);

define('API_IP_WHITELIST_ENABLED', true);
define('API_IP_WHITELIST', [
    '127.0.0.1',
    '::1'
]);
define('API_IP_BLACKLIST_ENABLED', true);
define('API_IP_BLACKLIST', []);
define('API_IP_LOGGING_ENABLED', true);
define('API_IP_LOGGING_FILE', __DIR__ . '/../logs/api_ip.log');

define('API_REQUEST_VALIDATION_ENABLED', true);
define('API_REQUEST_MAX_HEADERS', 100);
define('API_REQUEST_MAX_BODY_SIZE', 10485760);
define('API_REQUEST_TIMEOUT', 30);
define('API_REQUEST_LOGGING_ENABLED', true);
define('API_REQUEST_LOGGING_FILE', __DIR__ . '/../logs/api_requests.log');

define('API_SESSION_ENABLED', true);
define('API_SESSION_NAME', 'api_session');
define('API_SESSION_LIFETIME', 3600);
define('API_SESSION_COOKIE_SECURE', true);
define('API_SESSION_COOKIE_HTTPONLY', true);
define('API_SESSION_COOKIE_SAMESITE', 'Strict');
define('API_SESSION_REGENERATE', true);
define('API_SESSION_REGENERATE_INTERVAL', 300);

define('API_API_KEYS', [
    'DEEPSEEK' => [
        'enabled' => true,
        'required' => true,
        'validate' => true,
        'rotate' => true
    ],
    'GEMINI' => [
        'enabled' => true,
        'required' => true,
        'validate' => true,
        'rotate' => true
    ]
]);

define('API_IP_RESTRICTIONS', [
    'whitelist' => [
        'enabled' => true,
        'ips' => ['127.0.0.1', '::1']
    ],
    'blacklist' => [
        'enabled' => true,
        'ips' => []
    ]
]);

define('API_REQUEST_VALIDATION', [
    'enabled' => true,
    'max_headers' => 100,
    'max_body_size' => 10485760,
    'timeout' => 30,
    'validate_content_type' => true,
    'validate_content_length' => true,
    'validate_headers' => true
]);

define('API_ERROR_MESSAGES', [
    'generic' => 'حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
    'timeout' => 'تمت معالجة الطلب لفترة طويلة. يرجى المحاولة مرة أخرى.',
    'rate_limit' => 'تم تجاوز حد معدل الطلبات. يرجى الانتظار قليلاً.',
    'invalid_input' => 'المدخلات غير صالحة. يرجى التأكد من صحة المدخلات.',
    'api_error' => 'حدث خطأ في خدمة API. يرجى المحاولة مرة أخرى.',
    'authentication' => 'فشل التحقق من الهوية. يرجى التأكد من بيانات تسجيل الدخول.',
    'permission' => 'ليس لديك صلاحيات كافية للوصول إلى هذه الميزة.',
    'not_found' => 'لم يتم العثور على المورد المطلوب.',
    'server_error' => 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.'
]);

define('API_ROUTES', [
    'GET' => [
        'health' => [
            'path' => '/health',
            'controller' => 'HealthController',
            'method' => 'checkHealth',
            'auth_required' => false,
            'rate_limit' => 1000
        ],
        'metrics' => [
            'path' => '/metrics',
            'controller' => 'MetricsController',
            'method' => 'getMetrics',
            'auth_required' => true,
            'rate_limit' => 100
        ],
        'analytics' => [
            'path' => '/analytics',
            'controller' => 'AnalyticsController',
            'method' => 'getAnalytics',
            'auth_required' => true,
            'rate_limit' => 100
        ]
    ],
    'POST' => [
        'ask' => [
            'path' => '/ask',
            'controller' => 'AIService',
            'method' => 'processQuestion',
            'auth_required' => true,
            'rate_limit' => 100
        ],
        'auth' => [
            'path' => '/auth/login',
            'controller' => 'AuthController',
            'method' => 'login',
            'auth_required' => false,
            'rate_limit' => 1000
        ]
    ]
]);
