# WIDDX Theme and Lahza Payment Gateway - Configuration Guide

## 📋 Table of Contents

1. [Theme Configuration](#theme-configuration)
2. [Payment Gateway Configuration](#payment-gateway-configuration)
3. [3D Secure Configuration](#3d-secure-configuration)
4. [Order Form Configuration](#order-form-configuration)
5. [Security Configuration](#security-configuration)
6. [Performance Optimization](#performance-optimization)
7. [Accessibility Configuration](#accessibility-configuration)
8. [Monitoring and Logging](#monitoring-and-logging)

## 🎨 Theme Configuration

### Basic Theme Settings

#### WHMCS Admin Configuration
Navigate to **Setup → General Settings → General**

| Setting | Recommended Value | Description |
|---------|------------------|-------------|
| Template | `WIDDX` | Main theme template |
| Admin Template | `blend` | Admin area template |
| Language | `english` | Default language |
| Date Format | `DD/MM/YYYY` | Date display format |

#### Theme Customization

1. **Color Scheme Customization**
   ```css
   /* Edit: templates/WIDDX/css/theme.css */
   :root {
     --widdx-primary: #2c5aa0;      /* Primary brand color */
     --widdx-secondary: #1e3d6f;    /* Secondary brand color */
     --widdx-accent: #4a90e2;       /* Accent color */
     --widdx-success: #27ae60;      /* Success messages */
     --widdx-warning: #f39c12;      /* Warning messages */
     --widdx-danger: #e74c3c;       /* Error messages */
   }
   ```

2. **Logo Configuration**
   ```php
   // Upload logo via WHMCS Admin
   // Setup → General Settings → General → Logo URL
   // Recommended size: 200x50px (PNG with transparency)
   ```

3. **Typography Settings**
   ```css
   /* Custom fonts in templates/WIDDX/css/theme.css */
   :root {
     --widdx-font-primary: 'Inter', sans-serif;
     --widdx-font-secondary: 'Poppins', sans-serif;
   }
   ```

### Advanced Theme Configuration

#### Custom CSS
Create `templates/WIDDX/css/custom.css` for additional customizations:

```css
/* Custom branding */
.navbar-brand {
    font-size: 1.8rem;
    font-weight: 700;
}

/* Custom button styles */
.btn-custom {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

/* Custom card styling */
.custom-card {
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}
```

#### JavaScript Customization
Add custom functionality in `templates/WIDDX/js/custom.js`:

```javascript
// Custom theme functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
    
    // Custom form validation
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});
```

## 💳 Payment Gateway Configuration

### Basic Gateway Settings

#### Lahza API Configuration
Navigate to **Setup → Payment Gateways → Lahza Payment Gateway**

| Setting | Production | Test Mode | Description |
|---------|------------|-----------|-------------|
| Display Name | `Credit/Debit Card` | `Test Payment` | Customer-facing name |
| Public Key | `pk_live_xxxxx` | `pk_test_xxxxx` | Lahza public key |
| Secret Key | `sk_live_xxxxx` | `sk_test_xxxxx` | Lahza secret key |
| Webhook Secret | `whsec_xxxxx` | `whsec_test_xxxxx` | Webhook verification |
| Test Mode | `No` | `Yes` | Environment setting |

#### Currency Configuration

```php
// Supported currencies
$supportedCurrencies = [
    'USD' => 'US Dollar',
    'EUR' => 'Euro', 
    'GBP' => 'British Pound',
    'ILS' => 'Israeli Shekel',
    'JOD' => 'Jordanian Dinar'
];
```

### Advanced Gateway Settings

#### Transaction Limits
```php
// Configure in modules/gateways/lahza.php
$transactionLimits = [
    'min_amount' => 1.00,      // Minimum transaction amount
    'max_amount' => 10000.00,  // Maximum transaction amount
    'daily_limit' => 50000.00, // Daily processing limit
];
```

#### Retry Configuration
```php
// Payment retry settings
$retryConfig = [
    'max_retries' => 3,        // Maximum retry attempts
    'retry_delay' => 300,      // Delay between retries (seconds)
    'exponential_backoff' => true, // Use exponential backoff
];
```

## 🔐 3D Secure Configuration

### Basic 3DS Settings

#### Enable 3D Secure
Navigate to **Setup → Payment Gateways → Lahza Payment Gateway**

| Setting | Recommended | Description |
|---------|-------------|-------------|
| 3D Secure Authentication | `Yes` | Enable 3DS for all transactions |
| 3DS Timeout | `300` | Authentication timeout (seconds) |
| Challenge Window | `Full Screen` | Challenge display mode |
| Fallback Behavior | `Continue` | Action when 3DS unavailable |

#### 3DS Rules Configuration

```php
// Configure 3DS rules in modules/gateways/lahza.php
$threeDSRules = [
    'force_3ds_amount' => 100.00,     // Force 3DS above this amount
    'skip_3ds_countries' => [],       // Countries to skip 3DS
    'require_3ds_countries' => ['US', 'GB'], // Countries requiring 3DS
    'merchant_risk_indicator' => 'low', // Risk assessment
];
```

### Advanced 3DS Configuration

#### Browser Information Collection
```javascript
// Enhanced browser fingerprinting
const browserInfo = {
    colorDepth: screen.colorDepth,
    screenHeight: screen.height,
    screenWidth: screen.width,
    timeZoneOffset: new Date().getTimezoneOffset(),
    language: navigator.language,
    javaEnabled: navigator.javaEnabled(),
    userAgent: navigator.userAgent
};
```

#### Challenge Customization
```css
/* 3DS challenge styling */
.lahza-3ds-container {
    max-width: 600px;
    margin: 2rem auto;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.lahza-3ds-header {
    background: var(--widdx-gradient-primary);
    color: white;
    padding: 2rem;
    text-align: center;
}
```

## 🛒 Order Form Configuration

### Basic Order Form Settings

#### WIDDX Modern Order Form
Navigate to **Setup → General Settings → Ordering**

| Setting | Recommended | Description |
|---------|-------------|-------------|
| Order Form Template | `WIDDX Modern` | Use WIDDX order form |
| Show Sidebar | `Yes` | Display category sidebar |
| Enable Animations | `Yes` | Smooth animations |
| Progress Indicator | `Yes` | Show checkout progress |
| Real-time Validation | `Yes` | Validate forms instantly |

#### Product Display Configuration

```php
// Product grid settings
$productGridConfig = [
    'columns_desktop' => 3,    // Products per row on desktop
    'columns_tablet' => 2,     // Products per row on tablet  
    'columns_mobile' => 1,     // Products per row on mobile
    'show_features' => true,   // Display product features
    'show_pricing' => true,    // Display pricing information
    'enable_comparison' => true, // Enable product comparison
];
```

### Advanced Order Form Configuration

#### Custom Product Categories
```php
// Category icons and styling
$categoryConfig = [
    'hosting' => [
        'icon' => 'fas fa-server',
        'color' => '#2c5aa0',
        'description' => 'Reliable web hosting solutions'
    ],
    'domains' => [
        'icon' => 'fas fa-globe',
        'color' => '#27ae60', 
        'description' => 'Domain registration and management'
    ],
    'ssl' => [
        'icon' => 'fas fa-shield-alt',
        'color' => '#e74c3c',
        'description' => 'SSL certificates for security'
    ]
];
```

#### Trust Indicators
```html
<!-- Custom trust badges -->
<div class="trust-indicators">
    <div class="trust-item">
        <i class="fas fa-shield-alt"></i>
        <span>SSL Secured</span>
    </div>
    <div class="trust-item">
        <i class="fas fa-headset"></i>
        <span>24/7 Support</span>
    </div>
    <div class="trust-item">
        <i class="fas fa-undo"></i>
        <span>30-Day Guarantee</span>
    </div>
</div>
```

## 🛡️ Security Configuration

### SSL and HTTPS Configuration

#### Force HTTPS
```apache
# .htaccess configuration
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

#### Security Headers
```apache
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
```

### Rate Limiting Configuration

```php
// Rate limiting settings
$rateLimitConfig = [
    'requests_per_minute' => 60,    // Requests per minute per IP
    'requests_per_hour' => 1000,    // Requests per hour per IP
    'burst_limit' => 10,            // Burst request limit
    'whitelist_ips' => [            // Whitelisted IP addresses
        '127.0.0.1',
        '::1'
    ]
];
```

### Input Validation Rules

```php
// Validation rules
$validationRules = [
    'invoice_id' => [
        'type' => 'integer',
        'min' => 1,
        'max' => 999999999
    ],
    'amount' => [
        'type' => 'decimal',
        'min' => 0.01,
        'max' => 99999.99,
        'precision' => 2
    ],
    'email' => [
        'type' => 'email',
        'max_length' => 255
    ]
];
```

## ⚡ Performance Optimization

### Caching Configuration

#### Browser Caching
```apache
# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

#### Compression
```apache
# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### Database Optimization

```sql
-- Performance indexes
ALTER TABLE tblinvoices ADD INDEX idx_status_date (status, date);
ALTER TABLE tblaccounts ADD INDEX idx_gateway_date (gateway, date);
ALTER TABLE tblclients ADD INDEX idx_status_email (status, email);

-- Transaction log optimization
ALTER TABLE tblgatewaylog ADD INDEX idx_gateway_date (gateway, date);
ALTER TABLE tblgatewaylog ADD INDEX idx_result_date (result, date);
```

### CDN Configuration

```html
<!-- CDN resources -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
<link rel="dns-prefetch" href="//api.lahza.io">
```

## ♿ Accessibility Configuration

### WCAG 2.1 Compliance Settings

#### Screen Reader Support
```html
<!-- ARIA landmarks -->
<header role="banner">
<nav role="navigation" aria-label="Main navigation">
<main role="main" id="main-content">
<aside role="complementary" aria-label="Sidebar">
<footer role="contentinfo">
```

#### Keyboard Navigation
```css
/* Focus indicators */
*:focus {
    outline: 2px solid var(--widdx-primary);
    outline-offset: 2px;
}

/* Skip links */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--widdx-primary);
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
}

.skip-link:focus {
    top: 0;
}
```

#### Color Contrast
```css
/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --widdx-primary: #000080;
        --widdx-secondary: #000000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
```

## 📊 Monitoring and Logging

### Log Configuration

#### Log Levels
```php
// Logging configuration
$logConfig = [
    'level' => 'INFO',              // DEBUG, INFO, WARNING, ERROR, CRITICAL
    'max_file_size' => 5242880,     // 5MB
    'max_files' => 10,              // Keep 10 rotated files
    'log_categories' => [
        'general',
        'transactions', 
        'security',
        '3ds',
        'api'
    ]
];
```

#### Log Rotation
```bash
# Logrotate configuration
/path/to/whmcs/modules/gateways/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### Performance Monitoring

```javascript
// Performance monitoring
window.addEventListener('load', function() {
    if (window.performance && window.performance.timing) {
        const loadTime = window.performance.timing.loadEventEnd - 
                        window.performance.timing.navigationStart;
        
        // Send to analytics
        if (window.gtag) {
            gtag('event', 'timing_complete', {
                name: 'page_load',
                value: loadTime
            });
        }
    }
});
```

---

**Next Steps**: After configuration, proceed to the [User Guide](USER_GUIDE.md) for detailed usage instructions.
