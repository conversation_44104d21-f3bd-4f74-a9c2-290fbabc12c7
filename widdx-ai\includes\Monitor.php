<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Cache.php';
require_once __DIR__ . '/ErrorHandler.php';

class Monitor {
    private $db;
    private $cache;
    private $errorHandler;
    private $metrics = [];

    public function __construct() {
        $this->db = new Database();
        $this->cache = new Cache();
        $this->errorHandler = new ErrorHandler(false);
        
        // Start monitoring
        $this->startMonitoring();
    }

    private function startMonitoring() {
        // Start tracking metrics
        $this->metrics['start_time'] = microtime(true);
        $this->metrics['memory_start'] = memory_get_usage();
        
        // Register shutdown function
        register_shutdown_function([$this, 'logMetrics']);
    }

    public function trackRequest($requestType, $duration = null) {
        $this->metrics['requests'][] = [
            'type' => $requestType,
            'timestamp' => time(),
            'duration' => $duration ?: (microtime(true) - $this->metrics['start_time']) * 1000
        ];
    }

    public function trackError($error) {
        $this->metrics['errors'][] = [
            'message' => $error->getMessage(),
            'type' => get_class($error),
            'timestamp' => time(),
            'trace' => $error->getTraceAsString()
        ];
    }

    public function trackMemoryUsage() {
        $this->metrics['memory_usage'][] = [
            'timestamp' => time(),
            'usage' => memory_get_usage(true)
        ];
    }

    public function trackCacheStats() {
        $cacheStats = $this->cache->getCacheStats();
        $this->metrics['cache_stats'][] = [
            'timestamp' => time(),
            'stats' => $cacheStats
        ];
    }

    public function logMetrics() {
        try {
            $totalDuration = (microtime(true) - $this->metrics['start_time']) * 1000;
            $memoryUsage = memory_get_usage(true) - $this->metrics['memory_start'];

            $metrics = [
                'timestamp' => time(),
                'total_duration' => $totalDuration,
                'memory_usage' => $memoryUsage,
                'requests' => $this->metrics['requests'] ?? [],
                'errors' => $this->metrics['errors'] ?? [],
                'cache_stats' => $this->metrics['cache_stats'] ?? [],
                'php_version' => PHP_VERSION,
                'memory_limit' => ini_get('memory_limit')
            ];

            // Log to database
            $stmt = $this->db->getConnection()->prepare("
                INSERT INTO metrics (data)
                VALUES (:data)
            ");
            
            $stmt->execute([
                ':data' => json_encode($metrics)
            ]);

            // Log to file
            $logFile = __DIR__ . '/../logs/monitor.log';
            file_put_contents($logFile, json_encode($metrics) . "\n", FILE_APPEND);

        } catch (Exception $e) {
            error_log("Monitoring error: " . $e->getMessage());
        }
    }

    public function getSystemStatus() {
        $status = [
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'uptime' => time() - $this->metrics['start_time'],
            'requests' => count($this->metrics['requests'] ?? []),
            'errors' => count($this->metrics['errors'] ?? []),
            'cache' => $this->cache->getCacheStats()
        ];

        return $status;
    }

    public function getRecentErrors($limit = 10) {
        try {
            $stmt = $this->db->getConnection()->prepare("
                SELECT * FROM logs 
                WHERE log_type = 'ERROR' 
                ORDER BY created_at DESC 
                LIMIT :limit
            ");
            
            $stmt->execute([':limit' => $limit]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    public function getPerformanceMetrics($minutes = 60) {
        try {
            $stmt = $this->db->getConnection()->prepare("
                SELECT * FROM metrics 
                WHERE created_at > DATE_SUB(NOW(), INTERVAL :minutes MINUTE)
                ORDER BY created_at DESC
            ");
            
            $stmt->execute([':minutes' => $minutes]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    public function getCacheStats() {
        return $this->cache->getCacheStats();
    }

    public function getDatabaseStats() {
        try {
            $stmt = $this->db->getConnection()->prepare("
                SELECT 
                    COUNT(*) as total_questions,
                    COUNT(DISTINCT question_text) as unique_questions,
                    AVG(LENGTH(answer_text)) as avg_answer_length,
                    MAX(created_at) as last_updated
                FROM questions
            ");
            
            $stmt->execute();
            return $stmt->fetch();
        } catch (Exception $e) {
            return [];
        }
    }
}
