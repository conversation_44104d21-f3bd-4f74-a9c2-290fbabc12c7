<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test the chat API endpoint
echo "<h2>Testing Chat API</h2>";

// Test data
$testData = [
    'question' => 'مرحبا',
    'context' => 'test context'
];

// Initialize cURL
$ch = curl_init('http://localhost/widdx-ai/api/ask.php');

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

// Enable verbose output for debugging
curl_setopt($ch, CURLOPT_VERBOSE, true);
$verbose = fopen('php://temp', 'w+');
curl_setopt($ch, CURLOPT_STDERR, $verbose);

// Execute the request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// Get verbose output
rewind($verbose);
$verboseLog = stream_get_contents($verbose);

// Display results
echo "<h3>Request Details:</h3>";
echo "<pre>" . htmlspecialchars(print_r($testData, true)) . "</pre>";

echo "<h3>HTTP Status: $httpCode</h3>";

echo "<h3>Response:</h3>";
if ($response === false) {
    echo "<p style='color:red;'>cURL Error: " . curl_error($ch) . "</p>";
} else {
    // Try to decode JSON response
    $decoded = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<pre>" . htmlspecialchars(print_r($decoded, true)) . "</pre>";
    } else {
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
}

// Display verbose output for debugging
echo "<h3>cURL Verbose Output:</h3>";
echo "<pre>" . htmlspecialchars($verboseLog) . "</pre>";

// Close cURL resource
curl_close($ch);
?>
