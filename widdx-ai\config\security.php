<?php
// Security Configuration
if (!defined('SECURE_SECURITY_CONFIG_LOADED')) {
    define('SECURE_SECURITY_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// Security Headers
header('X-XSS-Protection: 1; mode=block');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: SAMEORIGIN');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self'');

// CSRF Protection
session_start();
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    
    // Set secure session settings
    ini_set('session.use_strict_mode', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', Env::get('APP_ENV') === 'production');
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_samesite', 'Lax');
}

// Input Validation and Sanitization
function sanitize_input($data) {
    if (is_array($data)) {
        return array_map('sanitize_input', $data);
    }
    
    $data = trim($data);
    $data = filter_var($data, FILTER_SANITIZE_STRING);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function is_valid_question($question) {
    // Basic validation
    if (!is_string($question)) {
        return false;
    }
    
    // Length validation
    $minLength = Env::get('MIN_QUESTION_LENGTH', 3);
    $maxLength = Env::get('MAX_QUESTION_LENGTH', 2000);
    
    if (strlen($question) < $minLength || strlen($question) > $maxLength) {
        return false;
    }
    
    // Content validation
    if (preg_match('/[<>]/', $question)) {
        return false;
    }
    
    // SQL injection prevention
    if (preg_match('/(select|update|delete|drop|union|insert)/i', $question)) {
        return false;
    }
    
    // XSS prevention
    if (preg_match('/<script|on[a-z]+|javascript:|alert\(|confirm\(|prompt\(/i', $question)) {
        return false;
    }
    
    return true;
}

// Rate Limiting Configuration
const MAX_REQUESTS_PER_MINUTE = Env::get('MAX_REQUESTS_PER_MINUTE', 60);
const RATE_LIMIT_WINDOW = Env::get('RATE_LIMIT_WINDOW', 60); // seconds
const RATE_LIMIT_BURST = Env::get('RATE_LIMIT_BURST', 3); // burst limit

function check_rate_limit() {
    try {
        $ip = filter_var($_SERVER['REMOTE_ADDR'], FILTER_VALIDATE_IP);
        if (!$ip) {
            throw new Exception('Invalid IP address');
        }
        
        $key = "rate_limit_{$ip}";
        
        // Use Redis for rate limiting
        $redis = new Redis();
        $redisHost = Env::get('REDIS_HOST', '127.0.0.1');
        $redisPort = Env::get('REDIS_PORT', 6379);
        
        if (!$redis->connect($redisHost, $redisPort)) {
            throw new Exception('Redis connection failed');
        }
        
        // Get current count and increment
        $current_count = (int)$redis->get($key) ?: 0;
        $redis->incr($key);
        
        // Set expiration
        $redis->expire($key, RATE_LIMIT_WINDOW);
        
        // Check limits
        if ($current_count >= MAX_REQUESTS_PER_MINUTE) {
            throw new Exception('Too many requests. Please try again later.');
        }
        
        // Check burst limit
        if ($current_count >= RATE_LIMIT_BURST) {
            // Add a small delay for burst protection
            usleep(100000); // 100ms delay
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Rate limit check failed: " . $e->getMessage());
        throw $e;
    }
}
    
    $redis->incr($key);
    $redis->expire($key, RATE_LIMIT_WINDOW);
}

// API key validation
function validate_api_key($key) {
    // In production, these should be stored in a secure database
    $valid_keys = [
        'DEEPSEEK' => API_KEY_DEEPSEEK,
        'GEMINI' => API_KEY_GEMINI
    ];
    
    return isset($valid_keys[$key]) && !empty($valid_keys[$key]);
}
