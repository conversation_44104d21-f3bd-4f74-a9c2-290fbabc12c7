[2025-06-28 00:50:11] [HEADER] === Starting Professional Payment Test Suite ===
[2025-06-28 00:50:11] [INFO] Test started at: 2025-06-28 00:50:11
[2025-06-28 00:50:11] [INFO] Log file: C:\xampp\htdocs\tests\lahza/payment_test_20250628_005011.log
[2025-06-28 00:50:11] [TEST] 🚀 Starting test: Individual customer with successful payment
[2025-06-28 00:50:11] [INFO]    ℹ️ Creating individual customer
[2025-06-28 00:50:11] [DETAIL]       Details: {
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "phone": "+966501234567",
    "address1": "شارع الملك فهد",
    "city": "الرياض",
    "state": "الرياض",
    "postcode": "12345",
    "country": "SA",
    "currency": "SAR"
}
[2025-06-28 00:50:11] [SUCCESS]    ✅ Customer data validated successfully
[2025-06-28 00:50:11] [INFO]    ℹ️ Creating test invoice #INV-1751064611-4D1B2E for 599.04 SAR
[2025-06-28 00:50:11] [INFO]    ℹ️ Processing payment with Visa card ending with 1111
[2025-06-28 00:50:12] [SUCCESS]    ✅ Payment processed with status: succeeded
[2025-06-28 00:50:12] [DETAIL]       Details: {
    "success": true,
    "status": "succeeded",
    "transaction_id": "TXN_1751064612_5182",
    "amount": 599.04,
    "currency": "SAR",
    "card": {
        "type": "Visa",
        "last4": "1111"
    },
    "customer": {
        "name": "أحمد محمد",
        "email": "<EMAIL>"
    },
    "timestamp": "2025-06-28T00:50:12+02:00"
}
[2025-06-28 00:50:12] [INFO]    ℹ️ Updated invoice status to Paid via addInvoicePayment
[2025-06-28 00:50:12] [INFO]    ℹ️ Verifying invoice status. Expected: Paid, Actual: Paid
[2025-06-28 00:50:12] [SUCCESS]    ✅ Invoice status should be Paid after payment
[2025-06-28 00:50:12] [PASS] ✅ Test passed: Individual customer with successful payment (0.61s)
[2025-06-28 00:50:12] [SEPARATOR] 
[2025-06-28 00:50:12] [TEST] 🚀 Starting test: Business customer with insufficient funds
[2025-06-28 00:50:12] [INFO]    ℹ️ Creating business customer
[2025-06-28 00:50:12] [DETAIL]       Details: {
    "company_name": "شركة التقنية المتطورة",
    "first_name": "سارة",
    "last_name": "علي",
    "email": "<EMAIL>",
    "phone": "+966502345678",
    "address1": "حي العليا",
    "city": "جدة",
    "state": "مكة المكرمة",
    "postcode": "23451",
    "country": "SA",
    "currency": "SAR",
    "tax_id": "1234567890"
}
[2025-06-28 00:50:12] [SUCCESS]    ✅ Customer data validated successfully
[2025-06-28 00:50:12] [INFO]    ℹ️ Creating test invoice #INV-1751064612-0FC518 for 996.01 SAR
[2025-06-28 00:50:12] [INFO]    ℹ️ Processing payment with Visa card ending with 9995
[2025-06-28 00:50:13] [ERROR]    ❌ Payment processed with status: declined
[2025-06-28 00:50:13] [DETAIL]       Details: {
    "success": false,
    "status": "declined",
    "transaction_id": "TXN_1751064613_6317",
    "decline_code": "insufficient_funds",
    "message": "Insufficient funds",
    "timestamp": "2025-06-28T00:50:13+02:00"
}
[2025-06-28 00:50:13] [INFO]    ℹ️ Verifying invoice status. Expected: Unpaid, Actual: Unpaid
[2025-06-28 00:50:13] [SUCCESS]    ✅ Invoice status should be Unpaid after payment
[2025-06-28 00:50:13] [PASS] ✅ Test passed: Business customer with insufficient funds (1.45s)
[2025-06-28 00:50:13] [SEPARATOR] 
[2025-06-28 00:50:13] [SUMMARY] 
=== Test Summary ===
Total tests: 2
✅ Passed: 2
❌ Failed: 0
⏱️  Duration: 2.07s
Test completed at: 2025-06-28 00:50:13
Log file: C:\xampp\htdocs\tests\lahza/payment_test_20250628_005011.log

[2025-06-28 00:50:13] [RESULT] ✅ Individual customer with successful payment (0.61s)
[2025-06-28 00:50:13] [RESULT] ✅ Business customer with insufficient funds (1.45s)
