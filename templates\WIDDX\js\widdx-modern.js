/**
 * WIDDX Modern JavaScript Features
 * Enhanced user experience and modern interactions
 * 
 * @package    WIDDX Theme
 * <AUTHOR> Development Team
 * @version    1.0.0
 */

(function() {
    'use strict';

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initScrollAnimations();
        initSmoothScrolling();
        initFormEnhancements();
        initLoadingStates();
        initAccessibilityFeatures();
        initPerformanceOptimizations();
    });

    /**
     * Initialize scroll-based animations
     */
    function initScrollAnimations() {
        // Intersection Observer for scroll reveals
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe all scroll reveal elements
        document.querySelectorAll('.widdx-scroll-reveal').forEach(function(el) {
            observer.observe(el);
        });

        // Parallax effect for hero sections
        const heroElements = document.querySelectorAll('.widdx-hero-section');
        if (heroElements.length > 0) {
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                
                heroElements.forEach(function(hero) {
                    hero.style.transform = `translateY(${rate}px)`;
                });
            });
        }
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(function(anchor) {
            anchor.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    e.preventDefault();
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Enhance form interactions
     */
    function initFormEnhancements() {
        // Add floating labels
        document.querySelectorAll('.widdx-form-control').forEach(function(input) {
            const wrapper = document.createElement('div');
            wrapper.className = 'widdx-form-group';
            input.parentNode.insertBefore(wrapper, input);
            wrapper.appendChild(input);

            // Add focus/blur handlers
            input.addEventListener('focus', function() {
                wrapper.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!input.value) {
                    wrapper.classList.remove('focused');
                }
            });

            // Check initial state
            if (input.value) {
                wrapper.classList.add('focused');
            }
        });

        // Real-time validation
        document.querySelectorAll('input[type="email"]').forEach(function(input) {
            input.addEventListener('input', function() {
                const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.value);
                input.classList.toggle('valid', isValid && input.value.length > 0);
                input.classList.toggle('invalid', !isValid && input.value.length > 0);
            });
        });
    }

    /**
     * Initialize loading states and skeleton screens
     */
    function initLoadingStates() {
        // Add loading states to buttons
        document.querySelectorAll('.btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                if (this.type === 'submit' || this.classList.contains('widdx-loading-btn')) {
                    this.classList.add('widdx-loading');
                    this.disabled = true;
                    
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                    
                    // Reset after 5 seconds (fallback)
                    setTimeout(() => {
                        this.classList.remove('widdx-loading');
                        this.disabled = false;
                        this.innerHTML = originalText;
                    }, 5000);
                }
            });
        });

        // Skeleton loading for dynamic content
        function showSkeleton(container) {
            container.classList.add('widdx-loading');
            container.innerHTML = '<div class="widdx-shimmer" style="height: 20px; margin: 10px 0;"></div>'.repeat(3);
        }

        function hideSkeleton(container, content) {
            container.classList.remove('widdx-loading');
            container.innerHTML = content;
        }

        // Expose globally for use in other scripts
        window.widdxLoading = { showSkeleton, hideSkeleton };
    }

    /**
     * Accessibility enhancements
     */
    function initAccessibilityFeatures() {
        // Keyboard navigation for custom elements
        document.querySelectorAll('.widdx-interactive').forEach(function(el) {
            if (!el.hasAttribute('tabindex')) {
                el.setAttribute('tabindex', '0');
            }

            el.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });

        // Focus management for modals
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const closeBtn = openModal.querySelector('[data-dismiss="modal"]');
                    if (closeBtn) closeBtn.click();
                }
            }
        });

        // Announce dynamic content changes to screen readers
        function announceToScreenReader(message) {
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.setAttribute('aria-atomic', 'true');
            announcement.className = 'widdx-sr-only';
            announcement.textContent = message;
            
            document.body.appendChild(announcement);
            
            setTimeout(() => {
                document.body.removeChild(announcement);
            }, 1000);
        }

        window.widdxA11y = { announceToScreenReader };
    }

    /**
     * Performance optimizations
     */
    function initPerformanceOptimizations() {
        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('widdx-lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(function(img) {
                img.classList.add('widdx-lazy');
                imageObserver.observe(img);
            });
        }

        // Debounced scroll handler
        let scrollTimeout;
        function handleScroll() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                // Scroll-based operations here
                updateScrollProgress();
            }, 16); // ~60fps
        }

        window.addEventListener('scroll', handleScroll, { passive: true });

        // Update scroll progress indicator
        function updateScrollProgress() {
            const progressBar = document.querySelector('.widdx-scroll-progress');
            if (progressBar) {
                const scrollTop = window.pageYOffset;
                const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                progressBar.style.width = scrollPercent + '%';
            }
        }

        // Preload critical resources
        function preloadResource(href, as = 'fetch') {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = href;
            link.as = as;
            document.head.appendChild(link);
        }

        // Preload common resources
        preloadResource('/templates/WIDDX/css/theme.min.css', 'style');
        preloadResource('/templates/WIDDX/js/scripts.min.js', 'script');
    }

    /**
     * Payment gateway enhancements
     */
    function initPaymentEnhancements() {
        // Enhance Lahza payment buttons
        document.querySelectorAll('.payment-gateway-lahza .btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                // Add loading state
                this.classList.add('widdx-loading');
                this.innerHTML = '<i class="fas fa-credit-card"></i> Redirecting to Lahza...';
                
                // Announce to screen readers
                if (window.widdxA11y) {
                    window.widdxA11y.announceToScreenReader('Redirecting to secure payment gateway');
                }
            });
        });

        // Form validation for payment forms
        document.querySelectorAll('form[action*="payment"]').forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        field.classList.add('invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    if (window.widdxA11y) {
                        window.widdxA11y.announceToScreenReader('Please fill in all required fields');
                    }
                }
            });
        });
    }

    // Initialize payment enhancements
    initPaymentEnhancements();

    // Expose utilities globally
    window.WIDDX = {
        version: '1.0.0',
        utils: {
            loading: window.widdxLoading,
            a11y: window.widdxA11y
        }
    };

})();
