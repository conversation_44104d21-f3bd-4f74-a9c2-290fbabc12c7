<?php
/**
 * Professional Payment Gateway Test Suite
 * 
 * This script provides comprehensive testing of the Lahza payment gateway integration,
 * including customer data submission, payment processing, and invoice status verification.
 */

require_once __DIR__ . '/test_config.php';
require_once __DIR__ . '/mock_whmcs.php';

class ProfessionalPaymentTest {
    private $testResults = [];
    private $currentTest = '';
    private $startTime;
    private $testCount = 0;
    private $passedCount = 0;
    private $failedCount = 0;
    private $testCards = [];
    private $testCustomers = [];
    private $config;
    private $logFile;

    public function __construct() {
        // Set error reporting
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        
        // Initialize configuration
        $this->config = require __DIR__ . '/test_config.php';
        $this->logFile = __DIR__ . '/payment_test_' . date('Ymd_His') . '.log';
        
        // Define test cards
        $this->testCards = [
            'success_visa' => [
                'type' => 'Visa',
                'number' => '****************',
                'cvv' => '004',
                'expiry' => '03/30',
                'expected' => 'success'
            ],
            'failed_insufficient_funds' => [
                'type' => 'Visa',
                'number' => '****************',
                'cvv' => '004',
                'expiry' => '03/30',
                'expected' => 'declined',
                'decline_reason' => 'insufficient_funds'
            ]
        ];
        
        // Define test customers
        $this->testCustomers = [
            'individual' => [
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
                'address1' => 'شارع الملك فهد',
                'city' => 'الرياض',
                'state' => 'الرياض',
                'postcode' => '12345',
                'country' => 'SA',
                'currency' => 'SAR'
            ],
            'business' => [
                'company_name' => 'شركة التقنية المتطورة',
                'first_name' => 'سارة',
                'last_name' => 'علي',
                'email' => '<EMAIL>',
                'phone' => '+966502345678',
                'address1' => 'حي العليا',
                'city' => 'جدة',
                'state' => 'مكة المكرمة',
                'postcode' => '23451',
                'country' => 'SA',
                'currency' => 'SAR',
                'tax_id' => '1234567890'
            ]
        ];
    }

    /**
     * Log a message to both console and log file
     */
    private function log($message, $type = 'info') {
        $timestamp = date('[Y-m-d H:i:s]');
        $logMessage = "$timestamp [$type] $message\n";
        
        // Output to console
        echo $logMessage;
        
        // Write to log file
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }

    /**
     * Start a new test case
     */
    private function startTest($name) {
        $this->currentTest = $name;
        $this->testResults[$name] = [
            'status' => 'running',
            'steps' => [],
            'start_time' => microtime(true)
        ];
        $this->testCount++;
        $this->log("🚀 Starting test: $name", 'TEST');
    }

    /**
     * Add a test step
     */
    private function addStep($step, $status = 'info', $details = null) {
        $stepData = [
            'step' => $step,
            'status' => $status,
            'timestamp' => microtime(true)
        ];
        
        if ($details !== null) {
            $stepData['details'] = $details;
        }
        
        $this->testResults[$this->currentTest]['steps'][] = $stepData;
        
        $statusIcon = '';
        switch ($status) {
            case 'success': $statusIcon = '✅'; break;
            case 'error': $statusIcon = '❌'; break;
            case 'warning': $statusIcon = '⚠️'; break;
            default: $statusIcon = 'ℹ️';
        }
        
        $this->log("   $statusIcon $step", strtoupper($status));
        
        if ($details !== null) {
            $this->log("      Details: " . json_encode($details, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), 'DETAIL');
        }
    }

    /**
     * Complete the current test
     */
    private function endTest($status, $message = '') {
        $test = &$this->testResults[$this->currentTest];
        $test['status'] = $status;
        $test['end_time'] = microtime(true);
        $test['duration'] = round($test['end_time'] - $test['start_time'], 2) . 's';
        
        if ($status === 'passed') {
            $this->passedCount++;
            $this->log("✅ Test passed: {$this->currentTest} ({$test['duration']})", 'PASS');
        } else {
            $this->failedCount++;
            $this->log("❌ Test failed: {$this->currentTest} - $message", 'FAIL');
        }
        
        $this->log("", 'SEPARATOR');
    }

    /**
     * Assert that a condition is true
     */
    private function assertTrue($condition, $message) {
        if ($condition) {
            $this->addStep($message, 'success');
            return true;
        } else {
            $this->addStep("Assertion failed: $message", 'error');
            return false;
        }
    }

    /**
     * Test customer creation and validation
     */
    private function testCustomerCreation($customerType) {
        $customer = $this->testCustomers[$customerType];
        $this->addStep("Creating $customerType customer", 'info', $customer);
        
        // Validate required fields
        $requiredFields = ['first_name', 'last_name', 'email', 'phone', 'address1', 'city', 'country'];
        foreach ($requiredFields as $field) {
            if (empty($customer[$field])) {
                return $this->assertTrue(false, "Missing required field: $field");
            }
        }
        
        // Validate email format
        if (!filter_var($customer['email'], FILTER_VALIDATE_EMAIL)) {
            return $this->assertTrue(false, "Invalid email format: {$customer['email']}");
        }
        
        // Validate phone number format (simple check)
        if (!preg_match('/^\+[0-9]{10,15}$/', $customer['phone'])) {
            return $this->assertTrue(false, "Invalid phone number format: {$customer['phone']}");
        }
        
        // If we got here, customer data is valid
        $this->addStep("Customer data validated successfully", 'success');
        return true;
    }

    /**
     * Test payment processing
     */
    private function testPaymentProcessing($customerType, $cardType) {
        $customer = $this->testCustomers[$customerType];
        $card = $this->testCards[$cardType];
        
        // Create test invoice
        $invoiceId = 'INV-' . time() . '-' . strtoupper(substr(md5(rand()), 0, 6));
        $amount = rand(1000, 100000) / 100; // Random amount between 10.00 and 1000.00
        
        $this->addStep("Creating test invoice #$invoiceId for {$amount} {$customer['currency']}", 'info');
        
        // Simulate invoice creation in WHMCS
        $invoiceData = [
            'id' => $invoiceId,
            'userid' => 'TEST' . time(),
            'status' => 'Unpaid',
            'date' => date('Y-m-d'),
            'duedate' => date('Y-m-d', strtotime('+7 days')),
            'subtotal' => $amount,
            'tax' => 0,
            'total' => $amount,
            'paymentmethod' => 'lahza',
            'notes' => 'Test invoice for payment processing',
            'customer' => $customer
        ];
        
        // Save invoice to mock database
        WHMCS_DB::updateInvoice($invoiceId, $invoiceData);
        
        // Prepare payment data
        $paymentData = [
            'invoice_id' => $invoiceId,
            'amount' => $amount,
            'currency' => $customer['currency'],
            'card' => $card,
            'customer' => $customer,
            'metadata' => [
                'test_case' => "$customerType with $cardType",
                'timestamp' => date('c')
            ]
        ];
        
        $this->addStep("Processing payment with {$card['type']} card ending with " . substr($card['number'], -4), 'info');
        
        // Process payment (simulated)
        $result = $this->processPayment($paymentData);
        
        // Verify payment result
        $this->addStep("Payment processed with status: " . $result['status'], 
                      $result['success'] ? 'success' : 'error',
                      $result);
        
        // Update invoice status based on payment result
        if ($result['success']) {
            // For successful payments, call addInvoicePayment to update the status
            addInvoicePayment(
                $invoiceId,
                $result['transaction_id'],
                $amount,
                0, // No fee
                'lahza'
            );
            $this->addStep("Updated invoice status to Paid via addInvoicePayment", 'info');
        }
        
        // Verify invoice status
        $updatedInvoice = WHMCS_DB::getInvoice($invoiceId);
        $expectedStatus = $result['success'] ? 'Paid' : 'Unpaid';
        
        $this->addStep("Verifying invoice status. Expected: $expectedStatus, Actual: " . $updatedInvoice['status'], 'info');
        
        $this->assertTrue(
            $updatedInvoice['status'] === $expectedStatus,
            "Invoice status should be $expectedStatus after payment"
        );
        
        // Log transaction
        logTransaction('lahza', [
            'invoiceid' => $invoiceId,
            'transid' => $result['transaction_id'] ?? 'N/A',
            'amount' => $amount,
            'status' => $result['success'] ? 'Success' : 'Failed',
            'message' => $result['message'] ?? '',
            'card_type' => $card['type'],
            'card_last4' => substr($card['number'], -4)
        ], $result['success'] ? 'Success' : 'Failed');
        
        return $result['success'];
    }

    /**
     * Simulate payment processing
     */
    private function processPayment($paymentData) {
        // In a real scenario, this would call the Lahza API
        $card = $paymentData['card'];
        
        // Simulate API call delay
        usleep(rand(500000, 1500000)); // 0.5-1.5 seconds
        
        // Determine success/failure based on card number
        if (strpos($card['number'], '9995') !== false) {
            return [
                'success' => false,
                'status' => 'declined',
                'transaction_id' => 'TXN_' . time() . '_' . rand(1000, 9999),
                'decline_code' => 'insufficient_funds',
                'message' => 'Insufficient funds',
                'timestamp' => date('c')
            ];
        }
        
        // Successful payment
        return [
            'success' => true,
            'status' => 'succeeded',
            'transaction_id' => 'TXN_' . time() . '_' . rand(1000, 9999),
            'amount' => $paymentData['amount'],
            'currency' => $paymentData['currency'],
            'card' => [
                'type' => $card['type'],
                'last4' => substr($card['number'], -4)
            ],
            'customer' => [
                'name' => $paymentData['customer']['first_name'] . ' ' . $paymentData['customer']['last_name'],
                'email' => $paymentData['customer']['email']
            ],
            'timestamp' => date('c')
        ];
    }

    /**
     * Run all tests
     */
    public function runAllTests() {
        $this->startTime = microtime(true);
        $this->log("=== Starting Professional Payment Test Suite ===", 'HEADER');
        $this->log("Test started at: " . date('Y-m-d H:i:s'), 'INFO');
        $this->log("Log file: {$this->logFile}", 'INFO');
        
        try {
            // Test individual customer with successful payment
            $this->startTest("Individual customer with successful payment");
            if ($this->testCustomerCreation('individual')) {
                $this->testPaymentProcessing('individual', 'success_visa');
            }
            $this->endTest('passed');
            
            // Test business customer with failed payment (insufficient funds)
            $this->startTest("Business customer with insufficient funds");
            if ($this->testCustomerCreation('business')) {
                $this->testPaymentProcessing('business', 'failed_insufficient_funds');
            }
            $this->endTest('passed');
            
        } catch (Exception $e) {
            $this->log("Test failed with exception: " . $e->getMessage(), 'ERROR');
            $this->endTest('failed', $e->getMessage());
        }
        
        // Generate summary
        $this->generateSummary();
    }
    
    /**
     * Generate test summary
     */
    private function generateSummary() {
        $duration = round(microtime(true) - $this->startTime, 2);
        $summary = "\n=== Test Summary ===\n";
        $summary .= "Total tests: {$this->testCount}\n";
        $summary .= "✅ Passed: {$this->passedCount}\n";
        $summary .= "❌ Failed: {$this->failedCount}\n";
        $summary .= "⏱️  Duration: {$duration}s\n";
        $summary .= "Test completed at: " . date('Y-m-d H:i:s') . "\n";
        $summary .= "Log file: {$this->logFile}\n";
        
        $this->log($summary, 'SUMMARY');
        
        // Output detailed results
        foreach ($this->testResults as $testName => $test) {
            $status = $test['status'] === 'passed' ? '✅' : '❌';
            $this->log("$status $testName ({$test['duration']})", 'RESULT');
        }
    }
}

// Run the test suite
$testSuite = new ProfessionalPaymentTest();
$testSuite->runAllTests();
?>
