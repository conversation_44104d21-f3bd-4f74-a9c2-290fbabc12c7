<?php
/**
 * WHMCS Invoice Simulator for Lahza Payment Gateway Testing
 */

// Load configuration
$config = require __DIR__ . '/test_config.php';

// Set API base URL based on test mode
$baseUrl = $config['test_mode'] ? $config['sandbox_url'] : $config['base_url'];

// Generate test invoice data
$invoice = $config['invoice'];
$callbackUrl = rtrim($config['whmcs']['system_url'], '/') . $config['whmcs']['callback_path'];
$returnUrl = rtrim($config['whmcs']['system_url'], '/') . $config['whmcs']['return_path'] . '?reference=' . $invoice['reference'];

// Set callback URL in invoice data
$invoice['callback_url'] = $callbackUrl;

// Display test information
echo "=== WHMCS Invoice Simulator for Lahza Gateway ===\n";
echo "Test Mode: " . ($config['test_mode'] ? 'Enabled' : 'Disabled') . "\n";
echo "API Endpoint: $baseUrl\n";
echo "Reference: {$invoice['reference']}\n";
echo "Amount: " . ($invoice['amount'] / 100) . " {$invoice['currency']}\n";

/**
 * Make API requests to Lahza
 */
function makeApiRequest($url, $method = 'GET', $data = null, $secretKey = '') {
    $ch = curl_init();
    
    // Set default headers
    $headers = [
        'Authorization: Bearer ' . $secretKey,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $options = [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => false, // For testing only
        CURLOPT_SSL_VERIFYHOST => 0,     // For testing only
        CURLOPT_TIMEOUT => 30,
    ];
    
    if ($method === 'POST') {
        $options[CURLOPT_POST] = true;
        if ($data) {
            $jsonData = is_array($data) ? json_encode($data) : $data;
            $options[CURLOPT_POSTFIELDS] = $jsonData;
        }
    }
    
    curl_setopt_array($ch, $options);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    $responseData = json_decode($response, true);
    
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => $response,
        'data' => $responseData,
        'error' => $error,
        'url' => $url,
        'success' => isset($responseData['status']) && $responseData['status'] === true
    ];
}

// Test 1: Create Payment Request
try {
    echo "\n=== 1. Creating Payment Request ===\n";
    
    // Prepare payment data according to Lahza API
    $paymentData = [
        'amount' => $invoice['amount'], // Already in smallest currency unit
        'currency' => $invoice['currency'],
        'reference' => $invoice['reference'],
        'description' => $invoice['description'],
        'email' => $invoice['email'],
        'mobile' => $invoice['mobile'],
        'first_name' => $invoice['first_name'],
        'last_name' => $invoice['last_name'],
        'callback_url' => $invoice['callback_url'],
        'metadata' => json_encode($invoice['metadata'])
    ];
    
    // Make the API request
    // Include public key in the request data for tracking
    $paymentData['public_key'] = $config['test_public_key'];
    
    // Test DNS resolution first
    $testUrl = $baseUrl . '/transaction/initialize';
    $dnsCheck = gethostbyname(parse_url($testUrl, PHP_URL_HOST));
    
    if ($dnsCheck === parse_url($testUrl, PHP_URL_HOST)) {
        throw new Exception("DNS resolution failed for " . parse_url($testUrl, PHP_URL_HOST) . ". Please check your internet connection and DNS settings.");
    }
    
    echo "Connecting to: $testUrl\n";
    
    $response = makeApiRequest(
        $testUrl,
        'POST',
        $paymentData,
        $config['test_secret_key']  // Use test secret key for server-side auth
    );
    
    echo "Status Code: {$response['code']}\n";
    
    if ($response['error']) {
        throw new Exception("API request failed: " . $response['error']);
    }
    
    if (!$response['success']) {
        throw new Exception("Payment creation failed: " . 
            ($response['data']['message'] ?? 'Unknown error'));
    }
    
    echo "✅ Payment request created successfully!\n";
    echo "Authorization URL: {$response['data']['data']['authorization_url']}\n";
    echo "Reference: {$response['data']['data']['reference']}\n";
    
    // Save reference for further testing
    $reference = $response['data']['data']['reference'];
    $authorizationUrl = $response['data']['data']['authorization_url'];
    
    // Display the payment link to the user
    echo "\n=== Payment Link ===\n";
    echo "Please visit the following URL to complete the payment:\n";
    echo $authorizationUrl . "\n\n";
    
    // Test 2: Verify Transaction
    echo "\n=== 2. Verifying Transaction ===\n";
    
    // In a real scenario, after the user completes the payment, Lahza will redirect back to your return_url
    // For testing, we'll simulate a verification request
    $verifyResponse = makeApiRequest(
        $baseUrl . "/transaction/verify/{$reference}",
        'GET',
        null,
        $config['test_secret_key']  // Use test secret key for server-side auth
    );
    
    echo "Verification Status Code: {$verifyResponse['code']}\n";
    
    if ($verifyResponse['error']) {
        throw new Exception("Verification failed: " . $verifyResponse['error']);
    }
    
    if (!$verifyResponse['success']) {
        throw new Exception("Verification failed: " . 
            ($verifyResponse['data']['message'] ?? 'Unknown error'));
    }
    
    $transactionStatus = $verifyResponse['data']['data']['status'] ?? 'unknown';
    echo "✅ Transaction Status: {$transactionStatus}\n";
    
    // Display transaction details
    if (isset($verifyResponse['data']['data'])) {
        echo "\n=== Transaction Details ===\n";
        echo "Amount: " . ($verifyResponse['data']['data']['amount'] / 100) . " {$invoice['currency']}\n";
        echo "Reference: {$verifyResponse['data']['data']['reference']}\n";
        echo "Status: {$verifyResponse['data']['data']['status']}\n";
        echo "Paid At: " . ($verifyResponse['data']['data']['paid_at'] ?? 'N/A') . "\n";
    }
    
    // Test 3: List Transactions (Optional)
    echo "\n=== 3. Listing Recent Transactions ===\n";
    
    $listResponse = makeApiRequest(
        $baseUrl . '/transaction',
        'GET',
        [
            'perPage' => 5, // Get last 5 transactions
            'public_key' => $config['test_public_key'] // Include public key for tracking
        ],
        $config['test_secret_key']  // Use test secret key for server-side auth
    );
    
    if ($listResponse['success'] && !empty($listResponse['data']['data'])) {
        echo "Recent Transactions:\n";
        foreach ($listResponse['data']['data'] as $txn) {
            echo "- {$txn['reference']}: " . ($txn['amount'] / 100) . " {$txn['currency']} ({$txn['status']})\n";
        }
    }
    
    echo "\n=== Test Completed Successfully ===\n";
    echo "\nNext Steps:\n";
    echo "1. Complete the payment at: {$authorizationUrl}\n";
    echo "2. After payment, the callback will be sent to: {$callbackUrl}\n";
    echo "3. You can verify the payment status by visiting: {$returnUrl}\n";
    
} catch (Exception $e) {
    echo "\n❌ Test Failed: " . $e->getMessage() . "\n";
    if (!empty($e->getTraceAsString())) {
        echo "Trace: " . $e->getTraceAsString() . "\n";
    }
    exit(1);
}
