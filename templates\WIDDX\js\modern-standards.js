/*!
 * WIDDX Modern Standards JavaScript
 * Advanced JavaScript features and modern web APIs
 * Copyright (c) 2025 WIDDX Development Team
 */

(function() {
    'use strict';

    // Modern JavaScript features and APIs
    class WIDDXModernStandards {
        constructor() {
            this.features = new Map();
            this.observers = new Map();
            this.init();
        }

        init() {
            this.detectFeatures();
            this.initModernAPIs();
            this.initPerformanceOptimizations();
            this.initAccessibilityEnhancements();
            this.initProgressiveEnhancement();
        }

        // ===== FEATURE DETECTION =====
        detectFeatures() {
            const features = {
                intersectionObserver: 'IntersectionObserver' in window,
                resizeObserver: 'ResizeObserver' in window,
                mutationObserver: 'MutationObserver' in window,
                performanceObserver: 'PerformanceObserver' in window,
                webAnimations: 'animate' in document.createElement('div'),
                cssCustomProperties: CSS.supports('color', 'var(--test)'),
                cssGrid: CSS.supports('display', 'grid'),
                cssContainerQueries: CSS.supports('container-type', 'inline-size'),
                viewTransitions: 'startViewTransition' in document,
                scrollTimeline: CSS.supports('animation-timeline', 'scroll()'),
                requestIdleCallback: 'requestIdleCallback' in window,
                webWorkers: 'Worker' in window,
                serviceWorker: 'serviceWorker' in navigator,
                webShare: 'share' in navigator,
                clipboard: 'clipboard' in navigator,
                geolocation: 'geolocation' in navigator,
                deviceMotion: 'DeviceMotionEvent' in window,
                touchEvents: 'ontouchstart' in window,
                pointerEvents: 'PointerEvent' in window
            };

            Object.entries(features).forEach(([feature, supported]) => {
                this.features.set(feature, supported);
                document.documentElement.classList.toggle(`widdx-${feature}`, supported);
                document.documentElement.classList.toggle(`widdx-no-${feature}`, !supported);
            });

            console.log('🔍 Feature Detection Complete:', Object.fromEntries(this.features));
        }

        // ===== MODERN WEB APIS =====
        initModernAPIs() {
            this.initIntersectionObserver();
            this.initResizeObserver();
            this.initPerformanceObserver();
            this.initWebAnimations();
            this.initViewTransitions();
            this.initWebShare();
            this.initClipboardAPI();
        }

        initIntersectionObserver() {
            if (!this.features.get('intersectionObserver')) return;

            // Lazy loading images
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            img.classList.remove('widdx-lazy');
                            img.classList.add('widdx-loaded');
                        }
                        imageObserver.unobserve(img);
                    }
                });
            }, { rootMargin: '50px' });

            document.querySelectorAll('img[data-src]').forEach(img => {
                img.classList.add('widdx-lazy');
                imageObserver.observe(img);
            });

            // Scroll animations
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('widdx-animate-in');
                        animationObserver.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.widdx-animate-on-scroll').forEach(el => {
                animationObserver.observe(el);
            });

            this.observers.set('intersection', { imageObserver, animationObserver });
        }

        initResizeObserver() {
            if (!this.features.get('resizeObserver')) return;

            const resizeObserver = new ResizeObserver(entries => {
                entries.forEach(entry => {
                    const { width, height } = entry.contentRect;
                    const element = entry.target;
                    
                    // Update CSS custom properties with element dimensions
                    element.style.setProperty('--element-width', `${width}px`);
                    element.style.setProperty('--element-height', `${height}px`);
                    
                    // Trigger custom resize event
                    element.dispatchEvent(new CustomEvent('widdx:resize', {
                        detail: { width, height }
                    }));
                });
            });

            document.querySelectorAll('.widdx-responsive-element').forEach(el => {
                resizeObserver.observe(el);
            });

            this.observers.set('resize', resizeObserver);
        }

        initPerformanceObserver() {
            if (!this.features.get('performanceObserver')) return;

            // Monitor Core Web Vitals
            const vitalsObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    switch (entry.entryType) {
                        case 'largest-contentful-paint':
                            console.log('📊 LCP:', entry.startTime);
                            break;
                        case 'first-input':
                            console.log('📊 FID:', entry.processingStart - entry.startTime);
                            break;
                        case 'layout-shift':
                            if (!entry.hadRecentInput) {
                                console.log('📊 CLS:', entry.value);
                            }
                            break;
                    }
                });
            });

            try {
                vitalsObserver.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
            } catch (e) {
                console.warn('Performance Observer not fully supported');
            }
        }

        initWebAnimations() {
            if (!this.features.get('webAnimations')) return;

            // Enhanced button animations
            document.querySelectorAll('.widdx-animate-button').forEach(button => {
                button.addEventListener('click', (e) => {
                    const ripple = document.createElement('span');
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.5);
                        border-radius: 50%;
                        pointer-events: none;
                    `;

                    button.appendChild(ripple);

                    ripple.animate([
                        { transform: 'scale(0)', opacity: 1 },
                        { transform: 'scale(1)', opacity: 0 }
                    ], {
                        duration: 600,
                        easing: 'ease-out'
                    }).onfinish = () => ripple.remove();
                });
            });
        }

        initViewTransitions() {
            if (!this.features.get('viewTransitions')) return;

            // Enhanced page transitions
            document.addEventListener('click', (e) => {
                const link = e.target.closest('a[href]');
                if (link && link.hostname === location.hostname) {
                    e.preventDefault();
                    
                    if (document.startViewTransition) {
                        document.startViewTransition(() => {
                            location.href = link.href;
                        });
                    } else {
                        location.href = link.href;
                    }
                }
            });
        }

        initWebShare() {
            if (!this.features.get('webShare')) return;

            document.querySelectorAll('.widdx-share-button').forEach(button => {
                button.addEventListener('click', async () => {
                    try {
                        await navigator.share({
                            title: document.title,
                            text: document.querySelector('meta[name="description"]')?.content || '',
                            url: location.href
                        });
                    } catch (err) {
                        console.log('Share cancelled or failed:', err);
                    }
                });
            });
        }

        initClipboardAPI() {
            if (!this.features.get('clipboard')) return;

            document.querySelectorAll('.widdx-copy-button').forEach(button => {
                button.addEventListener('click', async () => {
                    const text = button.dataset.copyText || button.textContent;
                    try {
                        await navigator.clipboard.writeText(text);
                        this.showToast('Copied to clipboard!', 'success');
                    } catch (err) {
                        console.error('Failed to copy:', err);
                        this.showToast('Failed to copy', 'error');
                    }
                });
            });
        }

        // ===== PERFORMANCE OPTIMIZATIONS =====
        initPerformanceOptimizations() {
            this.initIdleCallbacks();
            this.initResourceHints();
            this.initCriticalResourceLoading();
            this.initMemoryManagement();
        }

        initIdleCallbacks() {
            if (!this.features.get('requestIdleCallback')) return;

            const idleTasks = [];
            
            // Queue non-critical tasks
            idleTasks.push(
                () => this.preloadCriticalResources(),
                () => this.initAnalytics(),
                () => this.initServiceWorker(),
                () => this.optimizeImages()
            );

            const runIdleTasks = () => {
                if (idleTasks.length === 0) return;
                
                requestIdleCallback((deadline) => {
                    while (deadline.timeRemaining() > 0 && idleTasks.length > 0) {
                        const task = idleTasks.shift();
                        try {
                            task();
                        } catch (error) {
                            console.error('Idle task failed:', error);
                        }
                    }
                    
                    if (idleTasks.length > 0) {
                        runIdleTasks();
                    }
                }, { timeout: 5000 });
            };

            runIdleTasks();
        }

        initResourceHints() {
            // Preload critical resources
            const preloadResources = [
                { href: '/templates/WIDDX/css/widdx-unified.min.css', as: 'style' },
                { href: '/templates/WIDDX/js/widdx-modern.js', as: 'script' }
            ];

            preloadResources.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource.href;
                link.as = resource.as;
                if (resource.crossorigin) link.crossOrigin = resource.crossorigin;
                document.head.appendChild(link);
            });
        }

        initCriticalResourceLoading() {
            // Load critical CSS inline for above-the-fold content
            const criticalCSS = `
                .widdx-critical { display: block; }
                .widdx-above-fold { visibility: visible; }
            `;
            
            const style = document.createElement('style');
            style.textContent = criticalCSS;
            document.head.appendChild(style);
        }

        initMemoryManagement() {
            // Clean up observers when elements are removed
            const cleanupObserver = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    mutation.removedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Clean up any observers attached to removed elements
                            this.cleanupElementObservers(node);
                        }
                    });
                });
            });

            cleanupObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        cleanupElementObservers(element) {
            // Implementation would clean up specific observers
            // This is a placeholder for memory management
            console.log('Cleaning up observers for removed element:', element);
        }

        // ===== ACCESSIBILITY ENHANCEMENTS =====
        initAccessibilityEnhancements() {
            this.initFocusManagement();
            this.initARIALiveRegions();
            this.initKeyboardNavigation();
            this.initScreenReaderOptimizations();
        }

        initFocusManagement() {
            // Enhanced focus management
            let focusedBeforeModal = null;

            document.addEventListener('widdx:modal-open', (e) => {
                focusedBeforeModal = document.activeElement;
                const modal = e.detail.modal;
                const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                if (firstFocusable) firstFocusable.focus();
            });

            document.addEventListener('widdx:modal-close', () => {
                if (focusedBeforeModal) {
                    focusedBeforeModal.focus();
                    focusedBeforeModal = null;
                }
            });
        }

        initARIALiveRegions() {
            // Create ARIA live regions for dynamic content
            const liveRegion = document.createElement('div');
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'widdx-sr-only';
            liveRegion.id = 'widdx-live-region';
            document.body.appendChild(liveRegion);

            // Method to announce messages to screen readers
            this.announce = (message, priority = 'polite') => {
                liveRegion.setAttribute('aria-live', priority);
                liveRegion.textContent = message;
                setTimeout(() => liveRegion.textContent = '', 1000);
            };
        }

        initKeyboardNavigation() {
            // Enhanced keyboard navigation
            document.addEventListener('keydown', (e) => {
                // Escape key handling
                if (e.key === 'Escape') {
                    const openModal = document.querySelector('.modal.show, .widdx-modal.open');
                    if (openModal) {
                        this.closeModal(openModal);
                        return;
                    }

                    const openDropdown = document.querySelector('.dropdown.show, .widdx-dropdown.open');
                    if (openDropdown) {
                        this.closeDropdown(openDropdown);
                        return;
                    }
                }

                // Arrow key navigation for custom components
                if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                    const activeElement = document.activeElement;
                    if (activeElement.matches('.widdx-nav-item, .widdx-menu-item')) {
                        this.handleArrowNavigation(e, activeElement);
                    }
                }
            });
        }

        initScreenReaderOptimizations() {
            // Add screen reader specific optimizations
            document.querySelectorAll('.widdx-loading').forEach(element => {
                element.setAttribute('aria-label', 'Loading content');
                element.setAttribute('aria-busy', 'true');
            });

            // Update loading states
            const updateLoadingState = (element, isLoading) => {
                element.setAttribute('aria-busy', isLoading.toString());
                if (!isLoading) {
                    this.announce('Content loaded');
                }
            };

            // Expose method globally
            window.widdxUpdateLoadingState = updateLoadingState;
        }

        // ===== PROGRESSIVE ENHANCEMENT =====
        initProgressiveEnhancement() {
            // Enhance forms progressively
            this.enhanceForms();
            this.enhanceNavigation();
            this.enhanceInteractions();
        }

        enhanceForms() {
            document.querySelectorAll('form.widdx-enhance').forEach(form => {
                // Add real-time validation
                form.addEventListener('input', this.debounce((e) => {
                    this.validateField(e.target);
                }, 300));

                // Enhanced submit handling
                form.addEventListener('submit', (e) => {
                    if (!this.validateForm(form)) {
                        e.preventDefault();
                        this.announce('Please correct the errors in the form');
                    }
                });
            });
        }

        enhanceNavigation() {
            // Progressive enhancement for navigation
            document.querySelectorAll('.widdx-nav').forEach(nav => {
                // Add mobile menu functionality
                const toggle = nav.querySelector('.widdx-nav-toggle');
                const menu = nav.querySelector('.widdx-nav-menu');

                if (toggle && menu) {
                    toggle.addEventListener('click', () => {
                        const isOpen = menu.classList.toggle('open');
                        toggle.setAttribute('aria-expanded', isOpen.toString());
                        menu.setAttribute('aria-hidden', (!isOpen).toString());
                    });
                }
            });
        }

        enhanceInteractions() {
            // Add enhanced interactions
            document.querySelectorAll('.widdx-interactive').forEach(element => {
                // Add ripple effect on click
                element.addEventListener('click', this.createRippleEffect.bind(this));
                
                // Add hover effects
                element.addEventListener('mouseenter', () => {
                    element.classList.add('widdx-hover');
                });
                
                element.addEventListener('mouseleave', () => {
                    element.classList.remove('widdx-hover');
                });
            });
        }

        // ===== UTILITY METHODS =====
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        createRippleEffect(e) {
            const element = e.currentTarget;
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.className = 'widdx-ripple';
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                pointer-events: none;
                transform: scale(0);
                animation: widdx-ripple-animation 0.6s ease-out;
            `;

            element.appendChild(ripple);
            setTimeout(() => ripple.remove(), 600);
        }

        showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `widdx-toast widdx-toast--${type}`;
            toast.textContent = message;
            toast.setAttribute('role', 'alert');
            
            document.body.appendChild(toast);
            
            // Animate in
            requestAnimationFrame(() => {
                toast.classList.add('widdx-toast--show');
            });
            
            // Remove after delay
            setTimeout(() => {
                toast.classList.remove('widdx-toast--show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
            
            this.announce(message);
        }

        // Public API
        getFeatureSupport(feature) {
            return this.features.get(feature) || false;
        }

        destroy() {
            // Clean up all observers and event listeners
            this.observers.forEach(observer => {
                if (observer.disconnect) observer.disconnect();
            });
            this.observers.clear();
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.widdxModernStandards = new WIDDXModernStandards();
        });
    } else {
        window.widdxModernStandards = new WIDDXModernStandards();
    }

    // Add CSS for ripple animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes widdx-ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .widdx-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 10000;
        }
        
        .widdx-toast--show {
            transform: translateX(0);
        }
        
        .widdx-toast--success { background: #27ae60; }
        .widdx-toast--error { background: #e74c3c; }
        .widdx-toast--info { background: #3498db; }
    `;
    document.head.appendChild(style);

})();
