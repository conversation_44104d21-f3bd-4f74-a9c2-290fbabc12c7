<?php

/**
 * Enhanced Transaction Status Management System for WIDDX
 * 
 * This class provides comprehensive transaction status management with:
 * - Advanced state machine implementation
 * - Real-time status tracking
 * - Automated status transitions
 * - Comprehensive audit logging
 * - WHMCS integration
 * - Performance monitoring
 * 
 * @package WIDDX
 * @subpackage PaymentGateway
 * @version 1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

require_once __DIR__ . '/Logger.php';

class EnhancedTransactionManager {
    
    private $logger;
    private $gatewayParams;
    private $cache;
    
    // Enhanced transaction status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_AUTHORIZED = 'authorized';
    const STATUS_CAPTURED = 'captured';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_PARTIALLY_REFUNDED = 'partially_refunded';
    const STATUS_DISPUTED = 'disputed';
    const STATUS_EXPIRED = 'expired';
    const STATUS_VOIDED = 'voided';
    const STATUS_CHARGEBACK = 'chargeback';
    const STATUS_UNDER_REVIEW = 'under_review';
    
    // 3D Secure status constants
    const TDS_STATUS_REQUIRED = '3ds_required';
    const TDS_STATUS_AUTHENTICATED = '3ds_authenticated';
    const TDS_STATUS_ATTEMPTED = '3ds_attempted';
    const TDS_STATUS_FAILED = '3ds_failed';
    const TDS_STATUS_UNAVAILABLE = '3ds_unavailable';
    const TDS_STATUS_CHALLENGE = '3ds_challenge';
    const TDS_STATUS_FRICTIONLESS = '3ds_frictionless';
    
    // Transaction types
    const TYPE_PAYMENT = 'payment';
    const TYPE_REFUND = 'refund';
    const TYPE_AUTHORIZATION = 'authorization';
    const TYPE_CAPTURE = 'capture';
    const TYPE_VOID = 'void';
    
    // Priority levels for status changes
    const PRIORITY_LOW = 1;
    const PRIORITY_NORMAL = 2;
    const PRIORITY_HIGH = 3;
    const PRIORITY_CRITICAL = 4;
    
    /**
     * Constructor
     */
    public function __construct($gatewayParams, $logger = null) {
        $this->gatewayParams = $gatewayParams;
        $this->logger = $logger ?: new LahzaLogger();
        $this->initializeCache();
        $this->createTransactionTables();
    }
    
    /**
     * Initialize caching system
     */
    private function initializeCache() {
        $this->cache = [];
        
        // Try to use Redis if available
        if (extension_loaded('redis')) {
            try {
                $redis = new Redis();
                $redis->connect('127.0.0.1', 6379);
                $this->cache['provider'] = 'redis';
                $this->cache['connection'] = $redis;
            } catch (Exception $e) {
                $this->cache['provider'] = 'file';
            }
        } else {
            $this->cache['provider'] = 'file';
        }
    }
    
    /**
     * Create enhanced transaction tables
     */
    private function createTransactionTables() {
        try {
            // Enhanced transactions table
            $sql = "CREATE TABLE IF NOT EXISTS lahza_transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transaction_id VARCHAR(255) UNIQUE NOT NULL,
                invoice_id VARCHAR(255) NOT NULL,
                parent_transaction_id VARCHAR(255) NULL,
                transaction_type ENUM('payment', 'refund', 'authorization', 'capture', 'void') DEFAULT 'payment',
                amount DECIMAL(16,2) NOT NULL,
                currency VARCHAR(3) NOT NULL,
                status VARCHAR(50) NOT NULL,
                previous_status VARCHAR(50) NULL,
                gateway_transaction_id VARCHAR(255) NULL,
                gateway_reference VARCHAR(255) NULL,
                payment_method VARCHAR(50) NULL,
                card_last4 VARCHAR(4) NULL,
                card_type VARCHAR(20) NULL,
                three_ds_status VARCHAR(50) NULL,
                three_ds_version VARCHAR(10) NULL,
                risk_score DECIMAL(5,2) NULL,
                fraud_check_result VARCHAR(50) NULL,
                client_ip VARCHAR(45) NULL,
                user_agent TEXT NULL,
                metadata JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                completed_at TIMESTAMP NULL,
                INDEX idx_transaction_id (transaction_id),
                INDEX idx_invoice_id (invoice_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                INDEX idx_gateway_transaction_id (gateway_transaction_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            Capsule::statement($sql);
            
            // Transaction status history table
            $sql = "CREATE TABLE IF NOT EXISTS lahza_transaction_status_history (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transaction_id VARCHAR(255) NOT NULL,
                old_status VARCHAR(50) NULL,
                new_status VARCHAR(50) NOT NULL,
                status_reason VARCHAR(255) NULL,
                changed_by VARCHAR(100) NULL,
                priority TINYINT DEFAULT 2,
                metadata JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_transaction_id (transaction_id),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (transaction_id) REFERENCES lahza_transactions(transaction_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            Capsule::statement($sql);
            
            // Transaction events table
            $sql = "CREATE TABLE IF NOT EXISTS lahza_transaction_events (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transaction_id VARCHAR(255) NOT NULL,
                event_type VARCHAR(50) NOT NULL,
                event_data JSON NULL,
                source VARCHAR(50) NOT NULL,
                processed BOOLEAN DEFAULT FALSE,
                retry_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL,
                INDEX idx_transaction_id (transaction_id),
                INDEX idx_event_type (event_type),
                INDEX idx_processed (processed),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (transaction_id) REFERENCES lahza_transactions(transaction_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            Capsule::statement($sql);
            
            $this->logger->info('Enhanced transaction tables created successfully');
            
        } catch (Exception $e) {
            $this->logger->error('Failed to create transaction tables', [
                'error' => $e->getMessage()
            ], 'database');
        }
    }
    
    /**
     * Create a new transaction with enhanced tracking
     */
    public function createTransaction($invoiceId, $amount, $currency, $metadata = []) {
        $transactionId = $this->generateTransactionId($invoiceId);
        
        $transactionData = [
            'transaction_id' => $transactionId,
            'invoice_id' => $invoiceId,
            'transaction_type' => self::TYPE_PAYMENT,
            'amount' => $amount,
            'currency' => $currency,
            'status' => self::STATUS_PENDING,
            'client_ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'metadata' => json_encode($metadata),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+30 minutes'))
        ];
        
        try {
            // Insert transaction
            Capsule::table('lahza_transactions')->insert($transactionData);
            
            // Log status history
            $this->logStatusChange($transactionId, null, self::STATUS_PENDING, 'Transaction created', 'system');
            
            // Cache transaction
            $this->cacheTransaction($transactionId, $transactionData);
            
            $this->logger->info('Transaction created successfully', [
                'transaction_id' => $transactionId,
                'invoice_id' => $invoiceId,
                'amount' => $amount,
                'currency' => $currency
            ], 'transactions');
            
            return $transactionId;
            
        } catch (Exception $e) {
            $this->logger->error('Failed to create transaction', [
                'invoice_id' => $invoiceId,
                'error' => $e->getMessage()
            ], 'transactions');
            
            return false;
        }
    }
    
    /**
     * Update transaction status with enhanced validation and logging
     */
    public function updateStatus($transactionId, $newStatus, $reason = '', $metadata = [], $priority = self::PRIORITY_NORMAL) {
        $transaction = $this->getTransaction($transactionId);
        
        if (!$transaction) {
            $this->logger->error('Transaction not found for status update', [
                'transaction_id' => $transactionId,
                'new_status' => $newStatus
            ], 'transactions');
            return false;
        }
        
        $oldStatus = $transaction['status'];
        
        // Validate status transition
        if (!$this->isValidStatusTransition($oldStatus, $newStatus)) {
            $this->logger->warning('Invalid status transition attempted', [
                'transaction_id' => $transactionId,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'reason' => $reason
            ], 'transactions');
            return false;
        }
        
        try {
            // Begin transaction
            Capsule::beginTransaction();
            
            // Update transaction status
            $updateData = [
                'status' => $newStatus,
                'previous_status' => $oldStatus,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Add completion timestamp for final states
            if ($this->isFinalStatus($newStatus)) {
                $updateData['completed_at'] = date('Y-m-d H:i:s');
            }
            
            // Merge additional metadata
            if (!empty($metadata)) {
                $existingMetadata = json_decode($transaction['metadata'] ?? '{}', true);
                $mergedMetadata = array_merge($existingMetadata, $metadata);
                $updateData['metadata'] = json_encode($mergedMetadata);
            }
            
            Capsule::table('lahza_transactions')
                ->where('transaction_id', $transactionId)
                ->update($updateData);
            
            // Log status change
            $this->logStatusChange($transactionId, $oldStatus, $newStatus, $reason, 'system', $priority, $metadata);
            
            // Handle status-specific actions
            $this->handleStatusChange($transactionId, $oldStatus, $newStatus, $metadata);
            
            // Update cache
            $this->invalidateTransactionCache($transactionId);
            
            // Commit transaction
            Capsule::commit();
            
            $this->logger->info('Transaction status updated successfully', [
                'transaction_id' => $transactionId,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'reason' => $reason,
                'priority' => $priority
            ], 'transactions');
            
            // Trigger status change event
            $this->triggerStatusChangeEvent($transactionId, $oldStatus, $newStatus, $metadata);
            
            return true;
            
        } catch (Exception $e) {
            Capsule::rollback();
            
            $this->logger->error('Failed to update transaction status', [
                'transaction_id' => $transactionId,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ], 'transactions');
            
            return false;
        }
    }
    
    /**
     * Get transaction with caching
     */
    public function getTransaction($transactionId) {
        // Try cache first
        $cached = $this->getCachedTransaction($transactionId);
        if ($cached) {
            return $cached;
        }
        
        try {
            $transaction = Capsule::table('lahza_transactions')
                ->where('transaction_id', $transactionId)
                ->first();
            
            if ($transaction) {
                $transactionArray = (array) $transaction;
                $this->cacheTransaction($transactionId, $transactionArray);
                return $transactionArray;
            }
            
            return null;
            
        } catch (Exception $e) {
            $this->logger->error('Failed to retrieve transaction', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], 'transactions');
            
            return null;
        }
    }
    
    /**
     * Get transaction status history
     */
    public function getStatusHistory($transactionId) {
        try {
            $history = Capsule::table('lahza_transaction_status_history')
                ->where('transaction_id', $transactionId)
                ->orderBy('created_at', 'asc')
                ->get();
            
            return $history->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to retrieve status history', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], 'transactions');
            
            return [];
        }
    }
    
    /**
     * Get transactions by status
     */
    public function getTransactionsByStatus($status, $limit = 100) {
        try {
            $transactions = Capsule::table('lahza_transactions')
                ->where('status', $status)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
            
            return $transactions->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to retrieve transactions by status', [
                'status' => $status,
                'error' => $e->getMessage()
            ], 'transactions');
            
            return [];
        }
    }
    
    /**
     * Get expired transactions
     */
    public function getExpiredTransactions() {
        try {
            $transactions = Capsule::table('lahza_transactions')
                ->where('expires_at', '<', date('Y-m-d H:i:s'))
                ->whereIn('status', [self::STATUS_PENDING, self::STATUS_PROCESSING])
                ->get();
            
            return $transactions->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to retrieve expired transactions', [
                'error' => $e->getMessage()
            ], 'transactions');
            
            return [];
        }
    }
    
    /**
     * Process expired transactions
     */
    public function processExpiredTransactions() {
        $expiredTransactions = $this->getExpiredTransactions();
        $processedCount = 0;
        
        foreach ($expiredTransactions as $transaction) {
            if ($this->updateStatus(
                $transaction->transaction_id,
                self::STATUS_EXPIRED,
                'Transaction expired due to timeout',
                ['expired_at' => date('Y-m-d H:i:s')],
                self::PRIORITY_HIGH
            )) {
                $processedCount++;
            }
        }
        
        if ($processedCount > 0) {
            $this->logger->info('Processed expired transactions', [
                'count' => $processedCount
            ], 'transactions');
        }
        
        return $processedCount;
    }
    
    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId($invoiceId) {
        return 'WIDDX_' . $invoiceId . '_' . time() . '_' . mt_rand(1000, 9999);
    }
    
    /**
     * Validate status transition with enhanced rules
     */
    private function isValidStatusTransition($oldStatus, $newStatus) {
        $validTransitions = [
            self::STATUS_PENDING => [
                self::STATUS_PROCESSING,
                self::STATUS_AUTHORIZED,
                self::STATUS_FAILED,
                self::STATUS_CANCELLED,
                self::STATUS_EXPIRED,
                self::STATUS_UNDER_REVIEW,
                self::TDS_STATUS_REQUIRED
            ],
            self::STATUS_PROCESSING => [
                self::STATUS_AUTHORIZED,
                self::STATUS_CAPTURED,
                self::STATUS_COMPLETED,
                self::STATUS_FAILED,
                self::STATUS_UNDER_REVIEW,
                self::TDS_STATUS_REQUIRED
            ],
            self::STATUS_AUTHORIZED => [
                self::STATUS_CAPTURED,
                self::STATUS_COMPLETED,
                self::STATUS_CANCELLED,
                self::STATUS_VOIDED,
                self::STATUS_EXPIRED
            ],
            self::STATUS_CAPTURED => [
                self::STATUS_COMPLETED,
                self::STATUS_REFUNDED,
                self::STATUS_PARTIALLY_REFUNDED,
                self::STATUS_DISPUTED,
                self::STATUS_CHARGEBACK
            ],
            self::STATUS_COMPLETED => [
                self::STATUS_REFUNDED,
                self::STATUS_PARTIALLY_REFUNDED,
                self::STATUS_DISPUTED,
                self::STATUS_CHARGEBACK
            ],
            self::STATUS_UNDER_REVIEW => [
                self::STATUS_PROCESSING,
                self::STATUS_FAILED,
                self::STATUS_CANCELLED
            ],
            self::TDS_STATUS_REQUIRED => [
                self::TDS_STATUS_CHALLENGE,
                self::TDS_STATUS_FRICTIONLESS,
                self::TDS_STATUS_AUTHENTICATED,
                self::TDS_STATUS_ATTEMPTED,
                self::TDS_STATUS_FAILED,
                self::TDS_STATUS_UNAVAILABLE
            ],
            self::TDS_STATUS_CHALLENGE => [
                self::TDS_STATUS_AUTHENTICATED,
                self::TDS_STATUS_FAILED,
                self::STATUS_EXPIRED
            ],
            self::TDS_STATUS_FRICTIONLESS => [
                self::TDS_STATUS_AUTHENTICATED,
                self::STATUS_PROCESSING
            ],
            self::TDS_STATUS_AUTHENTICATED => [
                self::STATUS_PROCESSING,
                self::STATUS_AUTHORIZED,
                self::STATUS_COMPLETED
            ],
            self::TDS_STATUS_ATTEMPTED => [
                self::STATUS_PROCESSING,
                self::STATUS_AUTHORIZED,
                self::STATUS_COMPLETED
            ]
        ];
        
        return isset($validTransitions[$oldStatus]) && 
               in_array($newStatus, $validTransitions[$oldStatus]);
    }
    
    /**
     * Check if status is final (no further transitions allowed)
     */
    private function isFinalStatus($status) {
        $finalStatuses = [
            self::STATUS_COMPLETED,
            self::STATUS_FAILED,
            self::STATUS_CANCELLED,
            self::STATUS_REFUNDED,
            self::STATUS_EXPIRED,
            self::STATUS_VOIDED,
            self::STATUS_CHARGEBACK
        ];
        
        return in_array($status, $finalStatuses);
    }
    
    /**
     * Log status change to history table
     */
    private function logStatusChange($transactionId, $oldStatus, $newStatus, $reason, $changedBy, $priority = self::PRIORITY_NORMAL, $metadata = []) {
        try {
            Capsule::table('lahza_transaction_status_history')->insert([
                'transaction_id' => $transactionId,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'status_reason' => $reason,
                'changed_by' => $changedBy,
                'priority' => $priority,
                'metadata' => !empty($metadata) ? json_encode($metadata) : null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            $this->logger->error('Failed to log status change', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], 'transactions');
        }
    }
    
    /**
     * Handle status-specific actions
     */
    private function handleStatusChange($transactionId, $oldStatus, $newStatus, $metadata) {
        switch ($newStatus) {
            case self::STATUS_COMPLETED:
                $this->handleCompletedPayment($transactionId, $metadata);
                break;
                
            case self::STATUS_FAILED:
                $this->handleFailedPayment($transactionId, $metadata);
                break;
                
            case self::STATUS_REFUNDED:
            case self::STATUS_PARTIALLY_REFUNDED:
                $this->handleRefund($transactionId, $metadata);
                break;
                
            case self::STATUS_DISPUTED:
            case self::STATUS_CHARGEBACK:
                $this->handleDispute($transactionId, $metadata);
                break;
                
            case self::STATUS_EXPIRED:
                $this->handleExpiredTransaction($transactionId, $metadata);
                break;
        }
    }
    
    /**
     * Handle completed payment
     */
    private function handleCompletedPayment($transactionId, $metadata) {
        $transaction = $this->getTransaction($transactionId);
        
        if (!$transaction) {
            return;
        }
        
        try {
            // Add payment to WHMCS
            $paymentSuccess = addInvoicePayment(
                $transaction['invoice_id'],
                $metadata['gateway_transaction_id'] ?? $transactionId,
                $transaction['amount'],
                0, // fees
                $this->gatewayParams['name']
            );
            
            if ($paymentSuccess) {
                // Log successful payment
                logTransaction($this->gatewayParams['name'], [
                    'invoice_id' => $transaction['invoice_id'],
                    'transaction_id' => $transactionId,
                    'amount' => $transaction['amount'],
                    'currency' => $transaction['currency'],
                    'gateway_transaction_id' => $metadata['gateway_transaction_id'] ?? '',
                    'payment_method' => $metadata['payment_method'] ?? ''
                ], 'Success');
                
                $this->logger->info('Payment completed and added to WHMCS', [
                    'transaction_id' => $transactionId,
                    'invoice_id' => $transaction['invoice_id'],
                    'amount' => $transaction['amount']
                ], 'transactions');
            }
            
        } catch (Exception $e) {
            $this->logger->error('Failed to handle completed payment', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], 'transactions');
        }
    }
    
    /**
     * Handle failed payment
     */
    private function handleFailedPayment($transactionId, $metadata) {
        $transaction = $this->getTransaction($transactionId);
        
        if (!$transaction) {
            return;
        }
        
        // Log failed payment
        logTransaction($this->gatewayParams['name'], [
            'invoice_id' => $transaction['invoice_id'],
            'transaction_id' => $transactionId,
            'error' => $metadata['error_message'] ?? 'Payment failed',
            'error_code' => $metadata['error_code'] ?? ''
        ], 'Failed');
        
        $this->logger->warning('Payment failed', [
            'transaction_id' => $transactionId,
            'invoice_id' => $transaction['invoice_id'],
            'error' => $metadata['error_message'] ?? 'Unknown error'
        ], 'transactions');
    }
    
    /**
     * Handle refund
     */
    private function handleRefund($transactionId, $metadata) {
        $this->logger->info('Refund processed', [
            'transaction_id' => $transactionId,
            'refund_amount' => $metadata['refund_amount'] ?? 'Unknown',
            'refund_reason' => $metadata['refund_reason'] ?? 'Not specified'
        ], 'transactions');
    }
    
    /**
     * Handle dispute/chargeback
     */
    private function handleDispute($transactionId, $metadata) {
        $this->logger->warning('Dispute/chargeback received', [
            'transaction_id' => $transactionId,
            'dispute_reason' => $metadata['dispute_reason'] ?? 'Not specified',
            'dispute_amount' => $metadata['dispute_amount'] ?? 'Unknown'
        ], 'transactions');
    }
    
    /**
     * Handle expired transaction
     */
    private function handleExpiredTransaction($transactionId, $metadata) {
        $this->logger->info('Transaction expired', [
            'transaction_id' => $transactionId,
            'expired_at' => $metadata['expired_at'] ?? date('Y-m-d H:i:s')
        ], 'transactions');
    }
    
    /**
     * Trigger status change event
     */
    private function triggerStatusChangeEvent($transactionId, $oldStatus, $newStatus, $metadata) {
        try {
            Capsule::table('lahza_transaction_events')->insert([
                'transaction_id' => $transactionId,
                'event_type' => 'status_change',
                'event_data' => json_encode([
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                    'metadata' => $metadata
                ]),
                'source' => 'transaction_manager',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            $this->logger->error('Failed to trigger status change event', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], 'transactions');
        }
    }
    
    /**
     * Cache transaction data
     */
    private function cacheTransaction($transactionId, $data) {
        $cacheKey = "transaction_{$transactionId}";
        
        if ($this->cache['provider'] === 'redis') {
            try {
                $this->cache['connection']->setex($cacheKey, 3600, json_encode($data));
            } catch (Exception $e) {
                // Fallback to file cache
                $this->fileCacheSet($cacheKey, $data, 3600);
            }
        } else {
            $this->fileCacheSet($cacheKey, $data, 3600);
        }
    }
    
    /**
     * Get cached transaction
     */
    private function getCachedTransaction($transactionId) {
        $cacheKey = "transaction_{$transactionId}";
        
        if ($this->cache['provider'] === 'redis') {
            try {
                $cached = $this->cache['connection']->get($cacheKey);
                return $cached ? json_decode($cached, true) : null;
            } catch (Exception $e) {
                // Fallback to file cache
                return $this->fileCacheGet($cacheKey);
            }
        } else {
            return $this->fileCacheGet($cacheKey);
        }
    }
    
    /**
     * Invalidate transaction cache
     */
    private function invalidateTransactionCache($transactionId) {
        $cacheKey = "transaction_{$transactionId}";
        
        if ($this->cache['provider'] === 'redis') {
            try {
                $this->cache['connection']->del($cacheKey);
            } catch (Exception $e) {
                // Fallback to file cache
                $this->fileCacheDelete($cacheKey);
            }
        } else {
            $this->fileCacheDelete($cacheKey);
        }
    }
    
    /**
     * File cache set
     */
    private function fileCacheSet($key, $data, $ttl) {
        $cacheDir = __DIR__ . '/cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        $cacheFile = $cacheDir . '/' . md5($key) . '.cache';
        $cacheData = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
        
        file_put_contents($cacheFile, json_encode($cacheData), LOCK_EX);
    }
    
    /**
     * File cache get
     */
    private function fileCacheGet($key) {
        $cacheFile = __DIR__ . '/cache/' . md5($key) . '.cache';
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        
        if (!$cacheData || $cacheData['expires'] < time()) {
            unlink($cacheFile);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * File cache delete
     */
    private function fileCacheDelete($key) {
        $cacheFile = __DIR__ . '/cache/' . md5($key) . '.cache';
        
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
    }
    
    /**
     * Get transaction statistics
     */
    public function getTransactionStatistics($dateFrom = null, $dateTo = null) {
        try {
            $query = Capsule::table('lahza_transactions');
            
            if ($dateFrom) {
                $query->where('created_at', '>=', $dateFrom);
            }
            
            if ($dateTo) {
                $query->where('created_at', '<=', $dateTo);
            }
            
            $stats = $query->selectRaw('
                status,
                COUNT(*) as count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
            ')
            ->groupBy('status')
            ->get();
            
            return $stats->toArray();
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get transaction statistics', [
                'error' => $e->getMessage()
            ], 'transactions');
            
            return [];
        }
    }
    
    /**
     * Clean up old transaction data
     */
    public function cleanupOldTransactions($daysToKeep = 365) {
        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
            
            // Delete old completed transactions
            $deletedCount = Capsule::table('lahza_transactions')
                ->where('created_at', '<', $cutoffDate)
                ->whereIn('status', [
                    self::STATUS_COMPLETED,
                    self::STATUS_FAILED,
                    self::STATUS_CANCELLED,
                    self::STATUS_EXPIRED
                ])
                ->delete();
            
            $this->logger->info('Cleaned up old transactions', [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate
            ], 'transactions');
            
            return $deletedCount;
            
        } catch (Exception $e) {
            $this->logger->error('Failed to cleanup old transactions', [
                'error' => $e->getMessage()
            ], 'transactions');
            
            return 0;
        }
    }
}
