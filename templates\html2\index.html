<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX - Digital Solutions Company</title>
    <meta name="description"
        content="WIDDX offers web hosting, social media marketing, programming, and graphic design services.">

    <!-- Performance Optimizations -->
    <meta name="theme-color" content="#010815">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//prod.spline.design">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Critical CSS (Inlined for Performance) -->
    <style>
        /* Critical Above-the-fold CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #ffffff;
            background-color: #010815;
            overflow-x: hidden;
            transition: background-color 0.3s ease, color 0.3s ease
        }

        :root {
            --primary-color: #010815;
            --secondary-color: #6c5bb9;
            --accent-color: #c0a5d5;
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --text-muted: rgba(255, 255, 255, 0.6);
            --background-primary: #010815;
            --background-secondary: #0f1419;
            --background-tertiary: rgba(255, 255, 255, 0.05);
            --border-color: rgba(192, 165, 213, 0.1);
            --card-background: rgba(255, 255, 255, 0.05);
            --navbar-background: rgba(1, 8, 21, 0.95);
            --footer-background: #000510;
            --white: #ffffff;
            --gray-light: #f8f9fa;
            --gray-dark: #2c3e50;
            --transition: all 0.3s ease;
            --border-radius: 12px;
            --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.1);
            --container-max-width: 1200px;
            --section-padding: 80px 0
        }

        [data-theme="light"] {
            --primary-color: #ffffff;
            --text-primary: #1a1a1a;
            --text-secondary: rgba(26, 26, 26, 0.8);
            --text-muted: rgba(26, 26, 26, 0.6);
            --background-primary: #ffffff;
            --background-secondary: #f8f9fa;
            --background-tertiary: rgba(108, 91, 185, 0.05);
            --border-color: rgba(108, 91, 185, 0.2);
            --card-background: rgba(108, 91, 185, 0.05);
            --navbar-background: rgba(255, 255, 255, 0.95);
            --footer-background: #f1f3f4;
            --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.15)
        }

        .container {
            max-width: var(--container-max-width);
            margin: 0 auto;
            padding: 0 20px
        }

        .btn {
            display: inline-block;
            padding: 14px 28px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            border: 2px solid transparent;
            font-size: 16px;
            position: relative;
            overflow: hidden
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            color: var(--white);
            border-color: var(--secondary-color)
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(108, 91, 185, 0.3)
        }

        .btn-secondary {
            background: transparent;
            color: var(--accent-color);
            border-color: var(--accent-color)
        }

        .btn-secondary:hover {
            background: var(--accent-color);
            color: var(--primary-color);
            transform: translateY(-2px)
        }

        .btn-outline {
            background: transparent;
            color: var(--white);
            border-color: var(--white)
        }

        .btn-outline:hover {
            background: var(--white);
            color: var(--primary-color);
            transform: translateY(-2px)
        }

        #preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-color);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000
        }

        .preloader-content {
            text-align: center
        }

        .logo-animation h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 20px;
            letter-spacing: 3px
        }

        .loading-bar {
            width: 200px;
            height: 4px;
            background: rgba(192, 165, 213, 0.2);
            border-radius: 2px;
            overflow: hidden;
            position: relative
        }

        .loading-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
            animation: loading 2s ease-in-out infinite
        }

        @keyframes loading {
            0% {
                left: -100%
            }

            100% {
                left: 100%
            }
        }

        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: var(--navbar-background);
            backdrop-filter: blur(10px);
            z-index: 1000;
            transition: var(--transition);
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color)
        }

        .navbar.scrolled {
            padding: 10px 0;
            background: var(--navbar-background);
            box-shadow: var(--box-shadow)
        }

        .nav-container {
            max-width: var(--container-max-width);
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center
        }

        .nav-logo a {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--accent-color);
            text-decoration: none;
            letter-spacing: 2px
        }

        .nav-menu {
            display: flex;
            gap: 30px;
            align-items: center
        }

        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            padding: 8px 0
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--accent-color)
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--accent-color);
            transition: width 0.3s ease
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%
        }

        .client-area {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            padding: 8px 16px !important;
            border-radius: 20px;
            color: var(--white) !important
        }

        .client-area::after {
            display: none
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            gap: 4px
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: var(--text-primary);
            transition: var(--transition);
            border-radius: 2px
        }

        .theme-toggle {
            background: transparent;
            border: 2px solid var(--accent-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--accent-color);
            cursor: pointer;
            transition: var(--transition);
            margin-left: 15px
        }

        .theme-toggle:hover {
            background: var(--accent-color);
            color: var(--white);
            transform: scale(1.1)
        }

        .theme-toggle i {
            font-size: 1rem;
            transition: var(--transition)
        }

        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            background: var(--background-primary);
            transition: background 0.3s ease
        }

        .hero-content {
            opacity: 1;
            z-index: 2;
            position: relative
        }

        .hero-content>* {
            opacity: 1
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1
        }

        .hero-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(108, 91, 185, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(192, 165, 213, 0.1) 0%, transparent 50%);
            opacity: 0.7
        }

        .modern-gradient-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            background: var(--background-primary);
            transition: all 0.3s ease
        }

        .gradient-layer-1,
        .gradient-layer-2,
        .gradient-layer-3 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%
        }

        .gradient-layer-1 {
            background: radial-gradient(circle at 20% 30%, rgba(15, 20, 25, 0.9) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.15) 0%, transparent 60%), radial-gradient(circle at 40% 80%, rgba(192, 165, 213, 0.1) 0%, transparent 50%)
        }

        .gradient-sphere {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            opacity: 0.6
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2
        }

        .title-line {
            display: block
        }

        .highlight {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text
        }

        .hero-subtitle {
            font-size: clamp(1.1rem, 2vw, 1.3rem);
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 600px
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            flex-wrap: wrap
        }

        .hero-visual {
            position: relative;
            z-index: 2;
            height: 80vh;
            min-height: 600px;
            max-height: 800px;
            overflow: visible
        }

        .robot-container {
            width: 100%;
            min-width: 600px;
            min-height: 600px;
            max-width: 800px;
            max-height: 800px;
            position: relative;
            overflow: visible;
            display: flex;
            align-items: center;
            justify-content: center
        }

        .robot-container spline-viewer {
            width: 100%;
            height: 100%;
            border-radius: var(--border-radius);
            object-fit: contain;
            object-position: center
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none
        }

        .floating-card {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(192, 165, 213, 0.2);
            border-radius: var(--border-radius);
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
            transition: var(--transition);
            pointer-events: auto;
            cursor: pointer
        }

        .floating-card:nth-child(1) {
            top: 10%;
            right: 15%
        }

        .floating-card:nth-child(2) {
            top: 30%;
            left: 10%
        }

        .floating-card:nth-child(3) {
            bottom: 30%;
            right: 10%
        }

        .floating-card:nth-child(4) {
            bottom: 10%;
            left: 15%
        }

        .floating-card i {
            font-size: 1.5rem;
            color: var(--accent-color)
        }

        .floating-card span {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary)
        }

        .floating-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--accent-color);
            transform: translateY(-5px)
        }

        .scroll-indicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2
        }

        .scroll-arrow {
            width: 20px;
            height: 20px;
            border: 2px solid var(--accent-color);
            border-top: none;
            border-left: none;
            transform: rotate(45deg);
            animation: bounce 2s infinite
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0) rotate(45deg)
            }

            40% {
                transform: translateY(-10px) rotate(45deg)
            }

            60% {
                transform: translateY(-5px) rotate(45deg)
            }
        }

        @media (max-width:768px) {
            .hero {
                flex-direction: column;
                text-align: center;
                padding: 100px 0 50px
            }

            .hero-content {
                margin-bottom: 2rem
            }

            .hero-buttons {
                justify-content: center
            }

            .hero-visual {
                height: 50vh;
                min-height: 400px
            }

            .robot-container {
                min-width: 400px;
                min-height: 400px;
                max-width: 500px;
                max-height: 500px
            }

            .floating-card {
                padding: 10px;
                font-size: 0.8rem
            }

            .floating-card i {
                font-size: 1.2rem
            }

            .nav-menu {
                display: none
            }

            .hamburger {
                display: flex
            }
        }
    </style>

    <!-- Optimized Font Loading -->
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;600;700&display=swap"
        as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;600;700&display=swap">
    </noscript>

    <!-- Deferred CSS -->
    <link rel="preload" href="css/style.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="css/style.css">
    </noscript>

    <!-- Deferred Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style"
        onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    </noscript>
</head>

<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="preloader-content">
            <div class="logo-animation">
                <h1>WIDDX</h1>
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">WIDDX</a>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link active" data-translate="home">Home</a>
                <a href="about.html" class="nav-link" data-translate="about">About</a>
                <a href="services.html" class="nav-link" data-translate="services">Services</a>
                <a href="portfolio.html" class="nav-link" data-translate="portfolio">Portfolio</a>
                <a href="knowledgebase.html" class="nav-link" data-translate="knowledgebase">Knowledgebase</a>
                <a href="contact.html" class="nav-link" data-translate="contact">Contact</a>
                <a href="client-area.html" class="nav-link client-area" data-translate="clientArea">Client Area</a>
                <button class="language-toggle" id="languageToggle" aria-label="Toggle language">
                    <i class="fas fa-language" id="languageIcon"></i>
                    <span id="languageText">عربي</span>
                </button>
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
            </div>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title" data-translate="heroTitle">
                    <span class="title-line">Empowering Your</span>
                    <span class="title-line"><span class="highlight">Digital World</span></span>
                </h1>
                <p class="hero-subtitle" data-translate="heroSubtitle">
                    Hosting. Design. Development. Marketing – All in One Place.
                </p>
                <div class="hero-buttons">
                    <a href="contact.html" class="btn btn-primary" data-translate="getStarted">Get Started</a>
                    <a href="services.html" class="btn btn-secondary" data-translate="learnMore">View Services</a>
                </div>

            </div>
            <div class="hero-visual">
                <div class="robot-container">
                    <spline-viewer url="https://prod.spline.design/AqtlWJlNbO-ZMkvz/scene.splinecode"></spline-viewer>
                </div>
                <div class="floating-elements">
                    <div class="floating-card" data-speed="2">
                        <i class="fas fa-server"></i>
                        <span>Hosting</span>
                    </div>
                    <div class="floating-card" data-speed="3">
                        <i class="fas fa-bullhorn"></i>
                        <span>Marketing</span>
                    </div>
                    <div class="floating-card" data-speed="1.5">
                        <i class="fas fa-code"></i>
                        <span>Development</span>
                    </div>
                    <div class="floating-card" data-speed="2.5">
                        <i class="fas fa-palette"></i>
                        <span>Design</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- Services Overview -->
    <section class="services-overview py-5" id="services-overview">
        <div class="container">
            <div class="row mb-4">
                <div class="col text-center">
                    <h2 class="section-title display-5 fw-bold" data-translate="servicesTitle">What We Do</h2>
                    <p class="section-subtitle text-muted" data-translate="servicesSubtitle">Comprehensive digital
                        solutions tailored to your business needs</p>
                </div>
            </div>
        </div>
        <div class="services-grid">
            <div class="service-card" data-service="hosting">
                <div class="service-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h3 data-translate="webHosting">Web Hosting</h3>
                <p data-translate="webHostingDesc">Secure, scalable, and lightning-fast hosting for your website.</p>
                <a href="services.html#hosting" class="service-link" data-translate="learnMore">Learn More <i
                        class="fas fa-arrow-right"></i></a>
            </div>
            <div class="service-card" data-service="marketing">
                <div class="service-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <h3 data-translate="socialMedia">Social Media Marketing</h3>
                <p data-translate="socialMediaDesc">Run powerful campaigns on Facebook, Instagram & more.</p>
                <a href="services.html#marketing" class="service-link" data-translate="learnMore">Learn More <i
                        class="fas fa-arrow-right"></i></a>
            </div>
            <div class="service-card" data-service="development">
                <div class="service-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3 data-translate="programming">Custom Development</h3>
                <p data-translate="programmingDesc">Web and software solutions tailored to your business.</p>
                <a href="services.html#development" class="service-link" data-translate="learnMore">Learn More <i
                        class="fas fa-arrow-right"></i></a>
            </div>
            <div class="service-card" data-service="design">
                <div class="service-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h3 data-translate="graphicDesign">Graphic Design & Branding</h3>
                <p data-translate="graphicDesignDesc">Logos, brand identities, video editing, and content creation.</p>
                <a href="services.html#design" class="service-link" data-translate="learnMore">Learn More <i
                        class="fas fa-arrow-right"></i></a>
            </div>
        </div>
        </div>
    </section>

    <!-- Why Choose WIDDX -->
    <section class="why-choose-us">
        <div class="container">
            <div class="section-header">
                <h2 data-translate="whyChooseTitle">Why WIDDX?</h2>
                <p data-translate="whyChooseSubtitle">Three key reasons why businesses choose us for their digital needs
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-block">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>🛡️ Reliable Hosting</h3>
                    <p>High uptime, fast speeds, total security.</p>
                </div>
                <div class="feature-block">
                    <div class="feature-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3>🎯 Effective Marketing</h3>
                    <p>Data-driven campaigns that work.</p>
                </div>
                <div class="feature-block">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>🧠 Smart Tech</h3>
                    <p>Custom solutions built to solve real problems.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Features Section -->
    <section class="technical-features" id="technical-features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title" data-translate="technicalFeaturesTitle">Powerful Hosting Technology</h2>
                <p class="section-subtitle" data-translate="technicalFeaturesSubtitle">Industry-leading technology stack
                    for maximum performance and reliability</p>
            </div>
            <div class="features-content">
                <div class="features-visual">
                    <div class="tech-illustration">
                        <div class="server-stack">
                            <div class="server-layer" data-tech="cpanel">
                                <i class="fas fa-desktop"></i>
                                <span>cPanel</span>
                            </div>
                            <div class="server-layer" data-tech="cloudlinux">
                                <i class="fas fa-cloud"></i>
                                <span>CloudLinux</span>
                            </div>
                            <div class="server-layer" data-tech="litespeed">
                                <i class="fas fa-bolt"></i>
                                <span>LiteSpeed</span>
                            </div>
                            <div class="server-layer" data-tech="ssl">
                                <i class="fas fa-shield-alt"></i>
                                <span>Free SSL</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="features-list">
                    <div class="tech-feature">
                        <div class="feature-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <div class="feature-content">
                            <h3 data-translate="cpanelTitle">cPanel Control Panel</h3>
                            <p data-translate="cpanelDesc">Industry-standard control panel for easy website management
                                and hosting features access.</p>
                        </div>
                    </div>
                    <div class="tech-feature">
                        <div class="feature-icon">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="feature-content">
                            <h3 data-translate="cloudlinuxTitle">CloudLinux OS</h3>
                            <p data-translate="cloudlinuxDesc">Isolated environment with balanced CPU, RAM, and disk I/O
                                limits for optimal performance.</p>
                        </div>
                    </div>
                    <div class="tech-feature">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="feature-content">
                            <h3 data-translate="litespeedTitle">LiteSpeed Web Server</h3>
                            <p data-translate="litespeedDesc">Ultra-fast web server technology that delivers up to 3x
                                faster loading speeds.</p>
                        </div>
                    </div>
                    <div class="tech-feature">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-content">
                            <h3 data-translate="sslTitle">Free SSL Certificates</h3>
                            <p data-translate="sslDesc">Automatic SSL installation and renewal for secure HTTPS
                                connections on all websites.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="statistics-section" id="statistics">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title" data-translate="statisticsTitle">Trusted by Thousands</h2>
                <p class="section-subtitle" data-translate="statisticsSubtitle">Our numbers speak for themselves - join
                    thousands of satisfied customers worldwide</p>
            </div>
            <div class="stats-grid">
                <div class="stat-item" data-stat="customers">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value">
                        <span class="stat-number" data-target="15000">0</span>
                    </div>
                    <div class="stat-label" data-translate="happyCustomers">Happy Customers</div>
                </div>
                <div class="stat-item" data-stat="uptime">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-value">
                        <span class="stat-number" data-target="99.9">0</span>
                        <span class="stat-suffix">%</span>
                    </div>
                    <div class="stat-label" data-translate="uptimeGuarantee">Uptime Guarantee</div>
                </div>
                <div class="stat-item" data-stat="support">
                    <div class="stat-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="stat-value">
                        <span class="stat-number" data-target="24">0</span>
                        <span class="stat-suffix">/7</span>
                    </div>
                    <div class="stat-label" data-translate="supportAvailable">Support Available</div>
                </div>
                <div class="stat-item" data-stat="countries">
                    <div class="stat-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="stat-value">
                        <span class="stat-number" data-target="50">0</span>
                        <span class="stat-suffix">+</span>
                    </div>
                    <div class="stat-label" data-translate="countriesServed">Countries Served</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Preview -->
    <section class="portfolio-preview">
        <div class="container">
            <div class="section-header">
                <h2>A Glimpse of Our Work</h2>
                <p>Showcasing some of our recent successful projects</p>
            </div>
            <div class="portfolio-gallery">
                <div class="portfolio-item-preview">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h4>E-commerce Hosting</h4>
                            <p>High-performance hosting solution</p>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item-preview">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h4>Social Media Campaign</h4>
                            <p>300% increase in brand awareness</p>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item-preview">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h4>Custom Web App</h4>
                            <p>Enterprise management system</p>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item-preview">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h4>Brand Identity</h4>
                            <p>Complete visual identity design</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="portfolio-cta">
                <a href="portfolio.html" class="btn btn-primary">View Full Portfolio</a>
            </div>
        </div>
    </section>

    <!-- Client Testimonials -->
    <section class="client-testimonials">
        <div class="container">
            <div class="section-header">
                <h2>What Our Clients Say</h2>
                <p>Real feedback from satisfied customers</p>
            </div>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p>"WIDDX transformed our online presence completely. Their hosting solution is rock-solid and
                            their support team is incredibly responsive."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="author-info">
                            <h4>Sarah Johnson</h4>
                            <span>CEO, TechStart Inc.</span>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p>"The marketing campaign WIDDX created for us exceeded all expectations. We saw a 400%
                            increase in leads within the first quarter."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="author-info">
                            <h4>Michael Chen</h4>
                            <span>Marketing Director, GrowthCorp</span>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p>"Working with WIDDX on our custom application was a pleasure. They delivered exactly what we
                            needed, on time and within budget."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="author-info">
                            <h4>Emily Rodriguez</h4>
                            <span>CTO, InnovateLab</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section" id="faq">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title" data-translate="faqTitle">Frequently Asked Questions</h2>
                <p class="section-subtitle" data-translate="faqSubtitle">Find answers to common questions about our
                    hosting services</p>
            </div>
            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question" data-faq="1">
                        <h3 data-translate="faq1Question">What is included in your hosting plans?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" data-faq="1">
                        <p data-translate="faq1Answer">Our hosting plans include SSD storage, unlimited bandwidth, free
                            SSL certificates, cPanel control panel, 24/7 support, and a 99.9% uptime guarantee. Each
                            plan also comes with free website migration and daily backups.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question" data-faq="2">
                        <h3 data-translate="faq2Question">Do you offer a money-back guarantee?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" data-faq="2">
                        <p data-translate="faq2Answer">Yes, we offer a 30-day money-back guarantee on all our hosting
                            plans. If you're not satisfied with our service within the first 30 days, we'll refund your
                            payment in full.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question" data-faq="3">
                        <h3 data-translate="faq3Question">How do I migrate my existing website?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" data-faq="3">
                        <p data-translate="faq3Answer">We provide free website migration services for all new customers.
                            Our technical team will handle the entire migration process, ensuring zero downtime and data
                            integrity throughout the transfer.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question" data-faq="4">
                        <h3 data-translate="faq4Question">What kind of support do you provide?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" data-faq="4">
                        <p data-translate="faq4Answer">We offer 24/7 technical support through live chat, email, and
                            phone. Our experienced support team is always ready to help you with any hosting-related
                            questions or issues.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question" data-faq="5">
                        <h3 data-translate="faq5Question">Can I upgrade my hosting plan later?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" data-faq="5">
                        <p data-translate="faq5Answer">Absolutely! You can upgrade your hosting plan at any time through
                            your client area. The upgrade process is seamless and your website will continue to run
                            without any interruption.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Get Started?</h2>
                <p>Let's bring your ideas to life with reliable hosting, custom tech, and stunning visuals.</p>
                <div class="cta-buttons">
                    <a href="contact.html" class="btn btn-primary">Contact Us</a>
                    <a href="services.html" class="btn btn-outline">See Our Plans</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>WIDDX</h3>
                    <p>Your trusted partner for comprehensive digital solutions.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="services.html#hosting">Web Hosting</a></li>
                        <li><a href="services.html#marketing">Social Media Marketing</a></li>
                        <li><a href="services.html#development">Programming</a></li>
                        <li><a href="services.html#design">Graphic Design</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="portfolio.html">Portfolio</a></li>
                        <li><a href="knowledgebase.html">Knowledge Base</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="client-area.html">Client Area</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +****************</p>
                        <p><i class="fas fa-map-marker-alt"></i> 123 Digital Street, Tech City</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 WIDDX. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Performance-Optimized Scripts -->
    <script>
        // Critical performance optimizations
        (function () {
            // Ensure content visibility immediately
            function ensureVisibility() {
                const heroContent = document.querySelector('.hero-content');
                if (heroContent) {
                    heroContent.style.opacity = '1';
                    heroContent.style.visibility = 'visible';
                }

                const heroElements = document.querySelectorAll('.title-line, .hero-subtitle, .hero-buttons');
                heroElements.forEach(element => {
                    element.style.opacity = '1';
                    element.style.visibility = 'visible';
                    element.style.transform = 'none';
                });
            }

            // Optimized script loading function
            function loadScript(src, callback, isModule = false) {
                const script = document.createElement('script');
                script.src = src;
                script.async = true;
                if (isModule) script.type = 'module';
                if (callback) script.onload = callback;
                document.head.appendChild(script);
                return script;
            }

            // Load modern background system first
            function loadModernBackground() {
                loadScript('js/modern-background.js');
            }

            // Load GSAP after critical content
            function loadGSAP() {
                loadScript('https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js', function () {
                    loadScript('https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js', function () {
                        // Load GSAP animations after GSAP is ready
                        loadScript('js/gsap-animations.js');
                    });
                });
            }

            // Load Spline viewer when needed
            function loadSplineViewer() {
                if (!window.splineViewerLoaded) {
                    window.splineViewerLoaded = true;
                    loadScript('https://unpkg.com/@splinetool/viewer@1.3.5/build/spline-viewer.js', null, true);
                }
            }

            // Intersection Observer for Spline viewer
            function initSplineObserver() {
                if ('IntersectionObserver' in window) {
                    const splineObserver = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                loadSplineViewer();
                                splineObserver.unobserve(entry.target);
                            }
                        });
                    }, { rootMargin: '100px' });

                    const splineContainer = document.querySelector('.robot-container');
                    if (splineContainer) {
                        splineObserver.observe(splineContainer);
                    }
                } else {
                    // Fallback for browsers without IntersectionObserver
                    setTimeout(loadSplineViewer, 2000);
                }
            }

            // Initialize everything
            document.addEventListener('DOMContentLoaded', function () {
                ensureVisibility();
                loadModernBackground();
                initSplineObserver();

                // Load GSAP after a short delay to prioritize critical content
                setTimeout(loadGSAP, 100);
            });

            // Fallback: ensure content is visible even if scripts fail
            setTimeout(ensureVisibility, 500);
        })();
    </script>

    <!-- Language Manager - Load Early -->
    <script defer src="js/language-manager.js"></script>

    <!-- Main JavaScript - Optimized -->
    <script defer src="js/main.js"></script>

    <!-- Performance Monitoring -->
    <script defer src="js/performance-monitor.js"></script>

    <!-- Service Worker for Caching -->
    <script>
        // Register Service Worker (only for HTTP/HTTPS protocols)
        if ('serviceWorker' in navigator && (location.protocol === 'http:' || location.protocol === 'https:')) {
            window.addEventListener('load', function () {
                navigator.serviceWorker.register('sw.js')
                    .then(function (registration) {
                        console.log('✅ ServiceWorker registered successfully');
                    })
                    .catch(function (err) {
                        console.log('❌ ServiceWorker registration failed:', err);
                    });
            });
        } else if (location.protocol === 'file:') {
            console.log('ℹ️ Service Worker not supported for file:// protocol');
        }

        // Performance optimization: Preload critical pages
        window.addEventListener('load', function () {
            const criticalPages = ['about.html', 'services.html', 'portfolio.html', 'contact.html', 'client-area.html'];
            criticalPages.forEach(page => {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = page;
                document.head.appendChild(link);
            });
        });

        // Performance optimization: Reduce layout thrashing
        let resizeTimer;
        window.addEventListener('resize', function () {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function () {
                // Batch resize operations
                window.dispatchEvent(new CustomEvent('optimizedResize'));
            }, 250);
        });
    </script>
    <!-- Bootstrap 5 JS Bundle CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>
</body>

</html>