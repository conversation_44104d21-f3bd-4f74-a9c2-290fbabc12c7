// ===== ERROR CHECKER UTILITY =====
// This script helps identify common issues in the WIDDX project

class ErrorChecker {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.info = [];
    }

    // Check for missing elements
    checkMissingElements() {
        const requiredElements = [
            { id: 'navbar', name: 'Navigation Bar' },
            { id: 'preloader', name: 'Preloader' },
            { id: 'scrollToTop', name: 'Scroll to Top Button' },
            { id: 'themeToggle', name: 'Theme Toggle' },
            { id: 'languageToggle', name: 'Language Toggle' }
        ];

        requiredElements.forEach(element => {
            if (!document.getElementById(element.id)) {
                this.warnings.push(`Missing element: ${element.name} (ID: ${element.id})`);
            }
        });
    }

    // Check for broken links
    checkBrokenLinks() {
        const links = document.querySelectorAll('a[href]');
        links.forEach(link => {
            const href = link.getAttribute('href');
            if (href === '#' || href === '') {
                this.warnings.push(`Empty or placeholder link found: ${link.textContent.trim()}`);
            }
        });
    }

    // Check for missing images
    checkMissingImages() {
        const images = document.querySelectorAll('img[src]');
        images.forEach(img => {
            img.addEventListener('error', () => {
                this.errors.push(`Failed to load image: ${img.src}`);
            });
        });
    }

    // Check for JavaScript errors
    checkJavaScriptErrors() {
        // Check if required functions exist
        const requiredFunctions = [
            'initializeApp',
            'initThemeToggle',
            'initNavigation',
            'initMobileMenu'
        ];

        requiredFunctions.forEach(funcName => {
            if (typeof window[funcName] !== 'function') {
                this.warnings.push(`Function not found: ${funcName}`);
            }
        });

        // Check if GSAP is loaded
        if (typeof gsap === 'undefined') {
            this.warnings.push('GSAP library not loaded - animations may not work');
        }

        // Check if ScrollTrigger is loaded
        if (typeof ScrollTrigger === 'undefined') {
            this.warnings.push('ScrollTrigger plugin not loaded - scroll animations may not work');
        }
    }

    // Check CSS loading
    checkCSSLoading() {
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        stylesheets.forEach(stylesheet => {
            stylesheet.addEventListener('error', () => {
                this.errors.push(`Failed to load stylesheet: ${stylesheet.href}`);
            });
        });
    }

    // Check for console errors
    checkConsoleErrors() {
        const originalError = console.error;
        const originalWarn = console.warn;

        console.error = (...args) => {
            this.errors.push(`Console Error: ${args.join(' ')}`);
            originalError.apply(console, args);
        };

        console.warn = (...args) => {
            this.warnings.push(`Console Warning: ${args.join(' ')}`);
            originalWarn.apply(console, args);
        };
    }

    // Run all checks
    runAllChecks() {
        this.checkMissingElements();
        this.checkBrokenLinks();
        this.checkMissingImages();
        this.checkJavaScriptErrors();
        this.checkCSSLoading();
        this.checkConsoleErrors();

        // Performance check
        if (performance.now() > 3000) {
            this.warnings.push('Page load time is over 3 seconds - consider optimization');
        }

        this.info.push(`Checks completed at ${new Date().toLocaleTimeString()}`);
    }

    // Generate report
    generateReport() {
        console.group('🔍 WIDDX Error Checker Report');
        
        if (this.errors.length > 0) {
            console.group('❌ Errors');
            this.errors.forEach(error => console.error(error));
            console.groupEnd();
        }

        if (this.warnings.length > 0) {
            console.group('⚠️ Warnings');
            this.warnings.forEach(warning => console.warn(warning));
            console.groupEnd();
        }

        if (this.info.length > 0) {
            console.group('ℹ️ Information');
            this.info.forEach(info => console.info(info));
            console.groupEnd();
        }

        if (this.errors.length === 0 && this.warnings.length === 0) {
            console.log('✅ No issues found!');
        }

        console.groupEnd();

        return {
            errors: this.errors,
            warnings: this.warnings,
            info: this.info,
            hasIssues: this.errors.length > 0 || this.warnings.length > 0
        };
    }

    // Clear all stored issues
    clear() {
        this.errors = [];
        this.warnings = [];
        this.info = [];
    }
}

// Initialize error checker
const errorChecker = new ErrorChecker();

// Run checks when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            errorChecker.runAllChecks();
            errorChecker.generateReport();
        }, 1000);
    });
} else {
    setTimeout(() => {
        errorChecker.runAllChecks();
        errorChecker.generateReport();
    }, 1000);
}

// Make error checker available globally for manual testing
window.errorChecker = errorChecker;

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorChecker;
}