<?php

/**
 * Enhanced 3D Secure Support for WIDDX
 * 
 * Comprehensive 3D Secure 2.2.0 implementation with:
 * - Advanced browser fingerprinting
 * - Challenge flow management
 * - Frictionless authentication
 * - Mobile optimization
 * - Fallback handling
 * - Real-time monitoring
 * 
 * @package WIDDX
 * @subpackage PaymentGateway
 * @version 1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/EnhancedTransactionManager.php';

class Enhanced3DSecure {
    
    private $logger;
    private $transactionManager;
    private $gatewayParams;
    
    // 3D Secure 2.2.0 constants
    const VERSION = '2.2.0';
    const PROTOCOL_VERSION = '2.2.0';
    const CHALLENGE_TIMEOUT = 300; // 5 minutes
    const CHALLENGE_WINDOW_SIZES = [
        '01' => '250x400',
        '02' => '390x400', 
        '03' => '500x600',
        '04' => '600x400',
        '05' => '100%x100%' // Full screen
    ];
    
    // Authentication status codes
    const AUTH_SUCCESS = 'Y';           // Authentication successful
    const AUTH_FAILED = 'N';            // Authentication failed
    const AUTH_ATTEMPTED = 'A';         // Authentication attempted
    const AUTH_UNAVAILABLE = 'U';       // Authentication unavailable
    const AUTH_REJECTED = 'R';          // Authentication rejected
    const AUTH_CHALLENGE = 'C';         // Challenge required
    
    // ECI (Electronic Commerce Indicator) values
    const ECI_MASTERCARD_3DS = '02';    // Mastercard 3DS authenticated
    const ECI_VISA_3DS = '05';          // Visa 3DS authenticated
    const ECI_MASTERCARD_ATTEMPT = '01'; // Mastercard authentication attempted
    const ECI_VISA_ATTEMPT = '06';      // Visa authentication attempted
    const ECI_NON_3DS = '07';           // Non-3DS transaction
    
    public function __construct($gatewayParams, $logger = null, $transactionManager = null) {
        $this->gatewayParams = $gatewayParams;
        $this->logger = $logger ?: new LahzaLogger();
        $this->transactionManager = $transactionManager ?: new EnhancedTransactionManager($gatewayParams, $this->logger);
    }
    
    /**
     * Initialize 3D Secure authentication with enhanced browser fingerprinting
     */
    public function initializeAuthentication($params, $cardData) {
        $this->logger->info('Initializing 3D Secure authentication', [
            'invoice_id' => $params['invoiceid'],
            'amount' => $params['amount'],
            'currency' => $params['currency'],
            'card_type' => $this->detectCardType($cardData['number'])
        ], '3ds');
        
        // Generate unique transaction ID
        $transactionId = $this->generateTransactionId($params['invoiceid']);
        
        // Collect comprehensive browser information
        $browserInfo = $this->collectBrowserInformation();
        
        // Prepare 3DS authentication request
        $threeDSData = [
            'version' => self::VERSION,
            'protocol_version' => self::PROTOCOL_VERSION,
            'transaction_id' => $transactionId,
            'merchant_id' => $params['publicKey'],
            'merchant_name' => $params['companyname'] ?? 'WIDDX Merchant',
            'merchant_category_code' => '5734', // Computer software stores
            'merchant_country_code' => 'US',
            
            // Transaction details
            'amount' => $params['amount'],
            'currency' => $params['currency'],
            'transaction_type' => '01', // Goods/Service purchase
            'purchase_date' => date('YmdHis'),
            
            // Card information
            'card_number' => $cardData['number'],
            'cardholder_name' => $cardData['name'],
            'card_expiry_month' => $cardData['expiry_month'],
            'card_expiry_year' => $cardData['expiry_year'],
            
            // Browser and device information
            'browser_info' => $browserInfo,
            
            // Challenge configuration
            'challenge_window_size' => $this->getOptimalChallengeWindowSize($browserInfo),
            'challenge_timeout' => self::CHALLENGE_TIMEOUT,
            
            // URLs
            'notification_url' => $this->buildNotificationUrl($params),
            'return_url' => $params['returnurl'],
            'challenge_completion_url' => $this->buildChallengeCompletionUrl($params),
            
            // Risk assessment
            'risk_data' => $this->collectRiskData($params, $cardData),
            
            // Merchant preferences
            'three_ds_requestor_challenge_indicator' => $this->getChallengeIndicator($params),
            'three_ds_requestor_authentication_indicator' => '01', // Payment transaction
            
            // Additional security
            'sdk_app_id' => null, // Web-based transaction
            'sdk_reference_number' => null,
            'sdk_transaction_id' => null
        ];
        
        // Store 3DS initialization data
        $this->store3DSData($transactionId, [
            'status' => 'initialized',
            'data' => $threeDSData,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        $this->logger->info('3D Secure initialization completed', [
            'transaction_id' => $transactionId,
            'browser_fingerprint' => $this->generateBrowserFingerprint($browserInfo)
        ], '3ds');
        
        return $threeDSData;
    }
    
    /**
     * Collect comprehensive browser information for 3DS 2.2.0
     */
    private function collectBrowserInformation() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '*/*';
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'en-US';
        $acceptEncoding = $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '';
        
        return [
            // Required fields
            'accept_header' => $acceptHeader,
            'user_agent' => $userAgent,
            'browser_language' => $this->extractPrimaryLanguage($acceptLanguage),
            'browser_color_depth' => 24, // Will be updated via JavaScript
            'browser_screen_height' => 1080, // Will be updated via JavaScript
            'browser_screen_width' => 1920, // Will be updated via JavaScript
            'browser_timezone' => date('P'),
            'browser_java_enabled' => false, // Will be updated via JavaScript
            'browser_javascript_enabled' => true,
            
            // Additional fields for enhanced security
            'browser_accept_encoding' => $acceptEncoding,
            'browser_accept_language' => $acceptLanguage,
            'ip_address' => $this->getClientIpAddress(),
            'browser_fingerprint' => $this->generateBrowserFingerprint([
                'user_agent' => $userAgent,
                'accept_header' => $acceptHeader,
                'accept_language' => $acceptLanguage,
                'ip_address' => $this->getClientIpAddress()
            ]),
            
            // Device information
            'device_type' => $this->detectDeviceType($userAgent),
            'mobile_device' => $this->isMobileDevice($userAgent),
            'browser_name' => $this->detectBrowserName($userAgent),
            'browser_version' => $this->detectBrowserVersion($userAgent),
            'operating_system' => $this->detectOperatingSystem($userAgent),
            
            // Network information
            'connection_type' => $this->detectConnectionType(),
            'proxy_detected' => $this->detectProxy(),
            
            // Timestamp
            'collection_timestamp' => time(),
            'collection_date' => date('c')
        ];
    }
    
    /**
     * Process 3D Secure authentication response
     */
    public function processAuthenticationResponse($responseData) {
        $transactionId = $responseData['transaction_id'] ?? '';
        $authStatus = $responseData['authentication_status'] ?? '';
        
        $this->logger->info('Processing 3DS authentication response', [
            'transaction_id' => $transactionId,
            'auth_status' => $authStatus,
            'eci' => $responseData['eci'] ?? 'not_provided'
        ], '3ds');
        
        // Validate response signature
        if (!$this->validateResponseSignature($responseData)) {
            $this->logger->error('Invalid 3DS response signature', [
                'transaction_id' => $transactionId
            ], '3ds');
            return false;
        }
        
        // Update transaction status based on authentication result
        switch ($authStatus) {
            case self::AUTH_SUCCESS:
                return $this->handleSuccessfulAuthentication($responseData);
                
            case self::AUTH_ATTEMPTED:
                return $this->handleAttemptedAuthentication($responseData);
                
            case self::AUTH_FAILED:
                return $this->handleFailedAuthentication($responseData);
                
            case self::AUTH_UNAVAILABLE:
                return $this->handleUnavailableAuthentication($responseData);
                
            case self::AUTH_REJECTED:
                return $this->handleRejectedAuthentication($responseData);
                
            case self::AUTH_CHALLENGE:
                return $this->handleChallengeRequired($responseData);
                
            default:
                $this->logger->warning('Unknown 3DS authentication status', [
                    'transaction_id' => $transactionId,
                    'auth_status' => $authStatus
                ], '3ds');
                return false;
        }
    }
    
    /**
     * Handle successful 3D Secure authentication
     */
    private function handleSuccessfulAuthentication($responseData) {
        $transactionId = $responseData['transaction_id'];
        
        $this->logger->info('3DS authentication successful', [
            'transaction_id' => $transactionId,
            'eci' => $responseData['eci'],
            'cavv' => isset($responseData['cavv']) ? 'present' : 'absent'
        ], '3ds');
        
        // Update transaction status
        $this->transactionManager->updateStatus(
            $transactionId,
            EnhancedTransactionManager::TDS_STATUS_AUTHENTICATED,
            '3D Secure authentication successful',
            [
                'eci' => $responseData['eci'],
                'cavv' => $responseData['cavv'] ?? '',
                'xid' => $responseData['xid'] ?? '',
                'authentication_value' => $responseData['authentication_value'] ?? '',
                'ds_transaction_id' => $responseData['ds_transaction_id'] ?? ''
            ],
            EnhancedTransactionManager::PRIORITY_HIGH
        );
        
        // Store authentication data
        $this->store3DSData($transactionId, [
            'status' => 'authenticated',
            'auth_status' => self::AUTH_SUCCESS,
            'eci' => $responseData['eci'],
            'cavv' => $responseData['cavv'] ?? '',
            'xid' => $responseData['xid'] ?? '',
            'authentication_timestamp' => date('Y-m-d H:i:s')
        ]);
        
        return [
            'success' => true,
            'status' => 'authenticated',
            'continue_payment' => true,
            'authentication_data' => [
                'eci' => $responseData['eci'],
                'cavv' => $responseData['cavv'] ?? '',
                'xid' => $responseData['xid'] ?? ''
            ]
        ];
    }
    
    /**
     * Handle challenge required scenario
     */
    private function handleChallengeRequired($responseData) {
        $transactionId = $responseData['transaction_id'];
        $challengeUrl = $responseData['challenge_url'] ?? '';
        
        $this->logger->info('3DS challenge required', [
            'transaction_id' => $transactionId,
            'challenge_url_present' => !empty($challengeUrl)
        ], '3ds');
        
        // Update transaction status
        $this->transactionManager->updateStatus(
            $transactionId,
            EnhancedTransactionManager::TDS_STATUS_CHALLENGE,
            '3D Secure challenge required',
            [
                'challenge_url' => $challengeUrl,
                'challenge_window_size' => $responseData['challenge_window_size'] ?? '05',
                'challenge_timeout' => self::CHALLENGE_TIMEOUT
            ],
            EnhancedTransactionManager::PRIORITY_HIGH
        );
        
        // Store challenge data
        $this->store3DSData($transactionId, [
            'status' => 'challenge_required',
            'challenge_url' => $challengeUrl,
            'challenge_initiated_at' => date('Y-m-d H:i:s'),
            'challenge_expires_at' => date('Y-m-d H:i:s', time() + self::CHALLENGE_TIMEOUT)
        ]);
        
        return [
            'success' => true,
            'status' => 'challenge_required',
            'challenge_url' => $challengeUrl,
            'challenge_window_size' => $responseData['challenge_window_size'] ?? '05',
            'challenge_timeout' => self::CHALLENGE_TIMEOUT
        ];
    }
    
    /**
     * Generate enhanced browser fingerprint
     */
    private function generateBrowserFingerprint($browserInfo) {
        $fingerprintData = [
            'user_agent' => $browserInfo['user_agent'] ?? '',
            'accept_header' => $browserInfo['accept_header'] ?? '',
            'accept_language' => $browserInfo['accept_language'] ?? '',
            'ip_address' => $browserInfo['ip_address'] ?? '',
            'timezone' => date('P')
        ];
        
        return hash('sha256', json_encode($fingerprintData));
    }
    
    /**
     * Detect card type from card number
     */
    private function detectCardType($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        if (preg_match('/^4/', $cardNumber)) {
            return 'visa';
        } elseif (preg_match('/^5[1-5]/', $cardNumber) || preg_match('/^2[2-7]/', $cardNumber)) {
            return 'mastercard';
        } elseif (preg_match('/^3[47]/', $cardNumber)) {
            return 'amex';
        } elseif (preg_match('/^6(?:011|5)/', $cardNumber)) {
            return 'discover';
        }
        
        return 'unknown';
    }
    
    /**
     * Get optimal challenge window size based on device
     */
    private function getOptimalChallengeWindowSize($browserInfo) {
        if ($browserInfo['mobile_device']) {
            return '05'; // Full screen for mobile
        }
        
        // Desktop - use medium size
        return '03'; // 500x600
    }
    
    /**
     * Get challenge indicator based on risk assessment
     */
    private function getChallengeIndicator($params) {
        // 01 = No preference
        // 02 = No challenge requested
        // 03 = Challenge requested (3DS Requestor preference)
        // 04 = Challenge requested (Mandate)
        
        $amount = floatval($params['amount']);
        
        // Request challenge for high-value transactions
        if ($amount > 1000) {
            return '03';
        }
        
        // No preference for regular transactions
        return '01';
    }
    
    /**
     * Collect risk assessment data
     */
    private function collectRiskData($params, $cardData) {
        return [
            'transaction_amount' => $params['amount'],
            'transaction_currency' => $params['currency'],
            'customer_ip' => $this->getClientIpAddress(),
            'customer_email' => $params['clientdetails']['email'] ?? '',
            'billing_country' => $params['clientdetails']['country'] ?? '',
            'shipping_country' => $params['clientdetails']['country'] ?? '',
            'account_age' => $this->calculateAccountAge($params['clientdetails']['userid'] ?? 0),
            'previous_transactions' => $this->getPreviousTransactionCount($params['clientdetails']['userid'] ?? 0),
            'risk_score' => $this->calculateRiskScore($params, $cardData)
        ];
    }
    
    /**
     * Calculate risk score (0-100, higher = riskier)
     */
    private function calculateRiskScore($params, $cardData) {
        $score = 0;
        
        // Amount-based risk
        $amount = floatval($params['amount']);
        if ($amount > 1000) $score += 20;
        if ($amount > 5000) $score += 30;
        
        // New customer risk
        $userId = $params['clientdetails']['userid'] ?? 0;
        if ($userId == 0) $score += 25;
        
        // IP-based risk
        if ($this->detectProxy()) $score += 15;
        
        // Browser risk
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($userAgent)) $score += 10;
        
        return min(100, $score);
    }
    
    /**
     * Utility methods
     */
    private function generateTransactionId($invoiceId) {
        return 'WIDDX_3DS_' . $invoiceId . '_' . time() . '_' . mt_rand(1000, 9999);
    }
    
    private function getClientIpAddress() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    private function extractPrimaryLanguage($acceptLanguage) {
        if (empty($acceptLanguage)) return 'en';
        
        $languages = explode(',', $acceptLanguage);
        $primary = trim(explode(';', $languages[0])[0]);
        
        return substr($primary, 0, 2);
    }
    
    private function isMobileDevice($userAgent) {
        return preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent);
    }
    
    private function detectDeviceType($userAgent) {
        if (preg_match('/iPad/i', $userAgent)) return 'tablet';
        if (preg_match('/Mobile|Android|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent)) return 'mobile';
        return 'desktop';
    }
    
    private function detectBrowserName($userAgent) {
        if (preg_match('/Chrome/i', $userAgent)) return 'Chrome';
        if (preg_match('/Firefox/i', $userAgent)) return 'Firefox';
        if (preg_match('/Safari/i', $userAgent)) return 'Safari';
        if (preg_match('/Edge/i', $userAgent)) return 'Edge';
        if (preg_match('/Opera/i', $userAgent)) return 'Opera';
        return 'Unknown';
    }
    
    private function detectBrowserVersion($userAgent) {
        if (preg_match('/Chrome\/([0-9.]+)/i', $userAgent, $matches)) return $matches[1];
        if (preg_match('/Firefox\/([0-9.]+)/i', $userAgent, $matches)) return $matches[1];
        if (preg_match('/Version\/([0-9.]+).*Safari/i', $userAgent, $matches)) return $matches[1];
        return 'Unknown';
    }
    
    private function detectOperatingSystem($userAgent) {
        if (preg_match('/Windows NT ([0-9.]+)/i', $userAgent, $matches)) return 'Windows ' . $matches[1];
        if (preg_match('/Mac OS X ([0-9_]+)/i', $userAgent, $matches)) return 'macOS ' . str_replace('_', '.', $matches[1]);
        if (preg_match('/Android ([0-9.]+)/i', $userAgent, $matches)) return 'Android ' . $matches[1];
        if (preg_match('/iPhone OS ([0-9_]+)/i', $userAgent, $matches)) return 'iOS ' . str_replace('_', '.', $matches[1]);
        if (preg_match('/Linux/i', $userAgent)) return 'Linux';
        return 'Unknown';
    }
    
    private function detectConnectionType() {
        // This would typically require additional data or APIs
        return 'unknown';
    }
    
    private function detectProxy() {
        $proxyHeaders = [
            'HTTP_VIA',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED'
        ];
        
        foreach ($proxyHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                return true;
            }
        }
        
        return false;
    }
    
    private function buildNotificationUrl($params) {
        return rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza_3ds.php';
    }
    
    private function buildChallengeCompletionUrl($params) {
        return rtrim($params['systemurl'], '/') . '/modules/gateways/lahza/3ds_challenge_complete.php';
    }
    
    private function calculateAccountAge($userId) {
        if ($userId == 0) return 0;
        
        try {
            $client = Capsule::table('tblclients')->where('id', $userId)->first();
            if ($client) {
                return floor((time() - strtotime($client->datecreated)) / 86400); // Days
            }
        } catch (Exception $e) {
            $this->logger->error('Failed to calculate account age', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ], '3ds');
        }
        
        return 0;
    }
    
    private function getPreviousTransactionCount($userId) {
        if ($userId == 0) return 0;
        
        try {
            return Capsule::table('tblaccounts')
                ->join('tblinvoices', 'tblaccounts.invoiceid', '=', 'tblinvoices.id')
                ->where('tblinvoices.userid', $userId)
                ->where('tblaccounts.amountin', '>', 0)
                ->count();
        } catch (Exception $e) {
            $this->logger->error('Failed to get previous transaction count', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ], '3ds');
        }
        
        return 0;
    }
    
    private function validateResponseSignature($responseData) {
        // Implement signature validation based on your gateway's requirements
        $signature = $responseData['signature'] ?? '';
        unset($responseData['signature']);
        
        $expectedSignature = hash_hmac('sha256', json_encode($responseData), $this->gatewayParams['webhookSecret']);
        
        return hash_equals($expectedSignature, $signature);
    }
    
    private function store3DSData($transactionId, $data) {
        try {
            $existingData = Capsule::table('lahza_3ds_data')
                ->where('transaction_id', $transactionId)
                ->first();
            
            if ($existingData) {
                Capsule::table('lahza_3ds_data')
                    ->where('transaction_id', $transactionId)
                    ->update([
                        'data' => json_encode($data),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                Capsule::table('lahza_3ds_data')->insert([
                    'transaction_id' => $transactionId,
                    'data' => json_encode($data),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        } catch (Exception $e) {
            $this->logger->error('Failed to store 3DS data', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], '3ds');
        }
    }
    
    /**
     * Create 3DS data table if it doesn't exist
     */
    public function createTables() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS lahza_3ds_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transaction_id VARCHAR(255) UNIQUE NOT NULL,
                data JSON NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_transaction_id (transaction_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            Capsule::statement($sql);
            
            $this->logger->info('3DS data table created successfully', [], '3ds');
        } catch (Exception $e) {
            $this->logger->error('Failed to create 3DS data table', [
                'error' => $e->getMessage()
            ], '3ds');
        }
    }
    
    // Additional methods for handling other authentication statuses...
    private function handleAttemptedAuthentication($responseData) {
        // Implementation for attempted authentication
        return $this->handleSuccessfulAuthentication($responseData); // Proceed with payment
    }
    
    private function handleFailedAuthentication($responseData) {
        $transactionId = $responseData['transaction_id'];
        
        $this->transactionManager->updateStatus(
            $transactionId,
            EnhancedTransactionManager::TDS_STATUS_FAILED,
            '3D Secure authentication failed',
            ['error_code' => $responseData['error_code'] ?? 'unknown'],
            EnhancedTransactionManager::PRIORITY_HIGH
        );
        
        return ['success' => false, 'status' => 'failed', 'message' => '3D Secure authentication failed'];
    }
    
    private function handleUnavailableAuthentication($responseData) {
        // Proceed without 3DS when unavailable
        return ['success' => true, 'status' => 'unavailable', 'continue_payment' => true];
    }
    
    private function handleRejectedAuthentication($responseData) {
        return $this->handleFailedAuthentication($responseData);
    }
}
