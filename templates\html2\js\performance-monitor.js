// ===== PERFORMANCE MONITORING SCRIPT =====

class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.observers = [];
        this.startTime = performance.now();
        this.init();
    }

    init() {
        this.measureCoreWebVitals();
        this.measureLoadTimes();
        this.measureResourceTiming();
        this.setupContinuousMonitoring();
    }

    // Core Web Vitals Measurement
    measureCoreWebVitals() {
        // Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.lcp = lastEntry.startTime;
                    this.logMetric('LCP', lastEntry.startTime, 'ms', 2500); // Good: <2.5s
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                this.observers.push(lcpObserver);
            } catch (e) {
                // LCP measurement not supported - silently continue
            }

            // First Input Delay (FID)
            try {
                const fidObserver = new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    entries.forEach(entry => {
                        const fid = entry.processingStart - entry.startTime;
                        this.metrics.fid = fid;
                        this.logMetric('FID', fid, 'ms', 100); // Good: <100ms
                    });
                });
                fidObserver.observe({ entryTypes: ['first-input'] });
                this.observers.push(fidObserver);
            } catch (e) {
                // FID measurement not supported - silently continue
            }

            // Cumulative Layout Shift (CLS)
            try {
                let clsValue = 0;
                const clsObserver = new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    entries.forEach(entry => {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    });
                    this.metrics.cls = clsValue;
                    this.logMetric('CLS', clsValue, '', 0.1); // Good: <0.1
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
                this.observers.push(clsObserver);
            } catch (e) {
                // CLS measurement not supported - silently continue
            }
        }
    }

    // Load Time Measurements
    measureLoadTimes() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation) {
                    // Time to First Byte
                    const ttfb = navigation.responseStart - navigation.fetchStart;
                    this.metrics.ttfb = ttfb;
                    this.logMetric('TTFB', ttfb, 'ms', 600); // Good: <600ms

                    // DOM Content Loaded
                    const dcl = navigation.domContentLoadedEventEnd - navigation.fetchStart;
                    this.metrics.dcl = dcl;
                    this.logMetric('DOM Content Loaded', dcl, 'ms', 1500);

                    // Full Page Load
                    const pageLoad = navigation.loadEventEnd - navigation.fetchStart;
                    this.metrics.pageLoad = pageLoad;
                    this.logMetric('Page Load', pageLoad, 'ms', 3000);

                    // DNS Lookup
                    const dns = navigation.domainLookupEnd - navigation.domainLookupStart;
                    this.metrics.dns = dns;
                    this.logMetric('DNS Lookup', dns, 'ms', 200);

                    // TCP Connection
                    const tcp = navigation.connectEnd - navigation.connectStart;
                    this.metrics.tcp = tcp;
                    this.logMetric('TCP Connection', tcp, 'ms', 200);
                }

                // First Paint and First Contentful Paint
                const paintEntries = performance.getEntriesByType('paint');
                paintEntries.forEach(entry => {
                    if (entry.name === 'first-paint') {
                        this.metrics.fp = entry.startTime;
                        this.logMetric('First Paint', entry.startTime, 'ms', 1000);
                    } else if (entry.name === 'first-contentful-paint') {
                        this.metrics.fcp = entry.startTime;
                        this.logMetric('First Contentful Paint', entry.startTime, 'ms', 1800);
                    }
                });

                this.generateReport();
            }, 100);
        });
    }

    // Resource Timing Analysis
    measureResourceTiming() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const resources = performance.getEntriesByType('resource');
                const resourceMetrics = {
                    css: [],
                    js: [],
                    images: [],
                    fonts: [],
                    other: []
                };

                resources.forEach(resource => {
                    const duration = resource.responseEnd - resource.startTime;
                    const size = resource.transferSize || 0;
                    
                    const resourceData = {
                        name: resource.name,
                        duration: duration,
                        size: size,
                        type: this.getResourceType(resource.name)
                    };

                    if (resource.name.includes('.css')) {
                        resourceMetrics.css.push(resourceData);
                    } else if (resource.name.includes('.js')) {
                        resourceMetrics.js.push(resourceData);
                    } else if (this.isImageResource(resource.name)) {
                        resourceMetrics.images.push(resourceData);
                    } else if (this.isFontResource(resource.name)) {
                        resourceMetrics.fonts.push(resourceData);
                    } else {
                        resourceMetrics.other.push(resourceData);
                    }
                });

                this.metrics.resources = resourceMetrics;
                this.analyzeResources(resourceMetrics);
            }, 500);
        });
    }

    // Continuous Performance Monitoring
    setupContinuousMonitoring() {
        // Monitor memory usage
        if ('memory' in performance) {
            setInterval(() => {
                this.metrics.memory = {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                };
            }, 5000);
        }

        // Monitor frame rate
        let frameCount = 0;
        let lastTime = performance.now();
        
        const measureFPS = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                this.metrics.fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }

    // Helper Methods
    getResourceType(url) {
        if (url.includes('.css')) return 'CSS';
        if (url.includes('.js')) return 'JavaScript';
        if (this.isImageResource(url)) return 'Image';
        if (this.isFontResource(url)) return 'Font';
        return 'Other';
    }

    isImageResource(url) {
        return /\.(jpg|jpeg|png|gif|webp|svg|ico)(\?|$)/i.test(url);
    }

    isFontResource(url) {
        return /\.(woff|woff2|ttf|otf|eot)(\?|$)/i.test(url) || url.includes('fonts');
    }

    logMetric(name, value, unit, threshold) {
        const status = threshold ? (value <= threshold ? '✅' : '⚠️') : '';
        const formattedValue = typeof value === 'number' ? value.toFixed(2) : value;
        console.log(`${status} ${name}: ${formattedValue}${unit}`);
    }

    analyzeResources(resources) {
        console.group('📊 Resource Analysis');
        
        Object.keys(resources).forEach(type => {
            if (resources[type].length > 0) {
                const totalSize = resources[type].reduce((sum, r) => sum + r.size, 0);
                const avgDuration = resources[type].reduce((sum, r) => sum + r.duration, 0) / resources[type].length;
                
                console.log(`${type.toUpperCase()}: ${resources[type].length} files, ${(totalSize/1024).toFixed(2)}KB total, ${avgDuration.toFixed(2)}ms avg load time`);
            }
        });
        
        console.groupEnd();
    }

    generateReport() {
        console.group('🚀 Performance Report');
        console.log('Core Web Vitals:', {
            LCP: this.metrics.lcp ? `${this.metrics.lcp.toFixed(2)}ms` : 'N/A',
            FID: this.metrics.fid ? `${this.metrics.fid.toFixed(2)}ms` : 'N/A',
            CLS: this.metrics.cls ? this.metrics.cls.toFixed(3) : 'N/A'
        });
        
        console.log('Load Times:', {
            TTFB: this.metrics.ttfb ? `${this.metrics.ttfb.toFixed(2)}ms` : 'N/A',
            'First Paint': this.metrics.fp ? `${this.metrics.fp.toFixed(2)}ms` : 'N/A',
            'First Contentful Paint': this.metrics.fcp ? `${this.metrics.fcp.toFixed(2)}ms` : 'N/A',
            'DOM Content Loaded': this.metrics.dcl ? `${this.metrics.dcl.toFixed(2)}ms` : 'N/A',
            'Page Load': this.metrics.pageLoad ? `${this.metrics.pageLoad.toFixed(2)}ms` : 'N/A'
        });
        
        if (this.metrics.memory) {
            console.log('Memory Usage:', {
                'Used': `${(this.metrics.memory.used / 1048576).toFixed(2)}MB`,
                'Total': `${(this.metrics.memory.total / 1048576).toFixed(2)}MB`
            });
        }
        
        console.groupEnd();
    }

    // Public API
    getMetrics() {
        return this.metrics;
    }

    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];
    }
}

// Initialize performance monitoring
const performanceMonitor = new PerformanceMonitor();

// Export for use in other scripts
window.PerformanceMonitor = PerformanceMonitor;
window.performanceMonitor = performanceMonitor;
