<?php

class Env {
    private static $loaded = false;
    private static $env = [];
    private static $requiredVars = [
        'DB_HOST',
        'DB_USER',
        'DB_PASS',
        'DB_NAME',
        'APP_ENV',
        'APP_KEY'
    ];

    /**
     * Load environment variables from .env file
     */
    private static function load() {
        if (self::$loaded) {
            return;
        }

        $envFile = __DIR__ . '/../.env';
        
        if (!file_exists($envFile)) {
            throw new \RuntimeException('Environment file (.env) not found. Please create it.');
        }

        if (!is_readable($envFile)) {
            throw new \RuntimeException('Environment file (.env) is not readable.');
        }

        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Skip comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse the line
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value, " \t\n\r\0\x0B\"'");
                
                // Handle variable references like ${VAR}
                if (preg_match_all('/\${(\w+)}/', $value, $matches)) {
                    foreach ($matches[1] as $i => $varName) {
                        $varValue = self::get($varName, '');
                        $value = str_replace($matches[0][$i], $varValue, $value);
                    }
                }
                
                self::$env[$key] = $value;
                
                // Also put it in $_ENV and $_SERVER for compatibility
                $_ENV[$key] = $value;
                $_SERVER[$key] = $value;
            }
        }
        
        // Check required variables
        foreach (self::$requiredVars as $var) {
            if (!self::has($var)) {
                throw new \RuntimeException("Required environment variable '$var' is not set.");
            }
        }
        
        self::$loaded = true;
    }

    /**
     * Get an environment variable
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null) {
        self::load();
        
        // Check in this order: getenv -> $_ENV -> self::$env -> $default
        $value = getenv($key);
        if ($value !== false) {
            return $value;
        }
        
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }
        
        if (isset(self::$env[$key])) {
            return self::$env[$key];
        }
        
        if ($default !== null) {
            return $default;
        }
        
        throw new \RuntimeException("Environment variable '$key' is not set and no default value provided.");
    }
    
    /**
     * Initialize the environment
     */
    public static function init() {
        self::load();
    }
    
    /**
     * Get all environment variables
     * 
     * @return array
     */
    public static function all() {
        self::load();
        return self::$env;
    }
    
    /**
     * Check if an environment variable exists
     * 
     * @param string $key
     * @return bool
     */
    public static function has($key) {
        self::load();
        return getenv($key) !== false || 
               isset($_ENV[$key]) || 
               isset(self::$env[$key]);
    }
}

// Auto-load environment variables when this file is included
Env::init();
