+++
next = "/languages/overrides/"
prev = "/languages/locales/"
title = "Encoding"
weight = 30

+++

By default, language files in WHMCS use UTF-8 encoding without a Byte Order Marker character.

When modifying language files it is important to maintain the same encoding.

If you have chosen to change the system charset to something other than UTF-8 (for example iso-8859-1) then the language files will need to be re-saved with ANSI encoding (also without a Byte Order Marker). Most text editors have this option (including Notepad).
