/*!
 * WIDDX Modern Header & Footer JavaScript
 * Interactive functionality for modern components
 * Copyright (c) 2025 WIDDX Development Team
 */

(function($) {
    'use strict';

    // Initialize when DOM is ready
    $(document).ready(function() {
        initModernHeader();
        initModernFooter();
        initProgressBar();
        initQuickContact();
        initPWAPrompt();
        initBackToTop();
        initMobileMenu();
    });

    /**
     * Initialize Modern Header Functionality
     */
    function initModernHeader() {
        // Sticky header on scroll
        let lastScrollTop = 0;
        const header = $('.widdx-main-nav');
        
        $(window).scroll(function() {
            const scrollTop = $(this).scrollTop();
            
            if (scrollTop > 100) {
                header.addClass('scrolled');
            } else {
                header.removeClass('scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.addClass('header-hidden');
            } else {
                header.removeClass('header-hidden');
            }
            
            lastScrollTop = scrollTop;
        });

        // Search functionality
        $('.widdx-search-widget .form-control').on('focus', function() {
            $(this).parent().addClass('search-focused');
        }).on('blur', function() {
            $(this).parent().removeClass('search-focused');
        });

        // Cart button animation
        $('.widdx-cart-btn').on('mouseenter', function() {
            $('.widdx-cart-count').addClass('animate__animated animate__pulse');
        }).on('mouseleave', function() {
            $('.widdx-cart-count').removeClass('animate__animated animate__pulse');
        });

        // Language/Currency button
        $('.widdx-lang-btn, .widdx-lang-currency-btn').on('click', function() {
            $(this).addClass('loading');
            setTimeout(() => {
                $(this).removeClass('loading');
            }, 1000);
        });
    }

    /**
     * Initialize Modern Footer Functionality
     */
    function initModernFooter() {
        // Newsletter subscription
        $('.widdx-newsletter-form').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const email = form.find('.widdx-newsletter-input').val();
            const button = form.find('.widdx-newsletter-btn');
            
            if (!isValidEmail(email)) {
                showNotification('Please enter a valid email address', 'error');
                return;
            }
            
            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Subscribing...');
            
            // Simulate API call
            setTimeout(() => {
                button.prop('disabled', false).html('<i class="fas fa-check mr-2"></i>Subscribed!');
                form.find('.widdx-newsletter-input').val('');
                showNotification('Successfully subscribed to newsletter!', 'success');
                
                setTimeout(() => {
                    button.html('<i class="fas fa-paper-plane mr-2"></i>Subscribe');
                }, 2000);
            }, 1500);
        });

        // Footer menu animations
        $('.widdx-footer-menu a').on('mouseenter', function() {
            $(this).addClass('animate__animated animate__fadeInLeft');
        }).on('mouseleave', function() {
            $(this).removeClass('animate__animated animate__fadeInLeft');
        });

        // Social media links
        $('.widdx-social-icons .btn').on('click', function(e) {
            e.preventDefault();
            const href = $(this).attr('href');
            if (href && href !== '#') {
                window.open(href, '_blank', 'noopener,noreferrer');
            }
        });
    }

    /**
     * Initialize Progress Bar
     */
    function initProgressBar() {
        const progressBar = $('#widdxProgressBar');
        const progressFill = progressBar.find('.widdx-progress-fill');
        
        // Show progress bar on page load
        $(window).on('beforeunload', function() {
            progressBar.addClass('active');
            progressFill.css('width', '30%');
        });
        
        // Hide progress bar when page loads
        $(window).on('load', function() {
            progressFill.css('width', '100%');
            setTimeout(() => {
                progressBar.removeClass('active');
                progressFill.css('width', '0%');
            }, 500);
        });
        
        // Show progress on AJAX requests
        $(document).ajaxStart(function() {
            progressBar.addClass('active');
            progressFill.css('width', '50%');
        }).ajaxComplete(function() {
            progressFill.css('width', '100%');
            setTimeout(() => {
                progressBar.removeClass('active');
                progressFill.css('width', '0%');
            }, 300);
        });
    }

    /**
     * Initialize Quick Contact Widget
     */
    function initQuickContact() {
        const quickContact = $('#widdxQuickContact');
        const toggle = quickContact.find('.widdx-quick-contact-toggle');
        const panel = quickContact.find('.widdx-quick-contact-panel');
        const close = quickContact.find('.widdx-quick-contact-close');
        
        toggle.on('click', function() {
            quickContact.toggleClass('active');
        });
        
        close.on('click', function() {
            quickContact.removeClass('active');
        });
        
        // Close on outside click
        $(document).on('click', function(e) {
            if (!quickContact.is(e.target) && quickContact.has(e.target).length === 0) {
                quickContact.removeClass('active');
            }
        });
        
        // Auto-show after delay
        setTimeout(() => {
            if (!localStorage.getItem('widdx_quick_contact_shown')) {
                quickContact.addClass('active');
                localStorage.setItem('widdx_quick_contact_shown', 'true');
                
                setTimeout(() => {
                    quickContact.removeClass('active');
                }, 5000);
            }
        }, 10000);
    }

    /**
     * Initialize PWA Install Prompt
     */
    function initPWAPrompt() {
        const pwaPrompt = $('#widdxPwaInstallPrompt');
        const installBtn = $('#widdxPwaInstallButton');
        const laterBtn = $('#widdxPwaInstallLater');
        const closeBtn = pwaPrompt.find('.widdx-pwa-close');
        
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            if (!localStorage.getItem('widdx_pwa_dismissed')) {
                setTimeout(() => {
                    pwaPrompt.addClass('active');
                }, 5000);
            }
        });
        
        installBtn.on('click', function() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        showNotification('App installation started!', 'success');
                    }
                    deferredPrompt = null;
                    pwaPrompt.removeClass('active');
                });
            }
        });
        
        laterBtn.add(closeBtn).on('click', function() {
            pwaPrompt.removeClass('active');
            localStorage.setItem('widdx_pwa_dismissed', 'true');
        });
    }

    /**
     * Initialize Back to Top Button
     */
    function initBackToTop() {
        const backToTop = $('.widdx-back-to-top');
        
        backToTop.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 800, 'easeInOutCubic');
        });
        
        // Show/hide based on scroll position
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTop.fadeIn();
            } else {
                backToTop.fadeOut();
            }
        });
    }

    /**
     * Initialize Mobile Menu
     */
    function initMobileMenu() {
        const mobileToggle = $('.widdx-mobile-toggle');
        const mobileOverlay = $('.widdx-mobile-overlay');
        const mobileClose = $('.widdx-mobile-close');
        
        mobileToggle.on('click', function() {
            mobileOverlay.addClass('active');
            $('body').addClass('mobile-menu-open');
            $(this).attr('aria-expanded', 'true');
        });
        
        mobileClose.add(mobileOverlay).on('click', function(e) {
            if (e.target === this) {
                mobileOverlay.removeClass('active');
                $('body').removeClass('mobile-menu-open');
                mobileToggle.attr('aria-expanded', 'false');
            }
        });
        
        // Close on escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && mobileOverlay.hasClass('active')) {
                mobileOverlay.removeClass('active');
                $('body').removeClass('mobile-menu-open');
                mobileToggle.attr('aria-expanded', 'false');
            }
        });
    }

    /**
     * Utility Functions
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showNotification(message, type = 'info') {
        const notification = $(`
            <div class="widdx-notification widdx-notification-${type}">
                <div class="widdx-notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.addClass('show');
        }, 100);
        
        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Add easing function for smooth scrolling
    $.easing.easeInOutCubic = function (x, t, b, c, d) {
        if ((t/=d/2) < 1) return c/2*t*t*t + b;
        return c/2*((t-=2)*t*t + 2) + b;
    };

})(jQuery);
