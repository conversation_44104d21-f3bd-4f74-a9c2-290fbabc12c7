+++
next = "/oauth/auth-workflow"
prev = "/oauth/"
title = "Introduction"
toc = true
weight = 5

+++

If you have an app or service that needs to interact with WHMCS on behalf of your users, you should use OAuth.

WHMCS uses OAuth 2, an open specification, which allows users to authenticate with WHMCS to both verify their identity and give your app permission to access their data.

Once authorization is completed by a user, the OAuth process returns an access token to your app. The access token is a string generated by the WHMCS instance that you'll need to send with each subsequent API request to uniquely identify both your app and the end user.

There are several reasons we use OAuth. Most importantly, your app doesn't need to store or transmit the user's WHMCS password. OAuth also allows the user to authorize only a limited set of permissions and the user may revoke access at any time. This makes OAuth a safer and more secure form of API authorization for your users.

{{% notice info %}}
OAuth is an open protocol we support in WHMCS for allowing third-party applications to obtain limited access to the WHMCS service on behalf of users.
{{% /notice %}}
