+++
chapter = true
icon = "<i class='fa fa-retweet fa-fw'></i>"
next = "/hooks/getting-started"
title = "Hooks"
weight = 0

+++

## Introduction

Hooks allow you to execute your own code when events occur inside WHMCS.

* **[Getting Started](/hooks/getting-started/)**<br>Learn how to create a hook in WHMCS
* **[Defining Priority](/hooks/priority/)**<br>Priorities allow you to define in which order hooks execute
* **[Module Hooks](/hooks/module-hooks/)**<br>Learn how modules can take advantage of hooks
* **[Sample Hook](/hooks/sample-hook/)**<br>View sample code for creating a hook
* **[Hook Index](/hooks/hook-index/)**<br>A complete listing of all available hook points
* **[Hook Reference](/hooks-reference/)**<br>Visit the Hook Reference Manual
