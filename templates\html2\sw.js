// Service Worker for WIDDX Website Performance Optimization
const CACHE_NAME = 'widdx-v1.2';
const STATIC_CACHE = 'widdx-static-v1.2';
const DYNAMIC_CACHE = 'widdx-dynamic-v1.2';

// Assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/about.html',
    '/services.html',
    '/portfolio.html',
    '/contact.html',
    '/css/style.css',
    '/js/main.js',
    '/js/gsap-animations.js',
    '/js/portfolio.js'
];

// External resources to cache
const EXTERNAL_RESOURCES = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js'
];

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            }),
            caches.open(DYNAMIC_CACHE).then(cache => {
                console.log('Service Worker: Caching external resources');
                return cache.addAll(EXTERNAL_RESOURCES);
            })
        ]).then(() => {
            console.log('Service Worker: Installation complete');
            self.skipWaiting();
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Service Worker: Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activation complete');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') return;

    // Skip chrome-extension and other non-http(s) requests
    if (!url.protocol.startsWith('http')) return;

    event.respondWith(
        caches.match(request).then(cachedResponse => {
            if (cachedResponse) {
                // Serve from cache
                console.log('Service Worker: Serving from cache:', request.url);
                
                // Update cache in background for external resources
                if (isExternalResource(request.url)) {
                    updateCacheInBackground(request);
                }
                
                return cachedResponse;
            }

            // Network request with caching
            return fetch(request).then(response => {
                // Only cache successful responses
                if (!response || response.status !== 200 || response.type !== 'basic') {
                    return response;
                }

                // Clone response for caching
                const responseToCache = response.clone();
                
                // Determine cache type
                const cacheType = isStaticAsset(request.url) ? STATIC_CACHE : DYNAMIC_CACHE;
                
                caches.open(cacheType).then(cache => {
                    console.log('Service Worker: Caching new resource:', request.url);
                    cache.put(request, responseToCache);
                });

                return response;
            }).catch(error => {
                console.log('Service Worker: Network request failed:', error);
                
                // Return offline fallback for HTML pages
                if (request.headers.get('accept') && request.headers.get('accept').includes('text/html')) {
                    return caches.match('/index.html');
                }
                
                // For other resources, return a basic response or throw error
                return new Response('Offline', { status: 503, statusText: 'Service Unavailable' });
            });
        })
    );
});

// Helper functions
function isStaticAsset(url) {
    return STATIC_ASSETS.some(asset => url.includes(asset)) || 
           url.includes('.css') || 
           url.includes('.js') || 
           url.includes('.html');
}

function isExternalResource(url) {
    return EXTERNAL_RESOURCES.some(resource => url.includes(resource)) ||
           url.includes('fonts.googleapis.com') ||
           url.includes('cdnjs.cloudflare.com') ||
           url.includes('unpkg.com');
}

function updateCacheInBackground(request) {
    fetch(request).then(response => {
        if (response && response.status === 200) {
            caches.open(DYNAMIC_CACHE).then(cache => {
                cache.put(request, response.clone());
            });
        }
    }).catch(error => {
        console.log('Background cache update failed:', error);
    });
}

// Message handling for cache updates
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_UPDATE') {
        // Force cache update
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
        }).then(() => {
            console.log('Service Worker: Cache cleared for update');
        });
    }
});

// Background sync for offline actions
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        console.log('Service Worker: Background sync triggered');
        event.waitUntil(
            // Handle any offline actions here
            Promise.resolve()
        );
    }
});

// Push notification handling (for future use)
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey
            }
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});
