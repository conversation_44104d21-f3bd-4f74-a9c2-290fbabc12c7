<?php
// Load configuration
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/config/config.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test database connection
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ];
    
    echo "Attempting to connect to database...\n";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
    echo "✓ Successfully connected to database!\n";
    
    // Test query
    $stmt = $pdo->query("SELECT DATABASE() as db");
    $result = $stmt->fetch();
    echo "Current database: " . $result['db'] . "\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    echo "DSN: mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4\n";
    echo "User: " . DB_USER . "\n";
}
