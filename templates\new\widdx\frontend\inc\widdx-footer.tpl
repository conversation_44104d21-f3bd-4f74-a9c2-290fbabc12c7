        </div>
    </div>
    {if !$inShoppingCart && $secondarySidebar->hasChildren()}
        <div class="d-lg-none sidebar sidebar-secondary">
            {include file="$template/includes/sidebar.tpl" sidebar=$secondarySidebar}
        </div>
    {/if}
</div>
</section>

<!-- Modern WIDDX Footer -->
<footer id="footer" class="widdx-footer modern-footer" role="contentinfo">
    <!-- Newsletter Subscription Section -->
    <div class="widdx-newsletter-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="widdx-newsletter-content">
                        <h3 class="widdx-newsletter-title">Stay Updated</h3>
                        <p class="widdx-newsletter-subtitle">Get the latest news, updates, and exclusive offers delivered to your inbox.</p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <form class="widdx-newsletter-form" action="{$WEB_ROOT}/newsletter/subscribe" method="post">
                        <div class="input-group">
                            <input type="email" class="form-control widdx-newsletter-input"
                                   placeholder="Enter your email address"
                                   aria-label="Email address for newsletter"
                                   required>
                            <div class="input-group-append">
                                <button class="btn btn-primary widdx-newsletter-btn" type="submit">
                                    <i class="fas fa-paper-plane mr-2" aria-hidden="true"></i>
                                    Subscribe
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Footer Content -->
    <div class="widdx-footer-main">
        <div class="container">
            <div class="row">
                <!-- Company Information -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="widdx-footer-widget">
                        <div class="widdx-footer-brand mb-4">
                            {if $assetLogoPath}
                                <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/logo/logo-white.svg"
                                     alt="{$companyname}" class="widdx-footer-logo">
                            {else}
                                <h4 class="widdx-footer-brand-text">{$companyname}</h4>
                            {/if}
                        </div>
                        <p class="widdx-footer-description">
                            <strong>WIDDX</strong> - Your Digital Partner. We provide comprehensive digital solutions including web hosting, domain registration, website development, and digital marketing services to help your business thrive online.
                        </p>

                        <!-- Contact Information -->
                        <div class="widdx-contact-info mt-4">
                            <div class="widdx-contact-item">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                <span>Jenin, Palestine</span>
                            </div>
                            <div class="widdx-contact-item">
                                <i class="fas fa-envelope" aria-hidden="true"></i>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div class="widdx-contact-item">
                                <i class="fas fa-phone" aria-hidden="true"></i>
                                <a href="tel:+************">+970 123 456 789</a>
                            </div>
                        </div>

                        <!-- Social Media Links -->
                        <div class="widdx-social-links mt-4">
                            <h6 class="widdx-footer-subtitle">Follow Us</h6>
                            <div class="widdx-social-icons">
                                {include file="$template/includes/social-accounts.tpl"}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services & Products -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="widdx-footer-widget">
                        <h6 class="widdx-footer-title">Our Services</h6>
                        <ul class="widdx-footer-menu">
                            <li><a href="{$WEB_ROOT}/store/web-hosting">Web Hosting</a></li>
                            <li><a href="{$WEB_ROOT}/store/reseller-hosting">Reseller Hosting</a></li>
                            <li><a href="{$WEB_ROOT}/store/business-hosting">Business Hosting</a></li>
                            <li><a href="{$WEB_ROOT}/store/vps-hosting">VPS Hosting</a></li>
                            <li><a href="{$WEB_ROOT}/store/dedicated-servers">Dedicated Servers</a></li>
                            <li><a href="{$WEB_ROOT}/store/ssl-certificates">SSL Certificates</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Tools & Resources -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="widdx-footer-widget">
                        <h6 class="widdx-footer-title">Tools & Resources</h6>
                        <ul class="widdx-footer-menu">
                            <li><a href="{$WEB_ROOT}/page/whois-checker">Whois Checker</a></li>
                            <li><a href="{$WEB_ROOT}/page/seo-analyzer">SEO Analyzer</a></li>
                            <li><a href="{$WEB_ROOT}/page/sitemap-generator">Sitemap Generator</a></li>
                            <li><a href="{$WEB_ROOT}/page/dns-checker">DNS Checker</a></li>
                            <li><a href="{$WEB_ROOT}/knowledgebase">Knowledge Base</a></li>
                            <li><a href="{$WEB_ROOT}/downloads">Downloads</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Support & Help -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="widdx-footer-widget">
                        <h6 class="widdx-footer-title">Support & Help</h6>
                        <ul class="widdx-footer-menu">
                            <li><a href="{$WEB_ROOT}/contact.php">{lang key='contactus'}</a></li>
                            <li><a href="{$WEB_ROOT}/submitticket.php">Support Tickets</a></li>
                            <li><a href="{$WEB_ROOT}/serverstatus.php">Server Status</a></li>
                            <li><a href="{$WEB_ROOT}/page/affiliate-program">Affiliate Program</a></li>
                            <li><a href="{$WEB_ROOT}/announcements">Announcements</a></li>
                            <li><a href="{$WEB_ROOT}/page/network-status">Network Status</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Legal & Policies -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="widdx-footer-widget">
                        <h6 class="widdx-footer-title">Legal & Policies</h6>
                        <ul class="widdx-footer-menu">
                            {if $acceptTOS}
                                <li><a href="{$tosURL}" target="_blank">{lang key='ordertos'}</a></li>
                            {/if}
                            <li><a href="{$WEB_ROOT}/page/privacy-policy">Privacy Policy</a></li>
                            <li><a href="{$WEB_ROOT}/page/hosting-terms-of-service-tos">Hosting Terms</a></li>
                            <li><a href="{$WEB_ROOT}/page/reseller-hosting-terms-of-service-tos">Reseller Terms</a></li>
                            <li><a href="{$WEB_ROOT}/page/return-policy">Refund Policy</a></li>
                            <li><a href="{$WEB_ROOT}/page/dispute-resolution">Dispute Resolution</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Payment Methods & Trust Badges -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="widdx-footer-widget">
                        <h6 class="widdx-footer-title">We Accept</h6>
                        <div class="widdx-payment-methods">
                            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/payments/visa.svg"
                                 alt="Visa" class="widdx-payment-icon">
                            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/payments/mastercard.svg"
                                 alt="Mastercard" class="widdx-payment-icon">
                            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/payments/paypal.svg"
                                 alt="PayPal" class="widdx-payment-icon">
                            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/payments/lahza.svg"
                                 alt="Lahza" class="widdx-payment-icon">
                        </div>

                        <!-- Trust Badges -->
                        <div class="widdx-trust-badges mt-3">
                            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/badges/ssl-secured.svg"
                                 alt="SSL Secured" class="widdx-trust-badge">
                            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/badges/uptime-guarantee.svg"
                                 alt="99.9% Uptime" class="widdx-trust-badge">
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Footer Bottom -->
    <div class="widdx-footer-bottom">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="widdx-copyright">
                        <p class="mb-0">
                            {lang key="copyrightFooterNotice" year=$date_year company=$companyname}
                        </p>
                        <p class="widdx-powered-by mb-0">
                            Powered by <strong>WIDDX</strong> - Professional Hosting Solutions
                        </p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="widdx-footer-actions">
                        {if $languagechangeenabled && count($locales) > 1 || $currencies}
                            <button type="button" class="btn btn-outline-light btn-sm widdx-lang-currency-btn"
                                    data-toggle="modal" data-target="#modalChooseLanguage"
                                    aria-label="Change language and currency">
                                <div class="d-inline-block align-middle mr-1">
                                    <div class="iti-flag {if $activeLocale.countryCode === 'GB'}us{else}{$activeLocale.countryCode|lower}{/if}"></div>
                                </div>
                                <span>{$activeLocale.localisedName} / {$activeCurrency.prefix}{$activeCurrency.code}</span>
                            </button>
                        {/if}

                        <!-- Back to Top Button -->
                        <button class="widdx-back-to-top btn btn-primary btn-sm ml-2"
                                aria-label="Back to top"
                                title="Back to top">
                            <i class="fas fa-chevron-up" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Quick Contact Widget -->
<div class="widdx-quick-contact" id="widdxQuickContact">
    <div class="widdx-quick-contact-toggle" aria-label="Quick contact">
        <i class="fas fa-comments" aria-hidden="true"></i>
    </div>
    <div class="widdx-quick-contact-panel">
        <div class="widdx-quick-contact-header">
            <h6>Need Help?</h6>
            <button class="widdx-quick-contact-close" aria-label="Close">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
        </div>
        <div class="widdx-quick-contact-content">
            <div class="widdx-quick-contact-item">
                <a href="{$WEB_ROOT}/submitticket.php" class="btn btn-primary btn-sm btn-block">
                    <i class="fas fa-ticket-alt mr-2" aria-hidden="true"></i>
                    Open Support Ticket
                </a>
            </div>
            <div class="widdx-quick-contact-item">
                <a href="{$WEB_ROOT}/contact.php" class="btn btn-outline-primary btn-sm btn-block">
                    <i class="fas fa-envelope mr-2" aria-hidden="true"></i>
                    Contact Us
                </a>
            </div>
            <div class="widdx-quick-contact-item">
                <a href="tel:+************" class="btn btn-outline-success btn-sm btn-block">
                    <i class="fas fa-phone mr-2" aria-hidden="true"></i>
                    Call Now
                </a>
            </div>
        </div>
    </div>
</div>

<!-- PWA Install Prompt -->
<div class="pwa-install-prompt {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}rtl{/if}"
    id="pwaInstallPrompt">
    <div class="pwa-install-content">
        <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/192.png" alt="App Icon"
            class="pwa-install-icon">
        <div class="pwa-install-text">
            <h4>{$LANG.pwa_install_title|default:"Install Our App"}</h4>
            <p>{$LANG.pwa_install_message|default:"Install our app now for a better experience and offline access. Enjoy a fast and seamless experience! ðŸŒŸ"}
            </p>
        </div>
    </div>
    <div class="pwa-install-actions">
        <button class="pwa-install-button pwa-install-now" id="pwaInstallButton">
            {$LANG.pwa_install_now|default:"Install Now ðŸš€"}
        </button>
        <button class="pwa-install-button pwa-install-later" id="pwaInstallLater">
            {$LANG.pwa_install_later|default:"Maybe Later ðŸ•’"}
        </button>
    </div>
</div>

<!-- Language and Currency Modal -->
<form method="get" action="{$currentpagelinkback}">
    <div class="modal fade modal-localisation" id="modalChooseLanguage" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <!-- Ø²Ø± Ø§Ù„Ø¥ØºÙ„Ø§Ù‚ -->
                    <button type="button" class="btn-close" data-bs-dismiss="modal" data-dismiss="modal"
                        aria-label="Close"></button>

                    {if $languagechangeenabled && count($locales) > 1}
                        <h5 class="h5 pt-5 pb-3">{lang key='chooselanguage'}</h5>
                        <div class="row item-selector">
                            <input type="hidden" name="language" data-current="{$language}" value="{$language}" />
                            {foreach $locales as $locale}
                                <div class="col-4">
                                    <a href="#" class="item{if $language == $locale.language} active{/if}"
                                        data-value="{$locale.language}">
                                        {$locale.localisedName}
                                    </a>
                                </div>
                            {/foreach}
                        </div>
                    {/if}
                    {if !$loggedin && $currencies}
                        <p class="h5 pt-5 pb-3">{lang key='choosecurrency'}</p>
                        <div class="row item-selector">
                            <input type="hidden" name="currency" data-current="{$activeCurrency.id}" value="">
                            {foreach $currencies as $selectCurrency}
                                <div class="col-4">
                                    <a href="#" class="item{if $activeCurrency.id == $selectCurrency.id} active{/if}"
                                        data-value="{$selectCurrency.id}">
                                        {$selectCurrency.prefix} {$selectCurrency.code}
                                    </a>
                                </div>
                            {/foreach}
                        </div>
                    {/if}
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-secondary">{lang key='apply'}</button>
                </div>
            </div>
        </div>
    </div>
</form>

{if !$loggedin && $adminLoggedIn}
    <a href="{$WEB_ROOT}/logout.php?returntoadmin=1" class="btn btn-return-to-admin" data-toggle="tooltip" data-placement="bottom" title="{if $adminMasqueradingAsClient}{lang key='adminmasqueradingasclient'} {lang key='logoutandreturntoadminarea'}{else}{lang key='adminloggedin'} {lang key='returntoadminarea'}{/if}">
        <i class="fas fa-redo-alt"></i>
        <span>{lang key="admin.returnToAdmin"}</span>
    </a>
{/if}
<!-- Core Scripts -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/jquery-3.6.0.min.js"></script>
<script>
    jQuery.noConflict(true);
</script>
<script src="{assetPath file='swiper-bundle.min.js'}?v={$versionHash}"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/widdx-script.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/bootstrap.bundle.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/bootstrap-slider.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/owl.carousel.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/magnific-popup.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/validator.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/hs.megamenu.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/app.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/progressbar.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/pwa-install.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/sidebar.js"></script>

{if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
<!-- RTL Support -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/rtl-support.js"></script>
{/if}

<!-- Performance Optimizer (Load early for better performance) -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/performance-optimizer.js"></script>

<!-- Domain Checker Fix -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/domain-checker-fix.js"></script>

<!-- Domain Submission Fix (Enhanced) -->
<script src="{$WEB_ROOT}/templates/{$template}/domain-submission-fix.js"></script>

<!-- Error Handling -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/profile-image-fix.js"></script>

<!-- Modern Header & Footer JavaScript -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/widdx-modern-header-footer.js"></script>

<!-- Owl Carousel Initialization -->
<script>
    $(".owl-carousel").owlCarousel({
        loop: true,
        margin: 10,
        nav: true,
        dots: true,
        autoplay: true,
        autoplayTimeout: 5000,
        autoplayHoverPause: true,
        animateOut: 'fadeOut',
        animateIn: 'fadeIn',
        responsive: {
            0: { items: 1 },
            400: { items: 2 },
            800: { items: 3 },
            1000: { items: 4 }
        }
    });
</script>

{$footeroutput}
</body>

</html>