<?php
// Production Configuration Overrides
if (!defined('SECURE_PRODUCTION_CONFIG_LOADED')) {
    define('SECURE_PRODUCTION_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// Environment Settings
const APP_ENV = Env::get('APP_ENV', 'production');
const APP_DEBUG = Env::get('APP_DEBUG', false);

// Base URL
const BASE_URL = Env::get('BASE_URL', 'https://widdx.ai');

// API and Cache Settings
const API_TIMEOUT = Env::get('API_TIMEOUT', 15);
const CACHE_TTL = Env::get('CACHE_TTL', 604800);

// Logging Settings
const LOG_FILE = Env::get('LOG_FILE', __DIR__ . '/../logs/production.log');
const ERROR_LOG_FILE = Env::get('ERROR_LOG_FILE', __DIR__ . '/../logs/production_error.log');

// Rate Limiting
const MAX_REQUESTS_PER_MINUTE = Env::get('MAX_REQUESTS_PER_MINUTE', 120);
const RATE_LIMIT_WINDOW = Env::get('RATE_LIMIT_WINDOW', 30);

// Session Settings
const SESSION_LIFETIME = Env::get('SESSION_LIFETIME', 86400);

// Cache Settings
const CACHE_ENABLED = Env::get('CACHE_ENABLED', true);
const CACHE_PROVIDER = Env::get('CACHE_PROVIDER', 'redis');

// Language Settings
const DEFAULT_LANGUAGE = Env::get('DEFAULT_LANGUAGE', 'ar');

// Logging and Memory Settings
const MAX_LOG_SIZE = Env::get('MAX_LOG_SIZE', 52428800);
const ERROR_REPORTING_LEVEL = Env::get('ERROR_REPORTING_LEVEL', E_ALL & ~E_DEPRECATED & ~E_STRICT);
const MEMORY_LIMIT = Env::get('MEMORY_LIMIT', '512M');

// Redis Configuration
if (!defined('REDIS_HOST')) {
    const REDIS_HOST = Env::get('REDIS_HOST', 'redis');
    const REDIS_PORT = Env::get('REDIS_PORT', 6379);
    const REDIS_PASSWORD = Env::get('REDIS_PASSWORD', '');
}

// Database Cache Configuration
if (!defined('DB_CACHE_ENABLED')) {
    const DB_CACHE_ENABLED = Env::get('DB_CACHE_ENABLED', true);
    const DB_CACHE_TTL = Env::get('DB_CACHE_TTL', 3600);
}

// CDN Configuration
if (!defined('CDN_ENABLED')) {
    const CDN_ENABLED = Env::get('CDN_ENABLED', true);
    const CDN_URL = Env::get('CDN_URL', 'https://cdn.widdx.ai');
}

// SSL Configuration
const SSL_ENABLED = Env::get('SSL_ENABLED', true);
const SSL_REDIRECT = Env::get('SSL_REDIRECT', true);

// Security and Validation
if (APP_ENV === 'production') {
    // Validate base URL
    if (!filter_var(BASE_URL, FILTER_VALIDATE_URL)) {
        trigger_error('Invalid base URL configuration', E_USER_ERROR);
    }
    
    // Validate rate limiting
    if (MAX_REQUESTS_PER_MINUTE < 1 || RATE_LIMIT_WINDOW < 1) {
        trigger_error('Invalid rate limiting configuration', E_USER_ERROR);
    }
    
    // Validate session lifetime
    if (SESSION_LIFETIME < 3600) {
        trigger_error('Session lifetime is too short for production', E_USER_ERROR);
    }
    
    // Validate cache settings
    if (CACHE_TTL < 3600) {
        trigger_error('Cache TTL is too short for production', E_USER_ERROR);
    }
    
    // Validate log file sizes
    if (MAX_LOG_SIZE < 10485760) { // 10MB minimum
        trigger_error('Log file size limit is too small for production', E_USER_ERROR);
    }
    
    // Validate memory limit
    if (MEMORY_LIMIT < '128M') {
        trigger_error('Memory limit is too low for production', E_USER_ERROR);
    }
    
    // Create log directories with proper permissions
    $logDir = dirname(LOG_FILE);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    chmod($logDir, 0755);
    
    // Validate Redis configuration if enabled
    if (CACHE_PROVIDER === 'redis') {
        if (empty(REDIS_HOST) || REDIS_PORT < 1) {
            trigger_error('Invalid Redis configuration', E_USER_ERROR);
        }
    }
    
    // Validate CDN configuration if enabled
    if (CDN_ENABLED && !filter_var(CDN_URL, FILTER_VALIDATE_URL)) {
        trigger_error('Invalid CDN URL configuration', E_USER_ERROR);
    }
}
if (!defined('FORCE_SSL')) {
    define('FORCE_SSL', true);
}
