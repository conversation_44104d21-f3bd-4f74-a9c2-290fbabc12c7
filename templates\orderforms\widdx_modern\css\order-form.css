/**
 * WIDDX Modern Order Form Styles
 * Professional order form with enhanced UX
 * 
 * @package    WIDDX Order Form
 * <AUTHOR> Development Team
 * @version    1.0.0
 */

/* CSS Variables */
:root {
  --widdx-primary: #2c5aa0;
  --widdx-secondary: #1e3d6f;
  --widdx-accent: #4a90e2;
  --widdx-success: #27ae60;
  --widdx-warning: #f39c12;
  --widdx-danger: #e74c3c;
  --widdx-info: #3498db;
  --widdx-light: #f8f9fa;
  --widdx-dark: #2c3e50;
  
  --widdx-gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
  --widdx-gradient-secondary: linear-gradient(135deg, #1e3d6f 0%, #2c5aa0 100%);
  
  --widdx-border-radius: 8px;
  --widdx-border-radius-lg: 12px;
  --widdx-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --widdx-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
  
  --widdx-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --widdx-font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Base Styles */
.widdx-order-form-body {
  font-family: var(--widdx-font-primary);
  line-height: 1.6;
  color: #2c3e50;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Header Styles */
.widdx-order-header {
  background: var(--widdx-gradient-primary);
  color: white;
  padding: 1rem 0;
  box-shadow: var(--widdx-shadow);
}

.widdx-logo {
  max-height: 50px;
  width: auto;
}

.widdx-company-name {
  font-family: var(--widdx-font-secondary);
  font-weight: 700;
  margin: 0;
  color: white;
}

.widdx-header-actions .btn {
  margin-left: 0.5rem;
}

.widdx-welcome {
  margin-right: 1rem;
  font-weight: 500;
}

/* Progress Indicator */
.widdx-progress-container {
  background: white;
  border-radius: var(--widdx-border-radius-lg);
  padding: 2rem;
  box-shadow: var(--widdx-shadow);
  margin-bottom: 2rem;
}

.widdx-progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  position: relative;
}

.widdx-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.widdx-step-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  color: #6c757d;
  font-size: 1.2rem;
}

.widdx-step.active .widdx-step-icon {
  background: var(--widdx-gradient-primary);
  color: white;
  transform: scale(1.1);
}

.widdx-step-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6c757d;
  text-align: center;
}

.widdx-step.active .widdx-step-label {
  color: var(--widdx-primary);
  font-weight: 600;
}

.widdx-progress-bar {
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.widdx-progress-fill {
  height: 100%;
  background: var(--widdx-gradient-primary);
  border-radius: 2px;
  transition: width 0.6s ease;
}

/* Hero Section */
.widdx-hero-section {
  background: white;
  border-radius: var(--widdx-border-radius-lg);
  padding: 3rem 2rem;
  box-shadow: var(--widdx-shadow);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.widdx-hero-content {
  flex: 1;
}

.widdx-hero-title {
  font-family: var(--widdx-font-secondary);
  font-weight: 700;
  font-size: 2.5rem;
  color: var(--widdx-primary);
  margin-bottom: 1rem;
}

.widdx-hero-subtitle {
  font-size: 1.2rem;
  color: #6c757d;
  margin-bottom: 0;
}

.widdx-hero-graphic {
  color: var(--widdx-accent);
  opacity: 0.7;
  margin-left: 2rem;
}

/* Sidebar */
.widdx-sidebar {
  background: white;
  border-radius: var(--widdx-border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--widdx-shadow);
  position: sticky;
  top: 2rem;
}

/* Product Groups */
.widdx-product-group {
  background: white;
  border-radius: var(--widdx-border-radius-lg);
  padding: 2rem;
  box-shadow: var(--widdx-shadow);
}

.widdx-group-header {
  text-align: center;
  margin-bottom: 2rem;
}

.widdx-group-title {
  font-family: var(--widdx-font-secondary);
  font-weight: 700;
  color: var(--widdx-primary);
  margin-bottom: 0.5rem;
}

.widdx-group-tagline {
  color: #6c757d;
  font-size: 1.1rem;
}

/* Products Grid */
.widdx-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

/* Product Cards */
.widdx-product-card {
  background: white;
  border: 2px solid #f8f9fa;
  border-radius: var(--widdx-border-radius-lg);
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.widdx-product-card:hover {
  border-color: var(--widdx-primary);
  transform: translateY(-4px);
  box-shadow: var(--widdx-shadow-lg);
}

.widdx-product-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--widdx-gradient-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.widdx-product-name {
  font-family: var(--widdx-font-secondary);
  font-weight: 600;
  color: var(--widdx-primary);
  margin-bottom: 0.5rem;
}

.widdx-product-tagline {
  color: #6c757d;
  margin-bottom: 1.5rem;
}

/* Pricing */
.widdx-price-container {
  text-align: center;
  margin-bottom: 1rem;
}

.widdx-price-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--widdx-primary);
}

.widdx-price-cycle {
  font-size: 1rem;
  color: #6c757d;
  font-weight: 500;
}

.widdx-setup-fee {
  text-align: center;
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

/* Features */
.widdx-feature-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.widdx-feature-item {
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
}

.widdx-feature-item i {
  margin-right: 0.75rem;
  width: 16px;
}

/* Buttons */
.btn-widdx-primary {
  background: var(--widdx-gradient-primary);
  border: none;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: var(--widdx-border-radius);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-widdx-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--widdx-shadow-lg);
  color: white;
}

/* Domain Section */
.widdx-domain-section {
  background: white;
  border-radius: var(--widdx-border-radius-lg);
  padding: 2rem;
  box-shadow: var(--widdx-shadow);
}

.widdx-section-header {
  margin-bottom: 2rem;
}

.widdx-section-title {
  font-family: var(--widdx-font-secondary);
  font-weight: 700;
  color: var(--widdx-primary);
}

.widdx-section-subtitle {
  color: #6c757d;
  font-size: 1.1rem;
}

.widdx-domain-card {
  background: #f8f9fa;
  border-radius: var(--widdx-border-radius-lg);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.widdx-domain-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--widdx-shadow);
}

.widdx-domain-icon {
  color: var(--widdx-accent);
  margin-bottom: 1rem;
}

/* Trust Indicators */
.widdx-trust-section {
  background: white;
  border-radius: var(--widdx-border-radius-lg);
  padding: 2rem;
  box-shadow: var(--widdx-shadow);
}

.widdx-trust-indicators {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1rem;
}

.widdx-trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: var(--widdx-primary);
  font-weight: 500;
}

.widdx-trust-item i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--widdx-accent);
}

/* Footer */
.widdx-order-footer {
  background: var(--widdx-secondary);
  color: white;
  padding: 2rem 0;
  margin-top: 4rem;
}

.widdx-footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  margin-left: 1rem;
  transition: color 0.3s ease;
}

.widdx-footer-links a:hover {
  color: white;
  text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .widdx-hero-section {
    flex-direction: column;
    text-align: center;
  }
  
  .widdx-hero-graphic {
    margin-left: 0;
    margin-top: 1rem;
  }
  
  .widdx-hero-title {
    font-size: 2rem;
  }
  
  .widdx-products-grid {
    grid-template-columns: 1fr;
  }
  
  .widdx-trust-indicators {
    flex-direction: column;
  }
  
  .widdx-progress-steps {
    flex-wrap: wrap;
  }
  
  .widdx-step {
    margin-bottom: 1rem;
  }
}

/* Loading States */
.widdx-loading {
  position: relative;
  overflow: hidden;
}

.widdx-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.widdx-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--widdx-primary);
  border-radius: 50%;
  animation: widdxSpin 1s linear infinite;
  z-index: 11;
}

@keyframes widdxSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Validation States */
.widdx-form-group.has-error .form-control {
  border-color: var(--widdx-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.widdx-form-group.has-success .form-control {
  border-color: var(--widdx-success);
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.widdx-error-message {
  color: var(--widdx-danger);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.widdx-success-message {
  color: var(--widdx-success);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}
