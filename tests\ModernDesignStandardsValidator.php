<?php

/**
 * Modern Design Standards Validator for WIDDX
 * 
 * Validates compliance with modern web design standards including:
 * - CSS architecture and modern features
 * - Responsive design patterns
 * - Accessibility compliance
 * - Performance optimizations
 * - Progressive enhancement
 * 
 * @package WIDDX
 * @subpackage Tests
 * @version 1.0.0
 */

class ModernDesignStandardsValidator {
    
    private $results = [];
    private $errors = [];
    private $warnings = [];
    private $templatePath;
    
    public function __construct($templatePath = null) {
        $this->templatePath = $templatePath ?: __DIR__ . '/../templates/WIDDX';
    }
    
    /**
     * Run comprehensive modern design standards validation
     */
    public function validate() {
        echo "🎨 Starting Modern Design Standards Validation...\n\n";
        
        $this->validateCSSArchitecture();
        $this->validateResponsiveDesign();
        $this->validateAccessibility();
        $this->validatePerformance();
        $this->validateModernFeatures();
        $this->validateProgressiveEnhancement();
        
        $this->generateReport();
        
        return [
            'passed' => empty($this->errors),
            'score' => $this->calculateScore(),
            'results' => $this->results,
            'errors' => $this->errors,
            'warnings' => $this->warnings
        ];
    }
    
    /**
     * Validate CSS architecture and modern features
     */
    private function validateCSSArchitecture() {
        echo "📐 Validating CSS Architecture...\n";
        
        $cssFiles = [
            'theme.css',
            'modern-standards.css',
            'widdx-unified.min.css'
        ];
        
        foreach ($cssFiles as $file) {
            $filePath = $this->templatePath . '/css/' . $file;
            if (!file_exists($filePath)) {
                $this->errors[] = "CSS file missing: {$file}";
                continue;
            }
            
            $content = file_get_contents($filePath);
            
            // Check for CSS custom properties
            if (strpos($content, '--widdx-') !== false) {
                $this->results['css_custom_properties'] = '✅ CSS Custom Properties implemented';
            } else {
                $this->warnings[] = "CSS Custom Properties not found in {$file}";
            }
            
            // Check for CSS Grid
            if (strpos($content, 'display: grid') !== false || strpos($content, 'grid-template') !== false) {
                $this->results['css_grid'] = '✅ CSS Grid implemented';
            }
            
            // Check for Flexbox
            if (strpos($content, 'display: flex') !== false || strpos($content, 'flex-direction') !== false) {
                $this->results['flexbox'] = '✅ Flexbox implemented';
            }
            
            // Check for modern CSS features
            $modernFeatures = [
                'clamp(' => 'Fluid Typography',
                'min(' => 'CSS Math Functions',
                'max(' => 'CSS Math Functions',
                'aspect-ratio' => 'Aspect Ratio',
                'container-type' => 'Container Queries',
                'backdrop-filter' => 'Backdrop Filter',
                '@supports' => 'Feature Queries',
                '@media (prefers-' => 'User Preferences'
            ];
            
            foreach ($modernFeatures as $feature => $name) {
                if (strpos($content, $feature) !== false) {
                    $this->results["modern_css_{$name}"] = "✅ {$name} implemented";
                }
            }
        }
        
        echo "   CSS Architecture validation complete\n\n";
    }
    
    /**
     * Validate responsive design implementation
     */
    private function validateResponsiveDesign() {
        echo "📱 Validating Responsive Design...\n";
        
        $cssContent = $this->getCombinedCSS();
        
        // Check for mobile-first approach
        $mobileFirstPattern = '/@media\s*\(\s*min-width/i';
        if (preg_match($mobileFirstPattern, $cssContent)) {
            $this->results['mobile_first'] = '✅ Mobile-first approach detected';
        } else {
            $this->warnings[] = 'Mobile-first media queries not clearly detected';
        }
        
        // Check for responsive breakpoints
        $breakpoints = [
            '768px' => 'Tablet breakpoint',
            '1024px' => 'Desktop breakpoint',
            '1200px' => 'Large desktop breakpoint'
        ];
        
        foreach ($breakpoints as $breakpoint => $name) {
            if (strpos($cssContent, $breakpoint) !== false) {
                $this->results["breakpoint_{$breakpoint}"] = "✅ {$name} implemented";
            }
        }
        
        // Check for touch-friendly design
        if (strpos($cssContent, 'min-height: 44px') !== false || 
            strpos($cssContent, 'min-width: 44px') !== false) {
            $this->results['touch_friendly'] = '✅ Touch-friendly design (44px targets)';
        }
        
        // Check for responsive images
        $templateFiles = glob($this->templatePath . '/*.tpl');
        $hasResponsiveImages = false;
        
        foreach ($templateFiles as $file) {
            $content = file_get_contents($file);
            if (strpos($content, 'srcset') !== false || strpos($content, 'sizes') !== false) {
                $hasResponsiveImages = true;
                break;
            }
        }
        
        if ($hasResponsiveImages) {
            $this->results['responsive_images'] = '✅ Responsive images implemented';
        }
        
        echo "   Responsive design validation complete\n\n";
    }
    
    /**
     * Validate accessibility compliance
     */
    private function validateAccessibility() {
        echo "♿ Validating Accessibility...\n";
        
        $cssContent = $this->getCombinedCSS();
        $jsContent = $this->getCombinedJS();
        
        // Check for focus indicators
        if (strpos($cssContent, ':focus') !== false) {
            $this->results['focus_indicators'] = '✅ Focus indicators implemented';
        } else {
            $this->errors[] = 'Focus indicators not found';
        }
        
        // Check for reduced motion support
        if (strpos($cssContent, 'prefers-reduced-motion') !== false) {
            $this->results['reduced_motion'] = '✅ Reduced motion support';
        } else {
            $this->warnings[] = 'Reduced motion preferences not detected';
        }
        
        // Check for high contrast support
        if (strpos($cssContent, 'prefers-contrast') !== false) {
            $this->results['high_contrast'] = '✅ High contrast mode support';
        }
        
        // Check for ARIA implementation in JavaScript
        $ariaFeatures = [
            'setAttribute(\'aria-' => 'ARIA attributes',
            'getAttribute(\'aria-' => 'ARIA attribute reading',
            'role=' => 'ARIA roles',
            'aria-live' => 'Live regions'
        ];
        
        foreach ($ariaFeatures as $feature => $name) {
            if (strpos($jsContent, $feature) !== false) {
                $this->results["aria_{$name}"] = "✅ {$name} implemented";
            }
        }
        
        // Check for keyboard navigation
        if (strpos($jsContent, 'keydown') !== false || strpos($jsContent, 'keyup') !== false) {
            $this->results['keyboard_navigation'] = '✅ Keyboard navigation support';
        }
        
        echo "   Accessibility validation complete\n\n";
    }
    
    /**
     * Validate performance optimizations
     */
    private function validatePerformance() {
        echo "⚡ Validating Performance...\n";
        
        $jsContent = $this->getCombinedJS();
        $cssContent = $this->getCombinedCSS();
        
        // Check for lazy loading
        if (strpos($jsContent, 'IntersectionObserver') !== false && 
            strpos($jsContent, 'data-src') !== false) {
            $this->results['lazy_loading'] = '✅ Lazy loading implemented';
        }
        
        // Check for requestIdleCallback usage
        if (strpos($jsContent, 'requestIdleCallback') !== false) {
            $this->results['idle_callbacks'] = '✅ Idle callbacks for non-critical tasks';
        }
        
        // Check for debouncing
        if (strpos($jsContent, 'debounce') !== false) {
            $this->results['debouncing'] = '✅ Event debouncing implemented';
        }
        
        // Check for CSS optimizations
        if (strpos($cssContent, 'will-change') !== false) {
            $this->results['css_optimizations'] = '✅ CSS performance hints';
        }
        
        // Check for resource hints in templates
        $templateFiles = glob($this->templatePath . '/*.tpl');
        $hasResourceHints = false;
        
        foreach ($templateFiles as $file) {
            $content = file_get_contents($file);
            if (strpos($content, 'rel="preload"') !== false || 
                strpos($content, 'rel="prefetch"') !== false) {
                $hasResourceHints = true;
                break;
            }
        }
        
        if ($hasResourceHints) {
            $this->results['resource_hints'] = '✅ Resource hints implemented';
        }
        
        echo "   Performance validation complete\n\n";
    }
    
    /**
     * Validate modern web features
     */
    private function validateModernFeatures() {
        echo "🚀 Validating Modern Web Features...\n";
        
        $jsContent = $this->getCombinedJS();
        
        // Check for modern JavaScript features
        $modernJSFeatures = [
            'class ' => 'ES6 Classes',
            'const ' => 'ES6 Constants',
            'let ' => 'ES6 Let',
            '=>' => 'Arrow Functions',
            'async ' => 'Async/Await',
            'Promise' => 'Promises',
            'Map(' => 'ES6 Map',
            'Set(' => 'ES6 Set'
        ];
        
        foreach ($modernJSFeatures as $feature => $name) {
            if (strpos($jsContent, $feature) !== false) {
                $this->results["modern_js_{$name}"] = "✅ {$name} used";
            }
        }
        
        // Check for modern Web APIs
        $webAPIs = [
            'IntersectionObserver' => 'Intersection Observer',
            'ResizeObserver' => 'Resize Observer',
            'PerformanceObserver' => 'Performance Observer',
            'MutationObserver' => 'Mutation Observer',
            'requestAnimationFrame' => 'Animation Frame',
            'navigator.share' => 'Web Share API',
            'navigator.clipboard' => 'Clipboard API'
        ];
        
        foreach ($webAPIs as $api => $name) {
            if (strpos($jsContent, $api) !== false) {
                $this->results["web_api_{$name}"] = "✅ {$name} implemented";
            }
        }
        
        echo "   Modern features validation complete\n\n";
    }
    
    /**
     * Validate progressive enhancement
     */
    private function validateProgressiveEnhancement() {
        echo "📈 Validating Progressive Enhancement...\n";
        
        $jsContent = $this->getCombinedJS();
        
        // Check for feature detection
        if (strpos($jsContent, 'CSS.supports') !== false || 
            strpos($jsContent, 'in window') !== false) {
            $this->results['feature_detection'] = '✅ Feature detection implemented';
        } else {
            $this->warnings[] = 'Feature detection not clearly implemented';
        }
        
        // Check for graceful degradation
        if (strpos($jsContent, '@supports') !== false) {
            $this->results['graceful_degradation'] = '✅ CSS feature queries for graceful degradation';
        }
        
        // Check for polyfill usage
        if (strpos($jsContent, 'polyfill') !== false) {
            $this->results['polyfills'] = '✅ Polyfill usage detected';
        }
        
        echo "   Progressive enhancement validation complete\n\n";
    }
    
    /**
     * Get combined CSS content
     */
    private function getCombinedCSS() {
        $cssFiles = glob($this->templatePath . '/css/*.css');
        $content = '';
        
        foreach ($cssFiles as $file) {
            $content .= file_get_contents($file) . "\n";
        }
        
        return $content;
    }
    
    /**
     * Get combined JavaScript content
     */
    private function getCombinedJS() {
        $jsFiles = glob($this->templatePath . '/js/*.js');
        $content = '';
        
        foreach ($jsFiles as $file) {
            $content .= file_get_contents($file) . "\n";
        }
        
        return $content;
    }
    
    /**
     * Calculate compliance score
     */
    private function calculateScore() {
        $totalChecks = count($this->results) + count($this->errors) + count($this->warnings);
        $passedChecks = count($this->results);
        $warningPenalty = count($this->warnings) * 0.5;
        $errorPenalty = count($this->errors) * 1;
        
        if ($totalChecks === 0) return 100;
        
        $score = (($passedChecks - $warningPenalty - $errorPenalty) / $totalChecks) * 100;
        return max(0, min(100, round($score, 2)));
    }
    
    /**
     * Generate validation report
     */
    private function generateReport() {
        $score = $this->calculateScore();
        
        echo "📊 MODERN DESIGN STANDARDS VALIDATION REPORT\n";
        echo str_repeat("=", 50) . "\n\n";
        
        echo "🎯 Overall Score: {$score}/100\n\n";
        
        if (!empty($this->results)) {
            echo "✅ PASSED CHECKS:\n";
            foreach ($this->results as $check => $result) {
                echo "   {$result}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->warnings)) {
            echo "⚠️  WARNINGS:\n";
            foreach ($this->warnings as $warning) {
                echo "   ⚠️  {$warning}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "❌ ERRORS:\n";
            foreach ($this->errors as $error) {
                echo "   ❌ {$error}\n";
            }
            echo "\n";
        }
        
        // Compliance assessment
        if ($score >= 95) {
            echo "🏆 EXCELLENT: Exceeds modern design standards\n";
        } elseif ($score >= 85) {
            echo "✅ GOOD: Meets modern design standards\n";
        } elseif ($score >= 70) {
            echo "⚠️  FAIR: Partially meets modern design standards\n";
        } else {
            echo "❌ POOR: Does not meet modern design standards\n";
        }
        
        echo "\n📈 SUMMARY:\n";
        echo "   Passed: " . count($this->results) . "\n";
        echo "   Warnings: " . count($this->warnings) . "\n";
        echo "   Errors: " . count($this->errors) . "\n";
        echo "   Total Score: {$score}/100\n\n";
        
        // Save detailed report
        $this->saveDetailedReport($score);
    }
    
    /**
     * Save detailed validation report
     */
    private function saveDetailedReport($score) {
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'score' => $score,
            'status' => $score >= 85 ? 'COMPLIANT' : 'NON_COMPLIANT',
            'results' => $this->results,
            'warnings' => $this->warnings,
            'errors' => $this->errors,
            'recommendations' => $this->generateRecommendations()
        ];
        
        $reportPath = __DIR__ . '/reports/modern-design-standards-' . date('Y-m-d-H-i-s') . '.json';
        
        if (!is_dir(dirname($reportPath))) {
            mkdir(dirname($reportPath), 0755, true);
        }
        
        file_put_contents($reportPath, json_encode($report, JSON_PRETTY_PRINT));
        
        echo "📄 Detailed report saved: {$reportPath}\n";
    }
    
    /**
     * Generate recommendations based on validation results
     */
    private function generateRecommendations() {
        $recommendations = [];
        
        if (count($this->errors) > 0) {
            $recommendations[] = "Address critical errors to ensure basic compliance";
        }
        
        if (count($this->warnings) > 3) {
            $recommendations[] = "Review warnings to improve overall compliance score";
        }
        
        if (!isset($this->results['css_custom_properties'])) {
            $recommendations[] = "Implement CSS custom properties for better maintainability";
        }
        
        if (!isset($this->results['lazy_loading'])) {
            $recommendations[] = "Add lazy loading for improved performance";
        }
        
        if (!isset($this->results['reduced_motion'])) {
            $recommendations[] = "Add support for reduced motion preferences";
        }
        
        return $recommendations;
    }
}

// Run validation if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $validator = new ModernDesignStandardsValidator();
    $result = $validator->validate();
    
    exit($result['passed'] ? 0 : 1);
}
