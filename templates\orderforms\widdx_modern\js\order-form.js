/**
 * WIDDX Modern Order Form JavaScript
 * Enhanced interactions and user experience
 * 
 * @package    WIDDX Order Form
 * <AUTHOR> Development Team
 * @version    1.0.0
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initSmoothScrolling();
        initProgressTracking();
        initProductInteractions();
        initFormValidation();
        initLoadingStates();
        initAccessibilityFeatures();
        initPerformanceMonitoring();
    });

    /**
     * Initialize smooth scrolling for category links
     */
    function initSmoothScrolling() {
        $('.widdx-category-link').on('click', function(e) {
            e.preventDefault();
            
            const targetId = $(this).attr('href');
            const targetElement = $(targetId);
            
            if (targetElement.length) {
                // Update active category
                $('.widdx-category-link').removeClass('active');
                $(this).addClass('active');
                
                // Smooth scroll to target
                $('html, body').animate({
                    scrollTop: targetElement.offset().top - 100
                }, 800, 'easeInOutCubic');
                
                // Highlight target group
                $('.widdx-product-group').removeClass('highlighted');
                targetElement.addClass('highlighted');
                
                setTimeout(() => {
                    targetElement.removeClass('highlighted');
                }, 2000);
            }
        });
    }

    /**
     * Initialize progress tracking
     */
    function initProgressTracking() {
        // Update progress based on scroll position
        $(window).on('scroll', function() {
            updateProgressIndicator();
            updateActiveCategory();
        });
        
        // Update progress on page interactions
        $('.widdx-product-card').on('click', function() {
            updateProgressStep(2);
        });
    }

    /**
     * Update progress indicator based on scroll position
     */
    function updateProgressIndicator() {
        const scrollTop = $(window).scrollTop();
        const docHeight = $(document).height() - $(window).height();
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        let step = 1;
        if (scrollPercent > 25) step = 2;
        if (scrollPercent > 50) step = 3;
        if (scrollPercent > 75) step = 4;
        
        updateProgressStep(step);
    }

    /**
     * Update progress step
     */
    function updateProgressStep(step) {
        $('.widdx-step').removeClass('active');
        $(`.widdx-step[data-step="${step}"]`).addClass('active');
        
        const progressPercent = (step / 4) * 100;
        $('.widdx-progress-fill').css('width', progressPercent + '%');
    }

    /**
     * Update active category based on scroll position
     */
    function updateActiveCategory() {
        let activeGroup = null;
        
        $('.widdx-product-group').each(function() {
            const groupTop = $(this).offset().top - 150;
            const groupBottom = groupTop + $(this).outerHeight();
            const scrollTop = $(window).scrollTop();
            
            if (scrollTop >= groupTop && scrollTop < groupBottom) {
                activeGroup = $(this).attr('id');
            }
        });
        
        if (activeGroup) {
            $('.widdx-category-link').removeClass('active');
            $(`.widdx-category-link[href="#${activeGroup}"]`).addClass('active');
        }
    }

    /**
     * Initialize product card interactions
     */
    function initProductInteractions() {
        // Product card hover effects
        $('.widdx-product-card').on('mouseenter', function() {
            $(this).addClass('hovered');
        }).on('mouseleave', function() {
            $(this).removeClass('hovered');
        });

        // Product comparison
        $('.widdx-product-card').on('click', '.btn-compare', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const productId = $(this).closest('.widdx-product-card').data('product-id');
            toggleProductComparison(productId);
        });

        // Quick view functionality
        $('.widdx-product-card').on('click', '.btn-quick-view', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const productId = $(this).closest('.widdx-product-card').data('product-id');
            showProductQuickView(productId);
        });

        // Add to cart with animation
        $('.widdx-product-card').on('click', '.btn-widdx-primary', function(e) {
            const button = $(this);
            const card = button.closest('.widdx-product-card');
            
            // Add loading state
            button.addClass('widdx-loading').prop('disabled', true);
            
            // Animate card
            card.addClass('adding-to-cart');
            
            // Simulate API call delay
            setTimeout(() => {
                button.removeClass('widdx-loading').prop('disabled', false);
                card.removeClass('adding-to-cart').addClass('added-to-cart');
                
                // Show success feedback
                showNotification('Product added to cart!', 'success');
                
                // Reset state after animation
                setTimeout(() => {
                    card.removeClass('added-to-cart');
                }, 2000);
            }, 1500);
        });
    }

    /**
     * Initialize form validation
     */
    function initFormValidation() {
        // Real-time validation for form fields
        $('input, select, textarea').on('blur', function() {
            validateField($(this));
        });

        $('input[type="email"]').on('input', function() {
            validateEmail($(this));
        });

        $('input[type="tel"]').on('input', function() {
            validatePhone($(this));
        });

        // Form submission validation
        $('form').on('submit', function(e) {
            if (!validateForm($(this))) {
                e.preventDefault();
                showNotification('Please correct the errors in the form.', 'error');
            }
        });
    }

    /**
     * Validate individual field
     */
    function validateField($field) {
        const value = $field.val().trim();
        const fieldType = $field.attr('type') || $field.prop('tagName').toLowerCase();
        const isRequired = $field.prop('required');
        
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (isRequired && !value) {
            isValid = false;
            errorMessage = 'This field is required.';
        }

        // Type-specific validation
        if (value && fieldType === 'email' && !isValidEmail(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address.';
        }

        if (value && fieldType === 'tel' && !isValidPhone(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid phone number.';
        }

        // Update field state
        updateFieldValidationState($field, isValid, errorMessage);
        
        return isValid;
    }

    /**
     * Update field validation state
     */
    function updateFieldValidationState($field, isValid, errorMessage) {
        const $group = $field.closest('.widdx-form-group');
        
        $group.removeClass('has-error has-success');
        $group.find('.widdx-error-message').remove();
        
        if (isValid) {
            $group.addClass('has-success');
        } else {
            $group.addClass('has-error');
            $group.append(`<span class="widdx-error-message">${errorMessage}</span>`);
        }
    }

    /**
     * Validate email format
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate phone format
     */
    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }

    /**
     * Validate entire form
     */
    function validateForm($form) {
        let isValid = true;
        
        $form.find('input, select, textarea').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    /**
     * Initialize loading states
     */
    function initLoadingStates() {
        // Global AJAX loading indicator
        $(document).ajaxStart(function() {
            showGlobalLoading();
        }).ajaxStop(function() {
            hideGlobalLoading();
        });

        // Button loading states
        $('.btn[type="submit"]').on('click', function() {
            const $btn = $(this);
            if (!$btn.hasClass('widdx-loading')) {
                $btn.addClass('widdx-loading').prop('disabled', true);
                
                const originalText = $btn.html();
                $btn.data('original-text', originalText);
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            }
        });
    }

    /**
     * Show global loading indicator
     */
    function showGlobalLoading() {
        if (!$('#widdx-global-loading').length) {
            $('body').append(`
                <div id="widdx-global-loading" class="widdx-global-loading">
                    <div class="widdx-loading-spinner">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <div class="widdx-loading-text">Loading...</div>
                    </div>
                </div>
            `);
        }
        $('#widdx-global-loading').fadeIn(200);
    }

    /**
     * Hide global loading indicator
     */
    function hideGlobalLoading() {
        $('#widdx-global-loading').fadeOut(200);
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const notificationHtml = `
            <div class="widdx-notification widdx-notification-${type}">
                <div class="widdx-notification-content">
                    <i class="fas fa-${getNotificationIcon(type)}"></i>
                    <span>${message}</span>
                </div>
                <button class="widdx-notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        const $notification = $(notificationHtml);
        
        if (!$('#widdx-notifications').length) {
            $('body').append('<div id="widdx-notifications"></div>');
        }
        
        $('#widdx-notifications').append($notification);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.widdx-notification-close').on('click', function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        });
    }

    /**
     * Get notification icon based on type
     */
    function getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Initialize accessibility features
     */
    function initAccessibilityFeatures() {
        // Keyboard navigation for product cards
        $('.widdx-product-card').attr('tabindex', '0').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).find('.btn-widdx-primary').click();
            }
        });

        // Focus management for modals
        $('.modal').on('shown.bs.modal', function() {
            $(this).find('[autofocus]').focus();
        });

        // Announce dynamic content changes
        function announceToScreenReader(message) {
            $('#widdx-announcements').text(message);
        }

        // Expose globally
        window.widdxAnnounce = announceToScreenReader;
    }

    /**
     * Initialize performance monitoring
     */
    function initPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', function() {
            if (window.performance && window.performance.timing) {
                const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
                console.log('Order form load time:', loadTime + 'ms');
                
                // Send performance data to analytics (if configured)
                if (window.gtag) {
                    gtag('event', 'timing_complete', {
                        name: 'order_form_load',
                        value: loadTime
                    });
                }
            }
        });

        // Monitor user interactions
        $('.widdx-product-card').on('click', function() {
            const productId = $(this).data('product-id');
            
            // Track product interest
            if (window.gtag) {
                gtag('event', 'view_item', {
                    currency: 'USD',
                    value: 0,
                    items: [{
                        item_id: productId,
                        item_name: $(this).find('.widdx-product-name').text()
                    }]
                });
            }
        });
    }

    // Expose utilities globally
    window.WIDDXOrderForm = {
        showNotification: showNotification,
        validateField: validateField,
        updateProgressStep: updateProgressStep
    };

})(jQuery);
