<?php
/**
 * Test Configuration for Lahza Payment Gateway
 */

return [
    // Test mode configuration - Using production URL with test credentials
    'test_mode' => false, // Set to false to use production URL
    'base_url' => 'https://api.lahza.io', // Production URL
    'sandbox_url' => 'https://api.lahza.io', // Using production URL as fallback
    
    // API Credentials - Get these from your Lahza Dashboard
    'test_public_key' => 'pk_test_0e9Ce7p2rYbMd5Gjwi1JSSfnUUmBs98eQ',  // For client-side operations
    'test_secret_key' => 'sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im',  // For server-side operations (keep this secure!)
    
    // WHMCS Simulation
    'whmcs' => [
        'system_url' => 'http://steadily-worthy-kangaroo.ngrok-free.app',
        'callback_path' => '/modules/gateways/callback/lahza.php',
        'return_path' => '/viewinvoice.php',
    ],
    
    // Test invoice data
    'invoice' => [
        'reference' => 'INV-' . time(),
        'amount' => '10000', // Amount in smallest currency unit (100.00 ILS = 10000 aghorot)
        'currency' => 'ILS', // Default currency (ILS, JOD, or USD)
        'description' => 'Test Invoice for Lahza Gateway',
        'email' => '<EMAIL>',
        'mobile' => '+966501234567',
        'first_name' => 'Test',
        'last_name' => 'User',
        'callback_url' => '', // Will be set dynamically
        'metadata' => [
            'custom_fields' => [
                ['display_name' => 'Invoice ID', 'value' => 'INV-' . time()],
                ['display_name' => 'Test Mode', 'value' => 'true'],
                ['display_name' => 'Environment', 'value' => 'test']
            ]
        ]
    ],
];
