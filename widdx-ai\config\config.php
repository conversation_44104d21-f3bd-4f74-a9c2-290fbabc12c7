<?php
// Security Configuration
if (!defined('SECURE_CONFIG_LOADED')) {
    define('SECURE_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Application Configuration
const APP_NAME = 'WIDDX';
const APP_VERSION = '1.0.0';
const APP_ENV = Env::get('APP_ENV', 'production');
const APP_DEBUG = Env::get('APP_DEBUG', false);

// Base URL configuration
const BASE_URL = Env::get('BASE_URL', 'http://localhost/widdx-ai');

// Ensure proper protocol
if (Env::get('FORCE_HTTPS', true) && !preg_match('/^https:/', BASE_URL)) {
    trigger_error('BASE_URL should use HTTPS protocol', E_USER_WARNING);
}

const API_TIMEOUT = Env::get('API_TIMEOUT', 30);
const CACHE_TTL = Env::get('CACHE_TTL', 86400);

// Logging configuration
const LOG_FILE = Env::get('LOG_FILE', __DIR__ . '/../logs/app.log');
const ERROR_LOG_FILE = Env::get('ERROR_LOG_FILE', __DIR__ . '/../logs/error.log');

// Ensure log directories exist
$logDir = dirname(LOG_FILE);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Set proper permissions
chmod($logDir, 0755);

const MAX_QUESTION_LENGTH = Env::get('MAX_QUESTION_LENGTH', 2000);
const MIN_QUESTION_LENGTH = Env::get('MIN_QUESTION_LENGTH', 3);

// Rate limiting
const RATE_LIMIT_WINDOW = Env::get('RATE_LIMIT_WINDOW', 60);
const MAX_REQUESTS_PER_MINUTE = Env::get('MAX_REQUESTS_PER_MINUTE', 60);

// Redis configuration
const REDIS_HOST = Env::get('REDIS_HOST', '127.0.0.1');
const REDIS_PORT = Env::get('REDIS_PORT', 6379);

// Session configuration
const SESSION_LIFETIME = Env::get('SESSION_LIFETIME', 3600);
const SESSION_COOKIE_SECURE = Env::get('APP_ENV') === 'production';
const SESSION_COOKIE_HTTPONLY = true;

// Cache configuration
const CACHE_ENABLED = Env::get('CACHE_ENABLED', true);
const CACHE_PROVIDER = Env::get('CACHE_PROVIDER', 'redis');

// Language configuration
const DEFAULT_LANGUAGE = Env::get('DEFAULT_LANGUAGE', 'ar');
const SUPPORTED_LANGUAGES = Env::get('SUPPORTED_LANGUAGES', ['ar', 'en']);

// Logging configuration
const MAX_LOG_SIZE = Env::get('MAX_LOG_SIZE', 10485760);

// Error handling
const ERROR_REPORTING_LEVEL = Env::get('ERROR_REPORTING_LEVEL', E_ALL);
const MEMORY_LIMIT = Env::get('MEMORY_LIMIT', '256M');

// Load environment-specific overrides
if (file_exists(__DIR__ . '/config_' . APP_ENV . '.php')) {
    require_once __DIR__ . '/config_' . APP_ENV . '.php';
}
