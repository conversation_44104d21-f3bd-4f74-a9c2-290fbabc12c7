<?php
require_once __DIR__ . '/../includes/Monitor.php';
require_once __DIR__ . '/../includes/Database.php';

header('Content-Type: application/json');

try {
    // Initialize monitor
    $monitor = new Monitor();
    
    // Get system status
    $status = $monitor->getSystemStatus();
    
    // Get usage data for last 24 hours
    $usage = getUsageData();
    
    // Get source distribution
    $sources = getSourceDistribution();
    
    // Get recent activity
    $activity = getRecentActivity();
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'system' => $status,
            'usage' => $usage,
            'sources' => $sources,
            'activity' => $activity
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'حدث خطأ في جلب البيانات',
        'details' => $e->getMessage()
    ]);
}

function getUsageData() {
    $db = new Database();
    $stmt = $db->getConnection()->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:00') as time,
            COUNT(*) as count
        FROM questions
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY time
        ORDER BY time
    ");
    
    $data = $stmt->fetchAll();
    return [
        'labels' => array_column($data, 'time'),
        'values' => array_column($data, 'count')
    ];
}

function getSourceDistribution() {
    $db = new Database();
    $stmt = $db->getConnection()->query("
        SELECT 
            source_model,
            COUNT(*) as count
        FROM questions
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY source_model
    ");
    
    $data = $stmt->fetchAll();
    return [
        'labels' => array_column($data, 'source_model'),
        'values' => array_column($data, 'count')
    ];
}

function getRecentActivity() {
    $db = new Database();
    $stmt = $db->getConnection()->query("
        SELECT 
            created_at,
            question_text,
            answer_text,
            source_model
        FROM questions
        ORDER BY created_at DESC
        LIMIT 50
    ");
    
    return $stmt->fetchAll();
}
