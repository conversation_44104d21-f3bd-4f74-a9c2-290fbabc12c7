/*!
 * WIDDX Unified CSS - Minified Production Version
 * Modern design standards compliant stylesheet
 * Copyright (c) 2025 WIDDX Development Team
 */

/* CSS Custom Properties */
:root{--widdx-primary:#2c5aa0;--widdx-secondary:#1e3d6f;--widdx-accent:#4a90e2;--widdx-success:#27ae60;--widdx-warning:#f39c12;--widdx-danger:#e74c3c;--widdx-info:#3498db;--widdx-gradient-primary:linear-gradient(135deg,#2c5aa0 0%,#4a90e2 100%);--widdx-gradient-secondary:linear-gradient(135deg,#1e3d6f 0%,#2c5aa0 100%);--widdx-font-primary:'Inter',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--widdx-font-secondary:'Poppins',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--widdx-border-radius:8px;--widdx-border-radius-lg:12px;--widdx-shadow:0 4px 6px rgba(0,0,0,0.1);--widdx-shadow-lg:0 10px 25px rgba(0,0,0,0.15);--widdx-bg-primary:#ffffff;--widdx-bg-secondary:#f8f9fa;--widdx-text-primary:#2c3e50;--widdx-text-secondary:#6c757d}

/* Dark Mode */
@media (prefers-color-scheme:dark){:root{--widdx-bg-primary:#1a1a1a;--widdx-bg-secondary:#2d2d2d;--widdx-text-primary:#ffffff;--widdx-text-secondary:#cccccc}}

/* Typography */
body{font-family:var(--widdx-font-primary);line-height:1.6;color:var(--widdx-text-primary);text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}h1,h2,h3,h4,h5,h6{font-family:var(--widdx-font-secondary);font-weight:600;color:var(--widdx-secondary)}

/* Fluid Typography */
.widdx-fluid-text{font-size:clamp(1rem,2.5vw,1.5rem);line-height:clamp(1.4,1.5vw + 1rem,1.8)}.widdx-fluid-heading{font-size:clamp(1.5rem,4vw,3rem);line-height:clamp(1.2,1vw + 1rem,1.4)}

/* Layout Systems */
.widdx-grid-container{display:grid;gap:2rem;grid-template-columns:repeat(auto-fit,minmax(300px,1fr))}.widdx-grid-2col{display:grid;grid-template-columns:1fr 1fr;gap:2rem}.widdx-grid-3col{display:grid;grid-template-columns:repeat(3,1fr);gap:2rem}@media (max-width:768px){.widdx-grid-2col,.widdx-grid-3col{grid-template-columns:1fr}}

/* Flexbox Utilities */
.widdx-flex{display:flex}.widdx-flex-col{flex-direction:column}.widdx-flex-row{flex-direction:row}.widdx-items-center{align-items:center}.widdx-justify-center{justify-content:center}.widdx-justify-between{justify-content:space-between}

/* Modern Components */
.widdx-card{background:var(--widdx-bg-primary);border-radius:var(--widdx-border-radius);box-shadow:var(--widdx-shadow);padding:1.5rem;transition:all 0.3s cubic-bezier(0.4,0,0.2,1)}.widdx-card:hover{box-shadow:var(--widdx-shadow-lg);transform:translateY(-2px)}

/* Buttons */
.widdx-btn{display:inline-flex;align-items:center;justify-content:center;min-height:44px;min-width:44px;padding:0.75rem 1.5rem;border:none;border-radius:var(--widdx-border-radius);font-family:var(--widdx-font-primary);font-weight:500;text-decoration:none;cursor:pointer;transition:all 0.2s cubic-bezier(0.4,0,0.2,1);position:relative;overflow:hidden}.widdx-btn:focus-visible{outline:2px solid var(--widdx-primary);outline-offset:2px}.widdx-btn:focus:not(:focus-visible){outline:none}.widdx-btn:hover{transform:translateY(-1px)}.widdx-btn:active{transform:translateY(0);transition-duration:0.1s}

/* Button Variants */
.widdx-btn--primary{background:var(--widdx-gradient-primary);color:#ffffff}.widdx-btn--secondary{background:var(--widdx-secondary);color:#ffffff}.widdx-btn--outline{background:transparent;border:2px solid var(--widdx-primary);color:var(--widdx-primary)}

/* Forms */
.widdx-form-control{appearance:none;background:var(--widdx-bg-primary);border:1px solid rgba(44,90,160,0.3);border-radius:var(--widdx-border-radius);padding:0.75rem 1rem;font-size:1rem;line-height:1.5;width:100%;transition:all 0.2s cubic-bezier(0.4,0,0.2,1)}.widdx-form-control:focus{border-color:var(--widdx-primary);box-shadow:0 0 0 3px rgba(44,90,160,0.2);outline:none}

/* Accessibility */
.widdx-sr-only{position:absolute!important;width:1px!important;height:1px!important;padding:0!important;margin:-1px!important;overflow:hidden!important;clip:rect(0,0,0,0)!important;white-space:nowrap!important;border:0!important}

/* Focus Management */
.widdx-theme *:focus{outline:2px solid var(--widdx-primary);outline-offset:2px}.widdx-theme .btn:focus{box-shadow:0 0 0 0.2rem rgba(44,90,160,0.5);outline:none}

/* Reduced Motion */
@media (prefers-reduced-motion:reduce){.widdx-theme *{animation-duration:0.01ms!important;animation-iteration-count:1!important;transition-duration:0.01ms!important}}

/* High Contrast */
@media (prefers-contrast:high){.widdx-theme{--widdx-primary:#000080;--widdx-secondary:#000000}}

/* Touch Targets */
.widdx-touch-target{min-height:44px;min-width:44px;display:flex;align-items:center;justify-content:center}

/* Responsive Images */
.widdx-img-responsive{max-width:100%;height:auto;display:block}

/* Lazy Loading */
.widdx-lazy{opacity:0;transition:opacity 0.3s}.widdx-loaded{opacity:1}

/* Loading States */
.widdx-loading{position:relative;overflow:hidden}.widdx-loading::after{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.4),transparent);animation:widdxShimmer 1.5s infinite}@keyframes widdxShimmer{0%{left:-100%}100%{left:100%}}

/* Animations */
@keyframes widdxFadeInUp{from{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes widdxSlideInLeft{from{opacity:0;transform:translateX(-30px)}to{opacity:1;transform:translateX(0)}}

/* Animation Classes */
.widdx-animate-fade-in-up{animation:widdxFadeInUp 0.6s ease-out}.widdx-animate-slide-in-left{animation:widdxSlideInLeft 0.6s ease-out}

/* Scroll Animations */
.widdx-animate-on-scroll{opacity:0;transform:translateY(30px);transition:all 0.6s cubic-bezier(0.4,0,0.2,1)}.widdx-animate-in{opacity:1;transform:translateY(0)}

/* Utilities */
.widdx-text-center{text-align:center}.widdx-text-left{text-align:left}.widdx-text-right{text-align:right}.widdx-mb-1{margin-bottom:0.25rem}.widdx-mb-2{margin-bottom:0.5rem}.widdx-mb-3{margin-bottom:1rem}.widdx-mb-4{margin-bottom:1.5rem}.widdx-mt-1{margin-top:0.25rem}.widdx-mt-2{margin-top:0.5rem}.widdx-mt-3{margin-top:1rem}.widdx-mt-4{margin-top:1.5rem}.widdx-p-1{padding:0.25rem}.widdx-p-2{padding:0.5rem}.widdx-p-3{padding:1rem}.widdx-p-4{padding:1.5rem}

/* Aspect Ratios */
.widdx-aspect-square{aspect-ratio:1/1}.widdx-aspect-video{aspect-ratio:16/9}.widdx-aspect-photo{aspect-ratio:4/3}

/* Container Queries */
@supports (container-type:inline-size){.widdx-container{container-type:inline-size}@container (min-width:400px){.widdx-responsive-text{font-size:1.125rem;line-height:1.7}}@container (min-width:600px){.widdx-responsive-text{font-size:1.25rem;line-height:1.8}}}

/* Modern CSS Features */
@supports (backdrop-filter:blur(10px)){.widdx-backdrop{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px)}}

/* Print Styles */
@media print{.widdx-print-hidden{display:none!important}.widdx-card{box-shadow:none;border:1px solid #000}.widdx-btn{background:transparent!important;color:#000!important;border:1px solid #000!important}}

/* Mobile Navigation */
.widdx-nav-toggle{display:none}@media (max-width:768px){.widdx-nav-toggle{display:block}.widdx-nav-menu{display:none}.widdx-nav-menu.open{display:block}}

/* Interactive Elements */
.widdx-interactive{cursor:pointer;transition:all 0.2s ease}.widdx-interactive:hover{opacity:0.8}

/* Status Indicators */
.widdx-status-success{color:var(--widdx-success)}.widdx-status-warning{color:var(--widdx-warning)}.widdx-status-danger{color:var(--widdx-danger)}.widdx-status-info{color:var(--widdx-info)}

/* Modern Form Elements */
.widdx-checkbox{appearance:none;width:1.25rem;height:1.25rem;border:2px solid var(--widdx-primary);border-radius:0.25rem;position:relative;cursor:pointer;transition:all 0.2s cubic-bezier(0.4,0,0.2,1)}.widdx-checkbox:checked{background:var(--widdx-primary);border-color:var(--widdx-primary)}.widdx-checkbox:checked::after{content:'✓';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:white;font-size:0.875rem;font-weight:bold}

/* Notification System */
.widdx-toast{position:fixed;top:20px;right:20px;padding:12px 24px;border-radius:var(--widdx-border-radius);color:white;font-weight:500;transform:translateX(100%);transition:transform 0.3s ease;z-index:10000}.widdx-toast--show{transform:translateX(0)}.widdx-toast--success{background:var(--widdx-success)}.widdx-toast--error{background:var(--widdx-danger)}.widdx-toast--info{background:var(--widdx-info)}

/* Ripple Effect */
.widdx-ripple{position:absolute;border-radius:50%;pointer-events:none;transform:scale(0);animation:widdx-ripple-animation 0.6s ease-out}@keyframes widdx-ripple-animation{to{transform:scale(4);opacity:0}}

/* Performance Optimizations */
.widdx-content-visibility{content-visibility:auto;contain-intrinsic-size:0 500px}

/* Browser Optimizations */
@supports (-webkit-touch-callout:none){.widdx-safari-fix{-webkit-transform:translateZ(0);-webkit-backface-visibility:hidden}}

/* Logical Properties */
.widdx-logical{margin-block-start:1rem;margin-block-end:1rem;margin-inline-start:0.5rem;margin-inline-end:0.5rem;padding-block:1rem;padding-inline:1.5rem}

/* Spacing Utilities with Logical Properties */
.widdx-mbs-1{margin-block-start:0.25rem}.widdx-mbs-2{margin-block-start:0.5rem}.widdx-mbs-3{margin-block-start:1rem}.widdx-mis-1{margin-inline-start:0.25rem}.widdx-mis-2{margin-inline-start:0.5rem}.widdx-mis-3{margin-inline-start:1rem}

/* Modern Stack Layout */
.widdx-stack>*+*{margin-block-start:1rem}.widdx-stack-sm>*+*{margin-block-start:0.5rem}.widdx-stack-lg>*+*{margin-block-start:1.5rem}

/* Intrinsic Web Design */
.widdx-intrinsic{display:grid;grid-template-columns:repeat(auto-fit,minmax(min(300px,100%),1fr));gap:clamp(1rem,3vw,2rem)}

/* Modern Color Functions */
@supports (color:color-mix(in srgb,red,blue)){.widdx-modern-colors{background:color-mix(in srgb,var(--widdx-primary) 80%,transparent);border-color:color-mix(in srgb,var(--widdx-primary) 30%,transparent)}}

/* Forced Colors Mode */
@media (forced-colors:active){.widdx-forced-colors{border:1px solid;background:transparent}}

/* CSS Nesting Support */
@supports (selector(&)){.widdx-nested{color:var(--widdx-text-primary)}&:hover{background:var(--widdx-bg-secondary)}}

/* Responsive Design Patterns */
@media (min-width:768px){.widdx-tablet-up{display:block}.widdx-mobile-only{display:none}}@media (max-width:767px){.widdx-tablet-up{display:none}.widdx-mobile-only{display:block}}

/* Modern Scrollbar */
.widdx-scrollbar{scrollbar-width:thin;scrollbar-color:var(--widdx-primary) transparent}.widdx-scrollbar::-webkit-scrollbar{width:8px}.widdx-scrollbar::-webkit-scrollbar-track{background:transparent}.widdx-scrollbar::-webkit-scrollbar-thumb{background:var(--widdx-primary);border-radius:4px}

/* Enhanced Focus Indicators */
.widdx-focus-ring:focus-visible{outline:2px solid var(--widdx-primary);outline-offset:2px;border-radius:var(--widdx-border-radius)}

/* Modern Layout Helpers */
.widdx-center{display:grid;place-items:center}.widdx-cluster{display:flex;flex-wrap:wrap;gap:1rem;align-items:center}.widdx-sidebar{display:grid;grid-template-columns:auto 1fr;gap:2rem}@media (max-width:768px){.widdx-sidebar{grid-template-columns:1fr}}

/* Performance Hints */
.widdx-will-change{will-change:transform}.widdx-gpu-accelerated{transform:translateZ(0)}

/* Modern Typography Features */
.widdx-typography{font-feature-settings:"kern" 1,"liga" 1,"calt" 1;font-variant-numeric:oldstyle-nums proportional-nums}

/* CSS Cascade Layers Support */
@supports (color:color(display-p3 1 0 0)){.widdx-wide-gamut{color:color(display-p3 0.17 0.35 0.63)}}

/* Modern Interaction States */
.widdx-hover-lift:hover{transform:translateY(-2px);box-shadow:var(--widdx-shadow-lg)}.widdx-hover-scale:hover{transform:scale(1.05)}.widdx-active-scale:active{transform:scale(0.98)}

/* Skeleton Loading */
.widdx-skeleton{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0 50%,#f0f0f0 75%);background-size:200% 100%;animation:widdx-skeleton-loading 1.5s infinite}@keyframes widdx-skeleton-loading{0%{background-position:200% 0}100%{background-position:-200% 0}}

/* Modern Grid Patterns */
.widdx-auto-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem}.widdx-masonry{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));grid-auto-rows:masonry;gap:1.5rem}

/* Fallback for masonry */
@supports not (grid-auto-rows:masonry){.widdx-masonry{display:flex;flex-wrap:wrap}.widdx-masonry>*{flex:1 1 300px}}

/* Modern Form Validation */
.widdx-form-control:invalid{border-color:var(--widdx-danger)}.widdx-form-control:valid{border-color:var(--widdx-success)}.widdx-form-control:user-invalid{border-color:var(--widdx-danger);box-shadow:0 0 0 3px rgba(231,76,60,0.2)}

/* Modern State Management */
.widdx-loading-state{opacity:0.6;pointer-events:none}.widdx-error-state{border-color:var(--widdx-danger);background-color:rgba(231,76,60,0.05)}.widdx-success-state{border-color:var(--widdx-success);background-color:rgba(39,174,96,0.05)}

/* Modern Spacing System */
.widdx-space-y-1>*+*{margin-top:0.25rem}.widdx-space-y-2>*+*{margin-top:0.5rem}.widdx-space-y-3>*+*{margin-top:1rem}.widdx-space-y-4>*+*{margin-top:1.5rem}.widdx-space-x-1>*+*{margin-left:0.25rem}.widdx-space-x-2>*+*{margin-left:0.5rem}.widdx-space-x-3>*+*{margin-left:1rem}

/* Modern Visibility Utilities */
.widdx-visible{visibility:visible}.widdx-invisible{visibility:hidden}.widdx-opacity-0{opacity:0}.widdx-opacity-50{opacity:0.5}.widdx-opacity-100{opacity:1}

/* Modern Position Utilities */
.widdx-relative{position:relative}.widdx-absolute{position:absolute}.widdx-fixed{position:fixed}.widdx-sticky{position:sticky}

/* Modern Display Utilities */
.widdx-block{display:block}.widdx-inline{display:inline}.widdx-inline-block{display:inline-block}.widdx-hidden{display:none}

/* Modern Overflow Utilities */
.widdx-overflow-hidden{overflow:hidden}.widdx-overflow-auto{overflow:auto}.widdx-overflow-scroll{overflow:scroll}

/* Modern Z-Index Scale */
.widdx-z-0{z-index:0}.widdx-z-10{z-index:10}.widdx-z-20{z-index:20}.widdx-z-30{z-index:30}.widdx-z-40{z-index:40}.widdx-z-50{z-index:50}

/* Modern Border Utilities */
.widdx-border{border:1px solid rgba(0,0,0,0.1)}.widdx-border-t{border-top:1px solid rgba(0,0,0,0.1)}.widdx-border-r{border-right:1px solid rgba(0,0,0,0.1)}.widdx-border-b{border-bottom:1px solid rgba(0,0,0,0.1)}.widdx-border-l{border-left:1px solid rgba(0,0,0,0.1)}

/* Modern Shadow Utilities */
.widdx-shadow-sm{box-shadow:0 1px 2px rgba(0,0,0,0.05)}.widdx-shadow{box-shadow:var(--widdx-shadow)}.widdx-shadow-lg{box-shadow:var(--widdx-shadow-lg)}.widdx-shadow-none{box-shadow:none}

/* Modern Rounded Utilities */
.widdx-rounded{border-radius:var(--widdx-border-radius)}.widdx-rounded-lg{border-radius:var(--widdx-border-radius-lg)}.widdx-rounded-full{border-radius:9999px}.widdx-rounded-none{border-radius:0}

/* Modern Width Utilities */
.widdx-w-full{width:100%}.widdx-w-auto{width:auto}.widdx-w-fit{width:fit-content}.widdx-w-screen{width:100vw}

/* Modern Height Utilities */
.widdx-h-full{height:100%}.widdx-h-auto{height:auto}.widdx-h-fit{height:fit-content}.widdx-h-screen{height:100vh}

/* Modern Flex Utilities */
.widdx-flex-1{flex:1 1 0%}.widdx-flex-auto{flex:1 1 auto}.widdx-flex-initial{flex:0 1 auto}.widdx-flex-none{flex:none}.widdx-flex-shrink{flex-shrink:1}.widdx-flex-shrink-0{flex-shrink:0}.widdx-flex-grow{flex-grow:1}.widdx-flex-grow-0{flex-grow:0}

/* Modern Grid Utilities */
.widdx-grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.widdx-grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.widdx-grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.widdx-grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.widdx-col-span-1{grid-column:span 1/span 1}.widdx-col-span-2{grid-column:span 2/span 2}.widdx-col-span-3{grid-column:span 3/span 3}

/* Modern Gap Utilities */
.widdx-gap-1{gap:0.25rem}.widdx-gap-2{gap:0.5rem}.widdx-gap-3{gap:1rem}.widdx-gap-4{gap:1.5rem}.widdx-gap-6{gap:2rem}.widdx-gap-8{gap:2.5rem}

/* Modern Transition Utilities */
.widdx-transition{transition:all 0.2s cubic-bezier(0.4,0,0.2,1)}.widdx-transition-colors{transition:color,background-color,border-color 0.2s cubic-bezier(0.4,0,0.2,1)}.widdx-transition-transform{transition:transform 0.2s cubic-bezier(0.4,0,0.2,1)}.widdx-transition-opacity{transition:opacity 0.2s cubic-bezier(0.4,0,0.2,1)}

/* Modern Transform Utilities */
.widdx-transform{transform:translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1)}.widdx-scale-95{transform:scale(0.95)}.widdx-scale-100{transform:scale(1)}.widdx-scale-105{transform:scale(1.05)}.widdx-rotate-45{transform:rotate(45deg)}.widdx-rotate-90{transform:rotate(90deg)}.widdx-rotate-180{transform:rotate(180deg)}

/* Modern Filter Utilities */
.widdx-blur{filter:blur(8px)}.widdx-blur-sm{filter:blur(4px)}.widdx-blur-none{filter:blur(0)}.widdx-brightness-50{filter:brightness(0.5)}.widdx-brightness-100{filter:brightness(1)}.widdx-brightness-125{filter:brightness(1.25)}

/* Modern Backdrop Utilities */
@supports (backdrop-filter:blur(8px)){.widdx-backdrop-blur{backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px)}.widdx-backdrop-blur-sm{backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px)}}

/* Modern Cursor Utilities */
.widdx-cursor-pointer{cursor:pointer}.widdx-cursor-not-allowed{cursor:not-allowed}.widdx-cursor-wait{cursor:wait}.widdx-cursor-text{cursor:text}.widdx-cursor-move{cursor:move}.widdx-cursor-help{cursor:help}

/* Modern Select Utilities */
.widdx-select-none{user-select:none}.widdx-select-text{user-select:text}.widdx-select-all{user-select:all}.widdx-select-auto{user-select:auto}

/* Modern Resize Utilities */
.widdx-resize-none{resize:none}.widdx-resize{resize:both}.widdx-resize-x{resize:horizontal}.widdx-resize-y{resize:vertical}

/* Modern List Utilities */
.widdx-list-none{list-style-type:none}.widdx-list-disc{list-style-type:disc}.widdx-list-decimal{list-style-type:decimal}

/* Modern Object Utilities */
.widdx-object-contain{object-fit:contain}.widdx-object-cover{object-fit:cover}.widdx-object-fill{object-fit:fill}.widdx-object-none{object-fit:none}.widdx-object-scale-down{object-fit:scale-down}

/* Modern Pointer Events */
.widdx-pointer-events-none{pointer-events:none}.widdx-pointer-events-auto{pointer-events:auto}

/* Modern Appearance */
.widdx-appearance-none{appearance:none}

/* Modern Outline */
.widdx-outline-none{outline:2px solid transparent;outline-offset:2px}.widdx-outline{outline:2px solid var(--widdx-primary);outline-offset:2px}

/* Modern Ring Utilities */
.widdx-ring{box-shadow:0 0 0 3px rgba(44,90,160,0.5)}.widdx-ring-primary{box-shadow:0 0 0 3px rgba(44,90,160,0.5)}.widdx-ring-success{box-shadow:0 0 0 3px rgba(39,174,96,0.5)}.widdx-ring-danger{box-shadow:0 0 0 3px rgba(231,76,60,0.5)}

/* Modern Divide Utilities */
.widdx-divide-y>*+*{border-top:1px solid rgba(0,0,0,0.1)}.widdx-divide-x>*+*{border-left:1px solid rgba(0,0,0,0.1)}

/* Modern Screen Reader Utilities */
.widdx-not-sr-only{position:static;width:auto;height:auto;padding:0;margin:0;overflow:visible;clip:auto;white-space:normal}

/* Modern Placeholder Utilities */
.widdx-placeholder-gray::placeholder{color:#9ca3af}.widdx-placeholder-primary::placeholder{color:var(--widdx-primary)}

/* Modern Caret Utilities */
.widdx-caret-primary{caret-color:var(--widdx-primary)}.widdx-caret-transparent{caret-color:transparent}

/* Modern Accent Utilities */
.widdx-accent-primary{accent-color:var(--widdx-primary)}.widdx-accent-auto{accent-color:auto}

/* Modern Scroll Utilities */
.widdx-scroll-smooth{scroll-behavior:smooth}.widdx-scroll-auto{scroll-behavior:auto}

/* Modern Snap Utilities */
.widdx-snap-x{scroll-snap-type:x mandatory}.widdx-snap-y{scroll-snap-type:y mandatory}.widdx-snap-both{scroll-snap-type:both mandatory}.widdx-snap-none{scroll-snap-type:none}.widdx-snap-start{scroll-snap-align:start}.widdx-snap-end{scroll-snap-align:end}.widdx-snap-center{scroll-snap-align:center}

/* Modern Touch Utilities */
.widdx-touch-auto{touch-action:auto}.widdx-touch-none{touch-action:none}.widdx-touch-pan-x{touch-action:pan-x}.widdx-touch-pan-y{touch-action:pan-y}.widdx-touch-manipulation{touch-action:manipulation}

/* Modern Will Change Utilities */
.widdx-will-change-auto{will-change:auto}.widdx-will-change-scroll{will-change:scroll-position}.widdx-will-change-contents{will-change:contents}.widdx-will-change-transform{will-change:transform}

/* Modern Content Utilities */
.widdx-content-none{content:none}.widdx-content-empty{content:''}

/* Modern Isolation Utilities */
.widdx-isolate{isolation:isolate}.widdx-isolation-auto{isolation:auto}

/* Modern Mix Blend Utilities */
.widdx-mix-blend-normal{mix-blend-mode:normal}.widdx-mix-blend-multiply{mix-blend-mode:multiply}.widdx-mix-blend-screen{mix-blend-mode:screen}.widdx-mix-blend-overlay{mix-blend-mode:overlay}

/* Modern Background Blend Utilities */
.widdx-bg-blend-normal{background-blend-mode:normal}.widdx-bg-blend-multiply{background-blend-mode:multiply}.widdx-bg-blend-screen{background-blend-mode:screen}

/* Modern Box Decoration Utilities */
.widdx-box-decoration-clone{box-decoration-break:clone}.widdx-box-decoration-slice{box-decoration-break:slice}

/* Modern Float Utilities */
.widdx-float-right{float:right}.widdx-float-left{float:left}.widdx-float-none{float:none}

/* Modern Clear Utilities */
.widdx-clear-left{clear:left}.widdx-clear-right{clear:right}.widdx-clear-both{clear:both}.widdx-clear-none{clear:none}
