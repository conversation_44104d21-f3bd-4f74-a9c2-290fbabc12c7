<?php
// Direct database configuration
$dbHost = Env::get('DB_HOST', 'localhost');
$dbUser = Env::get('DB_USER');
$dbPass = Env::get('DB_PASS');
$dbName = Env::get('DB_NAME');

try {
    // Create connection
    $dsn = "mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_PERSISTENT => true
    ];
    
    $conn = new PDO($dsn, $dbUser, $dbPass, $options);
    
    echo "Connected successfully to database: " . $dbName . "<br>";
    
    // List all tables in the database
    $tables = $conn->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "<br>Tables in database:<br>";
        foreach ($tables as $table) {
            echo "- " . $table . "<br>";
        }
    } else {
        echo "<br>No tables found in the database.<br>";
    }
    
} catch(PDOException $e) {
    error_log("Database Connection Error: " . $e->getMessage());
    
    // Don't show sensitive error details in production
    if (Env::get('APP_ENV', 'production') === 'production') {
        echo "Connection failed: An error occurred while connecting to the database. Please contact support.";
        exit;
    }
    
    echo "Connection failed: " . $e->getMessage() . "<br>";
    
    // Try to connect without selecting a database to check if the database exists
    try {
        $conn = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<br>Successfully connected to MySQL server. Database '$dbName' might not exist.<br>";
        
        // Check if database exists
        $stmt = $conn->query("SHOW DATABASES LIKE '$dbName'");
        if ($stmt->rowCount() > 0) {
            echo "Database '$dbName' exists but there was an error connecting to it.<br>";
        } else {
            echo "Database '$dbName' does not exist.<br>";
        }
        
    } catch(PDOException $e2) {
        echo "<br>Could not connect to MySQL server. Please check your database credentials in the .env file.<br>";
        echo "Error: " . $e2->getMessage() . "<br>";
    }
}
?>
