{
    "timestamp": "2025-06-28 01:48:22",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751068102",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/EAK5LfLMCt",
            "access_code": "EAK5LfLMCt",
            "reference": "INV-1-1751068102"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 01:48:25",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751068104",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/RO2J3cH5Vi",
            "access_code": "RO2J3cH5Vi",
            "reference": "INV-1-1751068104"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:11:59",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751069518",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/4N56FvNOT0",
            "access_code": "4N56FvNOT0",
            "reference": "INV-1-1751069518"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:12:00",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751069520",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/mtGyvDsIdF",
            "access_code": "mtGyvDsIdF",
            "reference": "INV-1-1751069520"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:12:51",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751069570",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/GQyVbiASFU",
            "access_code": "GQyVbiASFU",
            "reference": "INV-1-1751069570"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:22:28",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751070148",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/y4kTaoWER8",
            "access_code": "y4kTaoWER8",
            "reference": "INV-1-1751070148"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-06-28 02:22:30",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1751070149",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/Kz0ha4Qt46",
            "access_code": "Kz0ha4Qt46",
            "reference": "INV-1-1751070149"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-07-11 13:00:34",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 100,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1752231633",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/AwFH9KE99P",
            "access_code": "AwFH9KE99P",
            "reference": "INV-1-1752231633"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
{
    "timestamp": "2025-07-18 13:00:39",
    "action": "CreatePayment",
    "http_code": 200,
    "request": {
        "url": "https:\/\/api.lahza.io\/transaction\/initialize",
        "data": {
            "amount": 110,
            "email": "<EMAIL>",
            "currency": "USD",
            "reference": "INV-1-1752836439",
            "callback_url": "http:\/\/localhost\/modules\/gateways\/callback\/lahza.php",
            "redirect_url": "http:\/\/localhost\/viewinvoice.php?id=1",
            "metadata": "{\"invoice_id\":1,\"customer_name\":\"test test\",\"whmcs_version\":\"8.12.1-release.1\"}",
            "first_name": "test",
            "last_name": "test"
        }
    },
    "response": {
        "status": true,
        "message": "Authorization URL created",
        "data": {
            "authorization_url": "https:\/\/checkout.lahza.io\/4HyWp37wer",
            "access_code": "4HyWp37wer",
            "reference": "INV-1-1752836439"
        }
    },
    "error": "",
    "params": {
        "invoiceid": 1
    }
}
