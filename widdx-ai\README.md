# WIDDX - مساعد الذكاء الاصطناعي

WIDDX هو مساعد ذكي يدمج بين نماذج الذكاء الاصطناعي المتقدمة لتوفير إجابات دقيقة وموثوقة على أسئلتك.

## المميزات

- دمج مع نماذج DeepSeek و Gemini
- تخزين الإجابات لسرعة الاستجابة
- واجهة مستخدم سهلة وحديثة
- دعم اللغة العربية
- تتبع مصدر الإجابات

## المتطلبات

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx) مع تفعيل mod_rewrite
- Composer (لإدارة التبعيات)

## التثبيت

1. نسخ المشروع:
   ```bash
   git clone https://github.com/yourusername/widdx-ai.git
   cd widdx-ai
   ```

2. إنشاء قاعدة البيانات:
   ```bash
   mysql -u username -p widdx_ai < database/setup.sql
   ```

3. تحديث ملف التكوين في `config/database.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   define('DB_NAME', 'widdx_ai');
   ```

4. إعداد مفاتيح API في `config/database.php`:
   ```php
   define('API_KEY_DEEPSEEK', 'your_deepseek_api_key');
   define('API_KEY_GEMINI', 'your_gemini_api_key');
   ```

5. تكوين خادم الويب لاستهداف المجلد `public`

6. تأكد من أن مجلد `logs` قابل للكتابة من قبل خادم الويب:
   ```bash
   chmod 777 logs
   ```

## هيكل المجلدات

- `/api` - نقاط النهاية للـ API
- `/config` - ملفات التكوين
- `/database` - مخطط قاعدة البيانات وعمليات الترحيل
- `/includes` - الفئات والمكتبات PHP
- `/logs` - سجلات التطبيق
- `/public` - الملفات العامة (مجلد المستندات)

## الأمان

- استخدم HTTPS في الإنتاج
- تخزين مفاتيح API والمعلومات الحساسة في متغيرات البيئة
- تحديث التبعيات باستمرار
- نسخ احتياطي دوري لقاعدة البيانات

## الترخيص

MIT
