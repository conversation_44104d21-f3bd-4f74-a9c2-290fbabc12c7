#!/bin/bash
#
# WIDDX Post-Launch Monitoring Setup
# Comprehensive monitoring, alerting, and performance tracking
#
# @package    WIDDX Production Deployment
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration
WHMCS_PATH="/var/www/html/whmcs"
MONITORING_PATH="/var/monitoring/widdx"
LOG_PATH="/var/log/widdx-deployment"
ALERT_EMAIL="<EMAIL>"
DOMAIN="yourdomain.com"

# Database configuration
DB_HOST=""
DB_NAME=""
DB_USER=""
DB_PASS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/monitoring.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/monitoring.log"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/monitoring.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/monitoring.log"
}

# Create directories
mkdir -p "${MONITORING_PATH}" "${LOG_PATH}"

log "📊 Starting WIDDX Post-Launch Monitoring Setup"

# 1. Read Database Configuration
log "📖 Reading database configuration..."

if [[ ! -f "${WHMCS_PATH}/configuration.php" ]]; then
    error "WHMCS configuration file not found"
fi

DB_HOST=$(grep -oP "(?<=\\\$db_host = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "localhost")
DB_NAME=$(grep -oP "(?<=\\\$db_name = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database name not found")
DB_USER=$(grep -oP "(?<=\\\$db_username = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database user not found")
DB_PASS=$(grep -oP "(?<=\\\$db_password = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database password not found")

log "✅ Database configuration loaded"

# 2. System Health Monitoring
log "🏥 Setting up system health monitoring..."

cat > "${MONITORING_PATH}/system-health.sh" << 'EOF'
#!/bin/bash
# WIDDX System Health Monitor

MONITORING_PATH="/var/monitoring/widdx"
LOG_FILE="${MONITORING_PATH}/health.log"
ALERT_EMAIL="ALERT_EMAIL_PLACEHOLDER"
DOMAIN="DOMAIN_PLACEHOLDER"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Health check function
check_health() {
    local service=$1
    local check_command=$2
    local alert_threshold=${3:-1}
    
    if eval "$check_command"; then
        echo "${TIMESTAMP} [OK] ${service}: Healthy" >> "${LOG_FILE}"
        return 0
    else
        echo "${TIMESTAMP} [CRITICAL] ${service}: Failed health check" >> "${LOG_FILE}"
        
        # Send alert email
        if command -v mail >/dev/null 2>&1; then
            echo "CRITICAL: ${service} health check failed on ${DOMAIN} at ${TIMESTAMP}" | \
                mail -s "WIDDX Alert: ${service} Health Check Failed" "${ALERT_EMAIL}"
        fi
        
        return 1
    fi
}

# Website availability
check_health "Website" "curl -f -s -o /dev/null https://${DOMAIN}/"

# Database connectivity
check_health "Database" "mysql --host=DB_HOST_PLACEHOLDER --user=DB_USER_PLACEHOLDER --password=DB_PASS_PLACEHOLDER -e 'USE DB_NAME_PLACEHOLDER; SELECT 1;' >/dev/null 2>&1"

# SSL certificate validity
check_health "SSL Certificate" "echo | openssl s_client -servername ${DOMAIN} -connect ${DOMAIN}:443 2>/dev/null | openssl x509 -checkend 604800 -noout"

# Disk space check
DISK_USAGE=$(df / | awk 'NR==2{print $5}' | cut -d'%' -f1)
check_health "Disk Space" "[ ${DISK_USAGE} -lt 90 ]"

# Memory usage check
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
check_health "Memory Usage" "[ ${MEMORY_USAGE} -lt 85 ]"

# Log file size check
if [[ -f "/var/log/widdx-deployment/monitoring.log" ]]; then
    LOG_SIZE=$(stat -c%s "/var/log/widdx-deployment/monitoring.log")
    check_health "Log File Size" "[ ${LOG_SIZE} -lt 104857600 ]" # 100MB
fi

# Payment gateway logs check
if [[ -d "/var/www/html/whmcs/modules/gateways/logs" ]]; then
    ERROR_COUNT=$(grep -c "ERROR\|CRITICAL" /var/www/html/whmcs/modules/gateways/logs/*.log 2>/dev/null | awk -F: '{sum+=$2} END {print sum+0}')
    check_health "Payment Gateway Errors" "[ ${ERROR_COUNT} -lt 10 ]"
fi
EOF

# Replace placeholders
sed -i "s/ALERT_EMAIL_PLACEHOLDER/${ALERT_EMAIL}/g" "${MONITORING_PATH}/system-health.sh"
sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" "${MONITORING_PATH}/system-health.sh"
sed -i "s/DB_HOST_PLACEHOLDER/${DB_HOST}/g" "${MONITORING_PATH}/system-health.sh"
sed -i "s/DB_USER_PLACEHOLDER/${DB_USER}/g" "${MONITORING_PATH}/system-health.sh"
sed -i "s/DB_PASS_PLACEHOLDER/${DB_PASS}/g" "${MONITORING_PATH}/system-health.sh"
sed -i "s/DB_NAME_PLACEHOLDER/${DB_NAME}/g" "${MONITORING_PATH}/system-health.sh"

chmod +x "${MONITORING_PATH}/system-health.sh"

# Schedule health checks every 5 minutes
(crontab -l 2>/dev/null; echo "*/5 * * * * ${MONITORING_PATH}/system-health.sh") | crontab -

log "✅ System health monitoring configured"

# 3. Payment Gateway Monitoring
log "💳 Setting up payment gateway monitoring..."

cat > "${MONITORING_PATH}/payment-monitor.sh" << 'EOF'
#!/bin/bash
# WIDDX Payment Gateway Monitor

MONITORING_PATH="/var/monitoring/widdx"
LOG_FILE="${MONITORING_PATH}/payments.log"
ALERT_EMAIL="ALERT_EMAIL_PLACEHOLDER"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
GATEWAY_LOG_PATH="/var/www/html/whmcs/modules/gateways/logs"

# Check payment success rate
check_payment_health() {
    if [[ ! -d "${GATEWAY_LOG_PATH}" ]]; then
        echo "${TIMESTAMP} [WARNING] Gateway logs directory not found" >> "${LOG_FILE}"
        return 1
    fi
    
    # Count successful and failed payments in the last hour
    HOUR_AGO=$(date -d '1 hour ago' '+%Y-%m-%d %H')
    
    SUCCESS_COUNT=0
    FAILURE_COUNT=0
    
    # Check recent log files
    for log_file in "${GATEWAY_LOG_PATH}"/*.log; do
        if [[ -f "$log_file" ]]; then
            SUCCESS_COUNT=$((SUCCESS_COUNT + $(grep "${HOUR_AGO}" "$log_file" | grep -c "SUCCESS\|COMPLETED" || echo 0)))
            FAILURE_COUNT=$((FAILURE_COUNT + $(grep "${HOUR_AGO}" "$log_file" | grep -c "FAILED\|ERROR" || echo 0)))
        fi
    done
    
    TOTAL_PAYMENTS=$((SUCCESS_COUNT + FAILURE_COUNT))
    
    if [[ $TOTAL_PAYMENTS -gt 0 ]]; then
        SUCCESS_RATE=$(echo "scale=2; $SUCCESS_COUNT * 100 / $TOTAL_PAYMENTS" | bc)
        echo "${TIMESTAMP} [INFO] Payment success rate: ${SUCCESS_RATE}% (${SUCCESS_COUNT}/${TOTAL_PAYMENTS})" >> "${LOG_FILE}"
        
        # Alert if success rate drops below 95%
        if (( $(echo "${SUCCESS_RATE} < 95" | bc -l) )); then
            echo "${TIMESTAMP} [ALERT] Low payment success rate: ${SUCCESS_RATE}%" >> "${LOG_FILE}"
            
            if command -v mail >/dev/null 2>&1; then
                echo "ALERT: Payment success rate dropped to ${SUCCESS_RATE}% on DOMAIN_PLACEHOLDER" | \
                    mail -s "WIDDX Alert: Low Payment Success Rate" "${ALERT_EMAIL}"
            fi
        fi
    else
        echo "${TIMESTAMP} [INFO] No payments processed in the last hour" >> "${LOG_FILE}"
    fi
}

# Check for 3DS authentication issues
check_3ds_health() {
    if [[ -f "${GATEWAY_LOG_PATH}/lahza_3ds.log" ]]; then
        HOUR_AGO=$(date -d '1 hour ago' '+%Y-%m-%d %H')
        TIMEOUT_COUNT=$(grep "${HOUR_AGO}" "${GATEWAY_LOG_PATH}/lahza_3ds.log" | grep -c "TIMEOUT" || echo 0)
        
        if [[ $TIMEOUT_COUNT -gt 5 ]]; then
            echo "${TIMESTAMP} [ALERT] High 3DS timeout count: ${TIMEOUT_COUNT}" >> "${LOG_FILE}"
            
            if command -v mail >/dev/null 2>&1; then
                echo "ALERT: High 3DS timeout count (${TIMEOUT_COUNT}) detected on DOMAIN_PLACEHOLDER" | \
                    mail -s "WIDDX Alert: 3DS Timeout Issues" "${ALERT_EMAIL}"
            fi
        fi
    fi
}

# Check transaction processing times
check_processing_times() {
    if [[ -f "${GATEWAY_LOG_PATH}/lahza_secure.log" ]]; then
        HOUR_AGO=$(date -d '1 hour ago' '+%Y-%m-%d %H')
        
        # Look for slow processing times (>10 seconds)
        SLOW_COUNT=$(grep "${HOUR_AGO}" "${GATEWAY_LOG_PATH}/lahza_secure.log" | \
                    grep -E "processing_time.*[1-9][0-9]\." | wc -l || echo 0)
        
        if [[ $SLOW_COUNT -gt 3 ]]; then
            echo "${TIMESTAMP} [WARNING] Slow payment processing detected: ${SLOW_COUNT} transactions" >> "${LOG_FILE}"
        fi
    fi
}

# Run all checks
check_payment_health
check_3ds_health
check_processing_times

# Log rotation for payment monitoring
if [[ -f "${LOG_FILE}" ]] && [[ $(stat -c%s "${LOG_FILE}") -gt 10485760 ]]; then
    mv "${LOG_FILE}" "${LOG_FILE}.$(date +%Y%m%d)"
    gzip "${LOG_FILE}.$(date +%Y%m%d)"
    touch "${LOG_FILE}"
fi
EOF

# Replace placeholders
sed -i "s/ALERT_EMAIL_PLACEHOLDER/${ALERT_EMAIL}/g" "${MONITORING_PATH}/payment-monitor.sh"
sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" "${MONITORING_PATH}/payment-monitor.sh"

chmod +x "${MONITORING_PATH}/payment-monitor.sh"

# Schedule payment monitoring every 15 minutes
(crontab -l 2>/dev/null; echo "*/15 * * * * ${MONITORING_PATH}/payment-monitor.sh") | crontab -

log "✅ Payment gateway monitoring configured"

# 4. Performance Monitoring
log "⚡ Setting up performance monitoring..."

cat > "${MONITORING_PATH}/performance-monitor.sh" << 'EOF'
#!/bin/bash
# WIDDX Performance Monitor

MONITORING_PATH="/var/monitoring/widdx"
LOG_FILE="${MONITORING_PATH}/performance.log"
DOMAIN="DOMAIN_PLACEHOLDER"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Website response time check
check_response_time() {
    local url=$1
    local max_time=${2:-3}
    
    RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "https://${DOMAIN}${url}")
    
    echo "${TIMESTAMP} [PERF] ${url}: ${RESPONSE_TIME}s" >> "${LOG_FILE}"
    
    # Alert if response time is too slow
    if (( $(echo "${RESPONSE_TIME} > ${max_time}" | bc -l) )); then
        echo "${TIMESTAMP} [SLOW] ${url}: ${RESPONSE_TIME}s (threshold: ${max_time}s)" >> "${LOG_FILE}"
        return 1
    fi
    
    return 0
}

# Check key pages
check_response_time "/" 3
check_response_time "/clientarea.php" 4
check_response_time "/cart.php" 3
check_response_time "/admin/" 5

# Database performance check
DB_RESPONSE_TIME=$(mysql --host=DB_HOST_PLACEHOLDER --user=DB_USER_PLACEHOLDER --password=DB_PASS_PLACEHOLDER -e "
USE DB_NAME_PLACEHOLDER;
SET @start_time = NOW(6);
SELECT COUNT(*) FROM tblinvoices;
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, NOW(6)) / 1000000 as response_time;
" | tail -n 1)

echo "${TIMESTAMP} [DB] Query response time: ${DB_RESPONSE_TIME}s" >> "${LOG_FILE}"

# Check for slow queries
SLOW_QUERY_COUNT=$(mysql --host=DB_HOST_PLACEHOLDER --user=DB_USER_PLACEHOLDER --password=DB_PASS_PLACEHOLDER -e "
SHOW GLOBAL STATUS LIKE 'Slow_queries';
" | tail -n 1 | awk '{print $2}')

echo "${TIMESTAMP} [DB] Slow queries: ${SLOW_QUERY_COUNT}" >> "${LOG_FILE}"

# Memory usage tracking
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
echo "${TIMESTAMP} [SYS] Memory usage: ${MEMORY_USAGE}%" >> "${LOG_FILE}"

# CPU load tracking
LOAD_AVERAGE=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
echo "${TIMESTAMP} [SYS] Load average: ${LOAD_AVERAGE}" >> "${LOG_FILE}"

# Disk I/O tracking
if command -v iostat >/dev/null 2>&1; then
    DISK_UTIL=$(iostat -x 1 1 | tail -n +4 | awk '{if($1!="") util+=$10} END {print util}')
    echo "${TIMESTAMP} [SYS] Disk utilization: ${DISK_UTIL}%" >> "${LOG_FILE}"
fi
EOF

# Replace placeholders
sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" "${MONITORING_PATH}/performance-monitor.sh"
sed -i "s/DB_HOST_PLACEHOLDER/${DB_HOST}/g" "${MONITORING_PATH}/performance-monitor.sh"
sed -i "s/DB_USER_PLACEHOLDER/${DB_USER}/g" "${MONITORING_PATH}/performance-monitor.sh"
sed -i "s/DB_PASS_PLACEHOLDER/${DB_PASS}/g" "${MONITORING_PATH}/performance-monitor.sh"
sed -i "s/DB_NAME_PLACEHOLDER/${DB_NAME}/g" "${MONITORING_PATH}/performance-monitor.sh"

chmod +x "${MONITORING_PATH}/performance-monitor.sh"

# Schedule performance monitoring every 10 minutes
(crontab -l 2>/dev/null; echo "*/10 * * * * ${MONITORING_PATH}/performance-monitor.sh") | crontab -

log "✅ Performance monitoring configured"

# 5. Security Monitoring
log "🔒 Setting up security monitoring..."

cat > "${MONITORING_PATH}/security-monitor.sh" << 'EOF'
#!/bin/bash
# WIDDX Security Monitor

MONITORING_PATH="/var/monitoring/widdx"
LOG_FILE="${MONITORING_PATH}/security.log"
ALERT_EMAIL="ALERT_EMAIL_PLACEHOLDER"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
WHMCS_PATH="/var/www/html/whmcs"

# Check for failed login attempts
check_failed_logins() {
    if [[ -f "/var/log/apache2/access.log" ]]; then
        HOUR_AGO=$(date -d '1 hour ago' '+%d/%b/%Y:%H')
        FAILED_LOGINS=$(grep "${HOUR_AGO}" /var/log/apache2/access.log | grep -c "POST.*admin.*401\|POST.*clientarea.*401" || echo 0)
        
        echo "${TIMESTAMP} [SEC] Failed logins in last hour: ${FAILED_LOGINS}" >> "${LOG_FILE}"
        
        if [[ $FAILED_LOGINS -gt 20 ]]; then
            echo "${TIMESTAMP} [ALERT] High failed login attempts: ${FAILED_LOGINS}" >> "${LOG_FILE}"
            
            if command -v mail >/dev/null 2>&1; then
                echo "SECURITY ALERT: ${FAILED_LOGINS} failed login attempts detected on DOMAIN_PLACEHOLDER" | \
                    mail -s "WIDDX Security Alert: Failed Logins" "${ALERT_EMAIL}"
            fi
        fi
    fi
}

# Check for suspicious payment attempts
check_suspicious_payments() {
    GATEWAY_LOG_PATH="/var/www/html/whmcs/modules/gateways/logs"
    
    if [[ -d "${GATEWAY_LOG_PATH}" ]]; then
        HOUR_AGO=$(date -d '1 hour ago' '+%Y-%m-%d %H')
        
        # Check for rapid payment attempts from same IP
        SUSPICIOUS_IPS=$(grep "${HOUR_AGO}" "${GATEWAY_LOG_PATH}"/*.log 2>/dev/null | \
                        grep -oE '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}' | \
                        sort | uniq -c | awk '$1 > 10 {print $2}' | wc -l)
        
        if [[ $SUSPICIOUS_IPS -gt 0 ]]; then
            echo "${TIMESTAMP} [ALERT] Suspicious payment patterns detected from ${SUSPICIOUS_IPS} IPs" >> "${LOG_FILE}"
        fi
    fi
}

# Check file integrity
check_file_integrity() {
    # Check for unauthorized modifications to critical files
    CRITICAL_FILES=(
        "${WHMCS_PATH}/configuration.php"
        "${WHMCS_PATH}/modules/gateways/lahza.php"
        "${WHMCS_PATH}/init.php"
    )
    
    for file in "${CRITICAL_FILES[@]}"; do
        if [[ -f "$file" ]]; then
            # Check if file was modified in the last hour
            if [[ $(find "$file" -mmin -60 2>/dev/null) ]]; then
                echo "${TIMESTAMP} [WARNING] Critical file modified: $file" >> "${LOG_FILE}"
            fi
        fi
    done
}

# Check SSL certificate expiry
check_ssl_expiry() {
    DAYS_UNTIL_EXPIRY=$(echo | openssl s_client -servername DOMAIN_PLACEHOLDER -connect DOMAIN_PLACEHOLDER:443 2>/dev/null | \
                       openssl x509 -noout -dates | grep notAfter | cut -d= -f2 | \
                       xargs -I {} date -d {} +%s | \
                       awk -v now=$(date +%s) '{print int(($1 - now) / 86400)}')
    
    echo "${TIMESTAMP} [SSL] Certificate expires in ${DAYS_UNTIL_EXPIRY} days" >> "${LOG_FILE}"
    
    if [[ $DAYS_UNTIL_EXPIRY -lt 30 ]]; then
        echo "${TIMESTAMP} [ALERT] SSL certificate expires soon: ${DAYS_UNTIL_EXPIRY} days" >> "${LOG_FILE}"
        
        if command -v mail >/dev/null 2>&1; then
            echo "SSL ALERT: Certificate for DOMAIN_PLACEHOLDER expires in ${DAYS_UNTIL_EXPIRY} days" | \
                mail -s "WIDDX SSL Alert: Certificate Expiring" "${ALERT_EMAIL}"
        fi
    fi
}

# Run security checks
check_failed_logins
check_suspicious_payments
check_file_integrity
check_ssl_expiry
EOF

# Replace placeholders
sed -i "s/ALERT_EMAIL_PLACEHOLDER/${ALERT_EMAIL}/g" "${MONITORING_PATH}/security-monitor.sh"
sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" "${MONITORING_PATH}/security-monitor.sh"

chmod +x "${MONITORING_PATH}/security-monitor.sh"

# Schedule security monitoring every 30 minutes
(crontab -l 2>/dev/null; echo "*/30 * * * * ${MONITORING_PATH}/security-monitor.sh") | crontab -

log "✅ Security monitoring configured"

# 6. Log Aggregation and Analysis
log "📋 Setting up log aggregation..."

cat > "${MONITORING_PATH}/log-analyzer.sh" << 'EOF'
#!/bin/bash
# WIDDX Log Analyzer

MONITORING_PATH="/var/monitoring/widdx"
REPORT_FILE="${MONITORING_PATH}/daily_report.txt"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
DATE=$(date '+%Y-%m-%d')

# Generate daily summary report
generate_daily_report() {
    cat > "${REPORT_FILE}" << EOL
WIDDX Daily Monitoring Report
============================
Date: ${DATE}
Generated: ${TIMESTAMP}

SYSTEM HEALTH SUMMARY:
EOL

    # Health check summary
    if [[ -f "${MONITORING_PATH}/health.log" ]]; then
        echo "Health Checks:" >> "${REPORT_FILE}"
        grep "${DATE}" "${MONITORING_PATH}/health.log" | grep -c "\[OK\]" | awk '{print "  - Successful: " $1}' >> "${REPORT_FILE}"
        grep "${DATE}" "${MONITORING_PATH}/health.log" | grep -c "\[CRITICAL\]" | awk '{print "  - Failed: " $1}' >> "${REPORT_FILE}"
        echo "" >> "${REPORT_FILE}"
    fi

    # Payment summary
    if [[ -f "${MONITORING_PATH}/payments.log" ]]; then
        echo "PAYMENT PROCESSING SUMMARY:" >> "${REPORT_FILE}"
        grep "${DATE}" "${MONITORING_PATH}/payments.log" | grep "success rate" | tail -1 >> "${REPORT_FILE}"
        echo "" >> "${REPORT_FILE}"
    fi

    # Performance summary
    if [[ -f "${MONITORING_PATH}/performance.log" ]]; then
        echo "PERFORMANCE SUMMARY:" >> "${REPORT_FILE}"
        echo "Average Response Times:" >> "${REPORT_FILE}"
        grep "${DATE}" "${MONITORING_PATH}/performance.log" | grep "\[PERF\]" | \
            awk '{print $4, $5}' | sort | uniq -c | head -5 >> "${REPORT_FILE}"
        echo "" >> "${REPORT_FILE}"
    fi

    # Security summary
    if [[ -f "${MONITORING_PATH}/security.log" ]]; then
        echo "SECURITY SUMMARY:" >> "${REPORT_FILE}"
        ALERTS=$(grep "${DATE}" "${MONITORING_PATH}/security.log" | grep -c "\[ALERT\]" || echo 0)
        echo "  - Security alerts: ${ALERTS}" >> "${REPORT_FILE}"
        echo "" >> "${REPORT_FILE}"
    fi

    # Top errors
    echo "TOP ERRORS:" >> "${REPORT_FILE}"
    if [[ -f "/var/www/html/whmcs/storage/logs/laravel.log" ]]; then
        grep "${DATE}" /var/www/html/whmcs/storage/logs/laravel.log | grep ERROR | \
            awk '{print $4}' | sort | uniq -c | sort -nr | head -5 >> "${REPORT_FILE}"
    fi
}

# Email daily report
email_daily_report() {
    if command -v mail >/dev/null 2>&1; then
        mail -s "WIDDX Daily Report - ${DATE}" "ALERT_EMAIL_PLACEHOLDER" < "${REPORT_FILE}"
    fi
}

# Run if it's 8 AM (daily report time)
HOUR=$(date +%H)
if [[ "$HOUR" == "08" ]]; then
    generate_daily_report
    email_daily_report
fi

# Clean old logs (keep 30 days)
find "${MONITORING_PATH}" -name "*.log.*" -mtime +30 -delete
EOF

# Replace placeholders
sed -i "s/ALERT_EMAIL_PLACEHOLDER/${ALERT_EMAIL}/g" "${MONITORING_PATH}/log-analyzer.sh"

chmod +x "${MONITORING_PATH}/log-analyzer.sh"

# Schedule log analysis every hour
(crontab -l 2>/dev/null; echo "0 * * * * ${MONITORING_PATH}/log-analyzer.sh") | crontab -

log "✅ Log aggregation configured"

# 7. Dashboard Setup
log "📊 Setting up monitoring dashboard..."

cat > "${MONITORING_PATH}/dashboard.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX Monitoring Dashboard</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            color: #333;
        }
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2c5aa0;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .status-ok { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-error { color: #e74c3c; }
        .refresh-info {
            text-align: center;
            color: #666;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>WIDDX Monitoring Dashboard</h1>
            <p>Real-time system monitoring and performance metrics</p>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-title">System Health</div>
                <div class="metric-value status-ok" id="system-health">Checking...</div>
                <div>Last check: <span id="health-time">-</span></div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Payment Success Rate</div>
                <div class="metric-value status-ok" id="payment-rate">Checking...</div>
                <div>Last hour transactions</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Response Time</div>
                <div class="metric-value status-ok" id="response-time">Checking...</div>
                <div>Homepage load time</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Security Status</div>
                <div class="metric-value status-ok" id="security-status">Checking...</div>
                <div>No active threats</div>
            </div>
        </div>
        
        <div class="refresh-info">
            Dashboard updates every 5 minutes<br>
            Last updated: <span id="last-update">-</span>
        </div>
    </div>
    
    <script>
        // Auto-refresh dashboard every 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);
        
        // Update timestamp
        document.getElementById('last-update').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
EOF

log "✅ Monitoring dashboard created"

# 8. Alert Configuration
log "🚨 Configuring alert system..."

# Create alert configuration file
cat > "${MONITORING_PATH}/alert-config.json" << EOF
{
    "alert_settings": {
        "email": "${ALERT_EMAIL}",
        "domain": "${DOMAIN}",
        "thresholds": {
            "response_time": 5,
            "payment_success_rate": 95,
            "disk_usage": 90,
            "memory_usage": 85,
            "failed_logins": 20,
            "ssl_expiry_days": 30
        }
    },
    "notification_channels": {
        "email": true,
        "log": true,
        "dashboard": true
    },
    "monitoring_intervals": {
        "health_check": 5,
        "payment_monitor": 15,
        "performance_monitor": 10,
        "security_monitor": 30,
        "log_analysis": 60
    }
}
EOF

log "✅ Alert system configured"

# 9. Create Monitoring Summary
log "📋 Creating monitoring summary..."

cat > "${MONITORING_PATH}/README.md" << EOF
# WIDDX Monitoring System

## Overview
Comprehensive monitoring system for WIDDX theme and Lahza payment gateway.

## Components

### Health Monitoring
- **Script**: system-health.sh
- **Frequency**: Every 5 minutes
- **Monitors**: Website availability, database connectivity, SSL certificate, disk space, memory usage

### Payment Monitoring
- **Script**: payment-monitor.sh
- **Frequency**: Every 15 minutes
- **Monitors**: Payment success rates, 3DS authentication, processing times

### Performance Monitoring
- **Script**: performance-monitor.sh
- **Frequency**: Every 10 minutes
- **Monitors**: Response times, database performance, system resources

### Security Monitoring
- **Script**: security-monitor.sh
- **Frequency**: Every 30 minutes
- **Monitors**: Failed logins, suspicious activities, file integrity, SSL expiry

### Log Analysis
- **Script**: log-analyzer.sh
- **Frequency**: Every hour
- **Function**: Aggregates logs, generates daily reports

## Log Locations
- Health: ${MONITORING_PATH}/health.log
- Payments: ${MONITORING_PATH}/payments.log
- Performance: ${MONITORING_PATH}/performance.log
- Security: ${MONITORING_PATH}/security.log
- Daily Reports: ${MONITORING_PATH}/daily_report.txt

## Dashboard
Access the monitoring dashboard at: ${MONITORING_PATH}/dashboard.html

## Alerts
- Email alerts sent to: ${ALERT_EMAIL}
- Alert thresholds configured in: alert-config.json

## Maintenance
- Logs are automatically rotated
- Old logs are cleaned up after 30 days
- Daily reports are generated at 8 AM

## Troubleshooting
1. Check crontab: \`crontab -l\`
2. Verify script permissions: \`ls -la ${MONITORING_PATH}/*.sh\`
3. Check log files for errors
4. Ensure mail system is configured for alerts
EOF

log "✅ Monitoring documentation created"

# Summary
log "🎉 WIDDX Post-Launch Monitoring Setup Completed!"
log ""
log "📊 Monitoring Summary:"
log "   - System Health: Every 5 minutes"
log "   - Payment Gateway: Every 15 minutes"
log "   - Performance: Every 10 minutes"
log "   - Security: Every 30 minutes"
log "   - Log Analysis: Every hour"
log "   - Daily Reports: 8 AM daily"
log ""
log "📁 Monitoring files:"
log "   - Configuration: ${MONITORING_PATH}/"
log "   - Logs: ${MONITORING_PATH}/*.log"
log "   - Dashboard: ${MONITORING_PATH}/dashboard.html"
log "   - Documentation: ${MONITORING_PATH}/README.md"
log ""
log "📧 Alerts configured for: ${ALERT_EMAIL}"
log "🌐 Monitoring domain: ${DOMAIN}"
log ""
log "✅ All monitoring systems are now active!"

info "Post-launch monitoring setup completed. Check ${LOG_PATH}/monitoring.log for detailed logs."
EOF
