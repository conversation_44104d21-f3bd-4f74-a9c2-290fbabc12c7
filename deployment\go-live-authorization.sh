#!/bin/bash
#
# WIDDX Go-Live Authorization Script
# Final authorization and traffic enablement for production launch
#
# @package    WIDDX Production Finalization
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration
DEPLOYMENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_PATH="/var/log/widdx-deployment"
DOMAIN="yourdomain.com"
ADMIN_EMAIL="<EMAIL>"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# Create log directory
mkdir -p "${LOG_PATH}"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/go-live.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/go-live.log"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/go-live.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/go-live.log"
}

# Function to display go-live banner
show_go_live_banner() {
    echo ""
    echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║                        🚀 WIDDX GO-LIVE AUTHORIZATION 🚀                    ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║                     Final Authorization for Production Launch               ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  This script will:                                                          ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Verify all finalization steps are complete                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Run final system validation                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Authorize live traffic                                                   ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Activate post-launch monitoring                                          ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Schedule 24-hour review                                                  ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  🌐 Ready to serve real customers!                                          ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Function to run finalization scripts
run_finalization_script() {
    local script_name="$1"
    local description="$2"
    
    echo ""
    echo -e "${PURPLE}▶ Running ${description}...${NC}"
    
    if [[ ! -f "${DEPLOYMENT_DIR}/${script_name}" ]]; then
        error "Finalization script not found: ${script_name}"
    fi
    
    chmod +x "${DEPLOYMENT_DIR}/${script_name}"
    
    if "${DEPLOYMENT_DIR}/${script_name}"; then
        echo -e "${GREEN}✅ ${description} completed successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ ${description} failed${NC}"
        return 1
    fi
}

# Function to verify system readiness
verify_system_readiness() {
    local readiness_score=0
    local max_score=5
    
    echo ""
    echo -e "${BLUE}🔍 Verifying System Readiness...${NC}"
    echo ""
    
    # Check API configuration
    if mysql -e "USE whmcs; SELECT value FROM tblpaymentgateways WHERE gateway='lahza' AND setting='testMode';" | grep -q 'off'; then
        echo -e "${GREEN}✅ Production API configured${NC}"
        readiness_score=$((readiness_score + 1))
    else
        echo -e "${RED}❌ Production API not configured${NC}"
    fi
    
    # Check payment testing
    if [[ -f "${LOG_PATH}/live_payment_test_"*".txt" ]]; then
        echo -e "${GREEN}✅ Live payment testing completed${NC}"
        readiness_score=$((readiness_score + 1))
    else
        echo -e "${RED}❌ Live payment testing not completed${NC}"
    fi
    
    # Check monitoring
    if [[ -d "/var/monitoring/widdx" ]] && [[ -x "/var/monitoring/widdx/system-health.sh" ]]; then
        echo -e "${GREEN}✅ Monitoring systems active${NC}"
        readiness_score=$((readiness_score + 1))
    else
        echo -e "${RED}❌ Monitoring systems not active${NC}"
    fi
    
    # Check smoke tests
    if [[ -f "${LOG_PATH}/final_smoke_test_"*".txt" ]]; then
        echo -e "${GREEN}✅ Final smoke tests completed${NC}"
        readiness_score=$((readiness_score + 1))
    else
        echo -e "${RED}❌ Final smoke tests not completed${NC}"
    fi
    
    # Check website accessibility
    if curl -f -s -o /dev/null "https://${DOMAIN}/"; then
        echo -e "${GREEN}✅ Website accessible${NC}"
        readiness_score=$((readiness_score + 1))
    else
        echo -e "${RED}❌ Website not accessible${NC}"
    fi
    
    echo ""
    echo "Readiness Score: ${readiness_score}/${max_score}"
    
    if [[ $readiness_score -eq $max_score ]]; then
        return 0
    else
        return 1
    fi
}

# Function to show final checklist
show_final_checklist() {
    echo ""
    echo -e "${YELLOW}📋 FINAL GO-LIVE CHECKLIST${NC}"
    echo -e "${YELLOW}═══════════════════════════${NC}"
    echo ""
    
    local checklist_items=(
        "Production API credentials configured"
        "Live payment testing completed successfully"
        "Monitoring systems validated and active"
        "Final smoke tests passed"
        "Security measures verified"
        "Performance benchmarks met"
        "Backup systems operational"
        "Support team notified and ready"
    )
    
    for item in "${checklist_items[@]}"; do
        echo -e "${GREEN}✅${NC} ${item}"
    done
    
    echo ""
}

# Function to activate post-launch monitoring
activate_post_launch_monitoring() {
    echo ""
    echo -e "${BLUE}📊 Activating Post-Launch Monitoring...${NC}"
    
    # Create post-launch monitoring script
    cat > "/var/monitoring/widdx/post-launch-monitor.sh" << 'EOF'
#!/bin/bash
# WIDDX Post-Launch Monitoring Script

MONITORING_PATH="/var/monitoring/widdx"
LOG_FILE="${MONITORING_PATH}/post-launch.log"
ALERT_EMAIL="ALERT_EMAIL_PLACEHOLDER"
DOMAIN="DOMAIN_PLACEHOLDER"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Enhanced monitoring for first 24 hours
echo "${TIMESTAMP} [POST-LAUNCH] Starting enhanced monitoring" >> "${LOG_FILE}"

# Check payment processing every 5 minutes
PAYMENT_ERRORS=$(grep -c "ERROR\|FAILED" /var/www/html/whmcs/modules/gateways/logs/*.log 2>/dev/null || echo 0)
if [[ $PAYMENT_ERRORS -gt 5 ]]; then
    echo "${TIMESTAMP} [ALERT] High payment error rate: ${PAYMENT_ERRORS}" >> "${LOG_FILE}"
    if command -v mail >/dev/null 2>&1; then
        echo "POST-LAUNCH ALERT: High payment error rate (${PAYMENT_ERRORS}) detected on ${DOMAIN}" | \
            mail -s "WIDDX Post-Launch Alert: Payment Errors" "${ALERT_EMAIL}"
    fi
fi

# Check website response time
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "https://${DOMAIN}/")
if (( $(echo "${RESPONSE_TIME} > 5" | bc -l) )); then
    echo "${TIMESTAMP} [ALERT] Slow response time: ${RESPONSE_TIME}s" >> "${LOG_FILE}"
    if command -v mail >/dev/null 2>&1; then
        echo "POST-LAUNCH ALERT: Slow response time (${RESPONSE_TIME}s) detected on ${DOMAIN}" | \
            mail -s "WIDDX Post-Launch Alert: Performance" "${ALERT_EMAIL}"
    fi
fi

# Check for 5xx errors
ERROR_5XX=$(grep -c " 5[0-9][0-9] " /var/log/apache2/access.log 2>/dev/null | tail -1 || echo 0)
if [[ $ERROR_5XX -gt 10 ]]; then
    echo "${TIMESTAMP} [ALERT] High 5xx error rate: ${ERROR_5XX}" >> "${LOG_FILE}"
    if command -v mail >/dev/null 2>&1; then
        echo "POST-LAUNCH ALERT: High 5xx error rate (${ERROR_5XX}) detected on ${DOMAIN}" | \
            mail -s "WIDDX Post-Launch Alert: Server Errors" "${ALERT_EMAIL}"
    fi
fi

echo "${TIMESTAMP} [POST-LAUNCH] Monitoring check completed" >> "${LOG_FILE}"
EOF

    # Replace placeholders
    sed -i "s/ALERT_EMAIL_PLACEHOLDER/${ADMIN_EMAIL}/g" "/var/monitoring/widdx/post-launch-monitor.sh"
    sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" "/var/monitoring/widdx/post-launch-monitor.sh"
    
    chmod +x "/var/monitoring/widdx/post-launch-monitor.sh"
    
    # Schedule enhanced monitoring for first 24 hours
    (crontab -l 2>/dev/null; echo "*/5 * * * * /var/monitoring/widdx/post-launch-monitor.sh") | crontab -
    
    echo -e "${GREEN}✅ Post-launch monitoring activated${NC}"
}

# Function to schedule 24-hour review
schedule_24_hour_review() {
    echo ""
    echo -e "${BLUE}📅 Scheduling 24-Hour Review...${NC}"
    
    # Create 24-hour review script
    cat > "/var/monitoring/widdx/24-hour-review.sh" << 'EOF'
#!/bin/bash
# WIDDX 24-Hour Post-Launch Review

MONITORING_PATH="/var/monitoring/widdx"
REPORT_FILE="${MONITORING_PATH}/24_hour_review_$(date +%Y%m%d).txt"
ALERT_EMAIL="ALERT_EMAIL_PLACEHOLDER"
DOMAIN="DOMAIN_PLACEHOLDER"

# Generate 24-hour review report
cat > "${REPORT_FILE}" << EOL
WIDDX 24-Hour Post-Launch Review
===============================
Generated: $(date)
Domain: ${DOMAIN}

SYSTEM PERFORMANCE SUMMARY:
$(tail -100 ${MONITORING_PATH}/performance.log | grep "$(date +%Y-%m-%d)" | tail -10)

PAYMENT PROCESSING SUMMARY:
$(tail -100 ${MONITORING_PATH}/payments.log | grep "$(date +%Y-%m-%d)" | tail -10)

SECURITY EVENTS:
$(tail -100 ${MONITORING_PATH}/security.log | grep "$(date +%Y-%m-%d)" | tail -10)

POST-LAUNCH ALERTS:
$(grep "ALERT" ${MONITORING_PATH}/post-launch.log 2>/dev/null || echo "No alerts in the last 24 hours")

RECOMMENDATIONS:
1. Review payment success rates and address any issues
2. Monitor website performance and optimize if needed
3. Check security logs for any suspicious activity
4. Plan regular maintenance and updates
5. Consider scaling resources based on traffic patterns

Next Review: $(date -d '+1 week')
EOL

# Email the review
if command -v mail >/dev/null 2>&1; then
    mail -s "WIDDX 24-Hour Post-Launch Review" "${ALERT_EMAIL}" < "${REPORT_FILE}"
fi

# Disable enhanced monitoring after 24 hours
crontab -l | grep -v "post-launch-monitor.sh" | crontab -

echo "$(date): 24-hour review completed and enhanced monitoring disabled" >> "${MONITORING_PATH}/post-launch.log"
EOF

    # Replace placeholders
    sed -i "s/ALERT_EMAIL_PLACEHOLDER/${ADMIN_EMAIL}/g" "/var/monitoring/widdx/24-hour-review.sh"
    sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" "/var/monitoring/widdx/24-hour-review.sh"
    
    chmod +x "/var/monitoring/widdx/24-hour-review.sh"
    
    # Schedule 24-hour review
    REVIEW_TIME=$(date -d '+24 hours' '+%M %H %d %m *')
    (crontab -l 2>/dev/null; echo "${REVIEW_TIME} /var/monitoring/widdx/24-hour-review.sh") | crontab -
    
    echo -e "${GREEN}✅ 24-hour review scheduled for $(date -d '+24 hours')${NC}"
}

# Function to generate go-live certificate
generate_go_live_certificate() {
    cat > "${LOG_PATH}/go_live_certificate_${TIMESTAMP}.txt" << EOF
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                        🏆 WIDDX GO-LIVE CERTIFICATE 🏆                       ║
║                                                                              ║
║  This certifies that the WIDDX theme and Lahza payment gateway system       ║
║  has successfully completed all production requirements and is authorized    ║
║  for live customer traffic.                                                  ║
║                                                                              ║
║  System Details:                                                             ║
║  • Domain: ${DOMAIN}                                                ║
║  • Go-Live Date: $(date)                                    ║
║  • Authorization ID: ${TIMESTAMP}                                   ║
║                                                                              ║
║  Components Certified:                                                       ║
║  ✅ WIDDX Theme: Modern, accessible, responsive design                       ║
║  ✅ Lahza Payment Gateway: Secure payment processing with 3D Secure         ║
║  ✅ WIDDX Modern Order Form: Enhanced user experience                        ║
║  ✅ Monitoring System: Comprehensive system monitoring                       ║
║  ✅ Security Features: Enterprise-grade security implementation              ║
║                                                                              ║
║  Quality Assurance:                                                          ║
║  • All production tests passed                                               ║
║  • Security validation completed                                             ║
║  • Performance benchmarks met                                                ║
║  • Accessibility compliance verified (WCAG 2.1 AA)                          ║
║  • Payment processing validated                                              ║
║                                                                              ║
║  Authorized by: WIDDX Development Team                                       ║
║  Certification Date: $(date)                                        ║
║                                                                              ║
║  🎉 Congratulations! Your system is ready to serve customers! 🎉            ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
}

# Main go-live function
main() {
    local start_time=$(date +%s)
    
    # Show banner
    show_go_live_banner
    
    log "🚀 Starting WIDDX Go-Live Authorization Process"
    
    # Verify system readiness
    if ! verify_system_readiness; then
        echo ""
        echo -e "${RED}❌ System not ready for go-live. Please complete all finalization steps.${NC}"
        echo ""
        echo "Required steps:"
        echo "1. Run: ./configure-production-api.sh"
        echo "2. Run: ./live-payment-testing.sh"
        echo "3. Run: ./validate-monitoring.sh"
        echo "4. Run: ./final-smoke-test.sh"
        echo ""
        exit 1
    fi
    
    # Show final checklist
    show_final_checklist
    
    # Final confirmation
    echo ""
    echo -e "${BOLD}${YELLOW}⚠️  FINAL AUTHORIZATION REQUIRED ⚠️${NC}"
    echo ""
    echo "This will authorize the WIDDX system for live customer traffic."
    echo "All finalization steps have been completed successfully."
    echo ""
    echo -n "Authorize go-live and open the floodgates? (yes/no): "
    read -r final_confirmation
    
    if [[ "$final_confirmation" != "yes" ]]; then
        echo ""
        echo "Go-live authorization cancelled."
        echo "Run this script again when ready to authorize live traffic."
        exit 0
    fi
    
    echo ""
    echo -e "${BOLD}${GREEN}🚀 GO-LIVE AUTHORIZED! 🚀${NC}"
    echo ""
    
    # Activate post-launch monitoring
    activate_post_launch_monitoring
    
    # Schedule 24-hour review
    schedule_24_hour_review
    
    # Generate go-live certificate
    generate_go_live_certificate
    
    # Send go-live notification
    if command -v mail >/dev/null 2>&1; then
        echo "WIDDX system has been authorized for live traffic and is now serving customers!" | \
            mail -s "🚀 WIDDX Go-Live Authorized - ${DOMAIN}" "${ADMIN_EMAIL}"
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Final success message
    echo ""
    echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║                    🎉 WIDDX SYSTEM IS NOW LIVE! 🎉                          ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  Your WIDDX-powered WHMCS system is now serving real customers!             ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  🌐 Website: https://${DOMAIN}                                      ║${NC}"
    echo -e "${BOLD}${CYAN}║  📊 Monitoring: Active and alerting                                         ║${NC}"
    echo -e "${BOLD}${CYAN}║  💳 Payments: Live processing enabled                                       ║${NC}"
    echo -e "${BOLD}${CYAN}║  🔒 Security: Enterprise-grade protection                                   ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  Next Steps:                                                                 ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Monitor payment processing closely                                       ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Review performance metrics                                               ║${NC}"
    echo -e "${BOLD}${CYAN}║  • Watch for alerts and notifications                                       ║${NC}"
    echo -e "${BOLD}${CYAN}║  • 24-hour review scheduled automatically                                   ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}║  🏆 Congratulations on your successful launch! 🏆                          ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                                              ║${NC}"
    echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    echo "📋 Important Information:"
    echo "   • Go-Live Certificate: ${LOG_PATH}/go_live_certificate_${TIMESTAMP}.txt"
    echo "   • Post-Launch Monitoring: Enhanced monitoring active for 24 hours"
    echo "   • 24-Hour Review: Scheduled for $(date -d '+24 hours')"
    echo "   • Support: Monitor logs and alerts for any issues"
    echo ""
    echo "🎯 Your WIDDX system is ready to scale and grow with your business!"
    
    log "🎉 WIDDX Go-Live Authorization completed successfully in ${duration} seconds"
}

# Run main function
main "$@"
EOF
