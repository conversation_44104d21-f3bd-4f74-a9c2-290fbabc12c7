#!/bin/bash
#
# WIDDX Data Migration and Backup Script
# Comprehensive backup and data migration for production deployment
#
# @package    WIDDX Production Deployment
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration
WHMCS_PATH="/var/www/html/whmcs"
BACKUP_PATH="/var/backups/whmcs"
MIGRATION_PATH="/var/backups/whmcs/migration"
LOG_PATH="/var/log/widdx-deployment"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Database configuration (will be read from WHMCS config)
DB_HOST=""
DB_NAME=""
DB_USER=""
DB_PASS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/migration.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/migration.log"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/migration.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/migration.log"
}

# Create directories
mkdir -p "${BACKUP_PATH}" "${MIGRATION_PATH}" "${LOG_PATH}"

log "🚀 Starting WIDDX Data Migration and Backup Process"

# 1. Read WHMCS Database Configuration
log "📖 Reading WHMCS database configuration..."

if [[ ! -f "${WHMCS_PATH}/configuration.php" ]]; then
    error "WHMCS configuration file not found at ${WHMCS_PATH}/configuration.php"
fi

# Extract database configuration
DB_HOST=$(grep -oP "(?<=\\\$db_host = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "localhost")
DB_NAME=$(grep -oP "(?<=\\\$db_name = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database name not found")
DB_USER=$(grep -oP "(?<=\\\$db_username = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database user not found")
DB_PASS=$(grep -oP "(?<=\\\$db_password = ')[^']*" "${WHMCS_PATH}/configuration.php" || error "Database password not found")

log "✅ Database configuration loaded: ${DB_NAME}@${DB_HOST}"

# 2. Pre-Migration System Backup
log "💾 Creating comprehensive system backup..."

# Database backup with compression
log "📊 Backing up database: ${DB_NAME}"
mysqldump \
    --host="${DB_HOST}" \
    --user="${DB_USER}" \
    --password="${DB_PASS}" \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --add-drop-table \
    --add-locks \
    --create-options \
    --disable-keys \
    --extended-insert \
    --quick \
    --set-charset \
    "${DB_NAME}" > "${BACKUP_PATH}/whmcs_pre_migration_${TIMESTAMP}.sql"

# Compress database backup
gzip "${BACKUP_PATH}/whmcs_pre_migration_${TIMESTAMP}.sql"
log "✅ Database backup completed: whmcs_pre_migration_${TIMESTAMP}.sql.gz"

# Files backup
log "📁 Backing up WHMCS files..."
tar -czf "${BACKUP_PATH}/whmcs_files_pre_migration_${TIMESTAMP}.tar.gz" \
    --exclude="${WHMCS_PATH}/storage/logs/*" \
    --exclude="${WHMCS_PATH}/downloads/temp/*" \
    --exclude="${WHMCS_PATH}/attachments/temp/*" \
    -C "$(dirname ${WHMCS_PATH})" "$(basename ${WHMCS_PATH})"

log "✅ Files backup completed: whmcs_files_pre_migration_${TIMESTAMP}.tar.gz"

# Configuration backup
log "⚙️ Backing up configuration files..."
mkdir -p "${MIGRATION_PATH}/config_backup"
cp "${WHMCS_PATH}/configuration.php" "${MIGRATION_PATH}/config_backup/"
cp -r "${WHMCS_PATH}/modules/gateways/" "${MIGRATION_PATH}/config_backup/" 2>/dev/null || true
cp -r "${WHMCS_PATH}/templates/" "${MIGRATION_PATH}/config_backup/" 2>/dev/null || true

log "✅ Configuration backup completed"

# 3. Data Integrity Verification
log "🔍 Verifying data integrity..."

# Check database connectivity
if ! mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "USE ${DB_NAME}; SELECT 1;" >/dev/null 2>&1; then
    error "Cannot connect to database for verification"
fi

# Verify critical tables exist
CRITICAL_TABLES=("tblclients" "tblinvoices" "tblorders" "tblproducts" "tblgatewaylog" "tbladmins")
for table in "${CRITICAL_TABLES[@]}"; do
    if ! mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "USE ${DB_NAME}; DESCRIBE ${table};" >/dev/null 2>&1; then
        error "Critical table missing: ${table}"
    fi
done

log "✅ Database integrity verified"

# Check file integrity
if [[ ! -f "${WHMCS_PATH}/init.php" ]]; then
    error "WHMCS core file missing: init.php"
fi

if [[ ! -d "${WHMCS_PATH}/includes" ]]; then
    error "WHMCS includes directory missing"
fi

log "✅ File system integrity verified"

# 4. Current System Analysis
log "📊 Analyzing current system state..."

# Database statistics
cat > "${MIGRATION_PATH}/pre_migration_stats.txt" << EOF
WIDDX Pre-Migration System Analysis
Generated: $(date)
=====================================

Database Statistics:
EOF

mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
SELECT 'Clients' as Table_Name, COUNT(*) as Record_Count FROM tblclients
UNION ALL
SELECT 'Invoices', COUNT(*) FROM tblinvoices
UNION ALL
SELECT 'Orders', COUNT(*) FROM tblorders
UNION ALL
SELECT 'Products', COUNT(*) FROM tblproducts
UNION ALL
SELECT 'Gateway Logs', COUNT(*) FROM tblgatewaylog
UNION ALL
SELECT 'Admins', COUNT(*) FROM tbladmins;
" >> "${MIGRATION_PATH}/pre_migration_stats.txt"

# File system statistics
echo "" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
echo "File System Statistics:" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
echo "WHMCS Directory Size: $(du -sh ${WHMCS_PATH} | cut -f1)" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
echo "Total Files: $(find ${WHMCS_PATH} -type f | wc -l)" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
echo "Total Directories: $(find ${WHMCS_PATH} -type d | wc -l)" >> "${MIGRATION_PATH}/pre_migration_stats.txt"

# Current theme information
echo "" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
echo "Current Configuration:" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
if [[ -f "${WHMCS_PATH}/configuration.php" ]]; then
    echo "Current Template: $(grep -oP "(?<=\\\$template = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "Not found")" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
    echo "System URL: $(grep -oP "(?<=\\\$systemurl = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "Not found")" >> "${MIGRATION_PATH}/pre_migration_stats.txt"
fi

log "✅ System analysis completed"

# 5. Gateway Configuration Backup
log "💳 Backing up payment gateway configurations..."

# Export current gateway settings
mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
SELECT * FROM tblpaymentgateways;
" > "${MIGRATION_PATH}/current_gateways.sql"

# Check for existing Lahza configuration
LAHZA_EXISTS=$(mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
SELECT COUNT(*) FROM tblpaymentgateways WHERE gateway = 'lahza';
" | tail -n 1)

if [[ "${LAHZA_EXISTS}" -gt 0 ]]; then
    warning "Existing Lahza gateway configuration found - will be preserved"
    mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
    USE ${DB_NAME};
    SELECT * FROM tblpaymentgateways WHERE gateway = 'lahza';
    " > "${MIGRATION_PATH}/existing_lahza_config.sql"
else
    info "No existing Lahza configuration found - fresh installation"
fi

log "✅ Gateway configuration backup completed"

# 6. Theme Migration Preparation
log "🎨 Preparing theme migration..."

# Check current template
CURRENT_TEMPLATE=$(grep -oP "(?<=\\\$template = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "")
if [[ -n "${CURRENT_TEMPLATE}" ]]; then
    log "Current template: ${CURRENT_TEMPLATE}"
    
    # Backup current template customizations
    if [[ -d "${WHMCS_PATH}/templates/${CURRENT_TEMPLATE}" ]]; then
        tar -czf "${MIGRATION_PATH}/current_template_${CURRENT_TEMPLATE}_${TIMESTAMP}.tar.gz" \
            -C "${WHMCS_PATH}/templates" "${CURRENT_TEMPLATE}"
        log "✅ Current template backed up: ${CURRENT_TEMPLATE}"
    fi
else
    warning "Current template not detected in configuration"
fi

# Check for custom CSS/JS
if [[ -f "${WHMCS_PATH}/templates/${CURRENT_TEMPLATE}/css/custom.css" ]]; then
    cp "${WHMCS_PATH}/templates/${CURRENT_TEMPLATE}/css/custom.css" "${MIGRATION_PATH}/custom.css.backup"
    log "✅ Custom CSS backed up"
fi

if [[ -f "${WHMCS_PATH}/templates/${CURRENT_TEMPLATE}/js/custom.js" ]]; then
    cp "${WHMCS_PATH}/templates/${CURRENT_TEMPLATE}/js/custom.js" "${MIGRATION_PATH}/custom.js.backup"
    log "✅ Custom JavaScript backed up"
fi

# 7. Order Form Migration Preparation
log "🛒 Preparing order form migration..."

# Check current order form template
CURRENT_ORDER_FORM=$(mysql --host="${DB_HOST}" --user="${DB_USER}" --password="${DB_PASS}" -e "
USE ${DB_NAME};
SELECT value FROM tblconfiguration WHERE setting = 'OrderFormTemplate';
" | tail -n 1)

if [[ -n "${CURRENT_ORDER_FORM}" && "${CURRENT_ORDER_FORM}" != "NULL" ]]; then
    log "Current order form: ${CURRENT_ORDER_FORM}"
    
    # Backup current order form
    if [[ -d "${WHMCS_PATH}/templates/orderforms/${CURRENT_ORDER_FORM}" ]]; then
        tar -czf "${MIGRATION_PATH}/current_orderform_${CURRENT_ORDER_FORM}_${TIMESTAMP}.tar.gz" \
            -C "${WHMCS_PATH}/templates/orderforms" "${CURRENT_ORDER_FORM}"
        log "✅ Current order form backed up: ${CURRENT_ORDER_FORM}"
    fi
else
    info "No current order form template detected"
fi

# 8. Create Migration Manifest
log "📋 Creating migration manifest..."

cat > "${MIGRATION_PATH}/migration_manifest.json" << EOF
{
    "migration_info": {
        "timestamp": "${TIMESTAMP}",
        "whmcs_path": "${WHMCS_PATH}",
        "database": {
            "host": "${DB_HOST}",
            "name": "${DB_NAME}",
            "user": "${DB_USER}"
        }
    },
    "backups": {
        "database": "whmcs_pre_migration_${TIMESTAMP}.sql.gz",
        "files": "whmcs_files_pre_migration_${TIMESTAMP}.tar.gz",
        "config": "config_backup/",
        "current_template": "${CURRENT_TEMPLATE}",
        "current_order_form": "${CURRENT_ORDER_FORM}"
    },
    "system_state": {
        "pre_migration_stats": "pre_migration_stats.txt",
        "gateway_config": "current_gateways.sql",
        "lahza_exists": ${LAHZA_EXISTS}
    },
    "migration_plan": {
        "theme_deployment": "WIDDX theme installation",
        "gateway_deployment": "Lahza payment gateway setup",
        "order_form_deployment": "WIDDX Modern order form",
        "data_preservation": "All existing data preserved"
    }
}
EOF

log "✅ Migration manifest created"

# 9. Validation and Verification
log "✅ Running final validation..."

# Verify backup integrity
if [[ ! -f "${BACKUP_PATH}/whmcs_pre_migration_${TIMESTAMP}.sql.gz" ]]; then
    error "Database backup verification failed"
fi

if [[ ! -f "${BACKUP_PATH}/whmcs_files_pre_migration_${TIMESTAMP}.tar.gz" ]]; then
    error "Files backup verification failed"
fi

# Test backup restoration capability
log "🧪 Testing backup restoration capability..."
gunzip -t "${BACKUP_PATH}/whmcs_pre_migration_${TIMESTAMP}.sql.gz" || error "Database backup is corrupted"
tar -tzf "${BACKUP_PATH}/whmcs_files_pre_migration_${TIMESTAMP}.tar.gz" >/dev/null || error "Files backup is corrupted"

log "✅ Backup integrity verified"

# 10. Generate Migration Report
log "📊 Generating migration report..."

cat > "${MIGRATION_PATH}/migration_report.txt" << EOF
WIDDX Data Migration Report
==========================
Generated: $(date)
Migration ID: ${TIMESTAMP}

BACKUP STATUS: ✅ COMPLETED
- Database backup: $(ls -lh "${BACKUP_PATH}/whmcs_pre_migration_${TIMESTAMP}.sql.gz" | awk '{print $5}')
- Files backup: $(ls -lh "${BACKUP_PATH}/whmcs_files_pre_migration_${TIMESTAMP}.tar.gz" | awk '{print $5}')
- Configuration backup: ✅ Completed
- Custom files backup: ✅ Completed

SYSTEM ANALYSIS: ✅ COMPLETED
- Database connectivity: ✅ Verified
- Critical tables: ✅ All present
- File system integrity: ✅ Verified
- Current template: ${CURRENT_TEMPLATE}
- Current order form: ${CURRENT_ORDER_FORM}

MIGRATION READINESS: ✅ READY
- All backups completed successfully
- System state documented
- Migration plan prepared
- Rollback procedures available

NEXT STEPS:
1. Deploy WIDDX theme files
2. Configure Lahza payment gateway
3. Install WIDDX Modern order form
4. Update system configuration
5. Run post-deployment validation

ROLLBACK PLAN:
- Database: Restore from whmcs_pre_migration_${TIMESTAMP}.sql.gz
- Files: Restore from whmcs_files_pre_migration_${TIMESTAMP}.tar.gz
- Configuration: Restore from config_backup/
EOF

log "✅ Migration report generated"

# Summary
log "🎉 Data migration and backup process completed successfully!"
log ""
log "📋 Migration Summary:"
log "   - Migration ID: ${TIMESTAMP}"
log "   - Database backup: $(ls -lh "${BACKUP_PATH}/whmcs_pre_migration_${TIMESTAMP}.sql.gz" | awk '{print $5}')"
log "   - Files backup: $(ls -lh "${BACKUP_PATH}/whmcs_files_pre_migration_${TIMESTAMP}.tar.gz" | awk '{print $5}')"
log "   - Current template: ${CURRENT_TEMPLATE}"
log "   - Current order form: ${CURRENT_ORDER_FORM}"
log "   - Lahza gateway exists: $([ ${LAHZA_EXISTS} -gt 0 ] && echo "Yes" || echo "No")"
log ""
log "📁 Important files:"
log "   - Migration manifest: ${MIGRATION_PATH}/migration_manifest.json"
log "   - Migration report: ${MIGRATION_PATH}/migration_report.txt"
log "   - System stats: ${MIGRATION_PATH}/pre_migration_stats.txt"
log ""
log "✅ System is ready for WIDDX deployment!"

info "Data migration and backup completed. Check ${LOG_PATH}/migration.log for detailed logs."
EOF
