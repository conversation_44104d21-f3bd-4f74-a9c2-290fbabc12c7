/**
 * GSAP ANIMATION CONTROLLER
 * -------------------------
 * Coordinates all animation modules:
 * - Core utilities (core-animations.js)
 * - Hover effects (hover-animations.js)
 * - Scroll effects (scroll-animations.js)
 * - Page transitions (page-transitions.js)
 * - UI animations (ui-animations.js)
 * - Stats animations (stats-animations.js)
 * - FAQ animations (faq-animations.js)
 *
 * Usage:
 * 1. Auto-initializes when DOM is ready
 * 2. Provides cleanup via AnimationController.cleanup()
 * 3. Exports metrics via AnimationController.getMetrics()
 *
 * Note: Implementation details live in separate modules
 */

// Animation metrics for performance tracking
const animationMetrics = {
    animations: new Map(),
    init() {
        this.startTime = performance.now();
    },
    logAnimation(name) {
        this.animations.set(name, performance.now());
    },
    endAnimation(name) {
        if (this.animations.has(name)) {
            this.animations.delete(name);
        }
    },
    logError(error) {
        console.warn('Animation error:', error);
    },
    getSummary() {
        return {
            activeAnimations: this.animations.size,
            totalTime: performance.now() - (this.startTime || 0)
        };
    },
    cleanup() {
        this.animations.clear();
    },
    report() {
        // Silent reporting for performance
    }
};

// Global animation controller
const AnimationController = {
    // Initialize all animations
    init() {
        try {
            // Initialize core metrics first
            animationMetrics.init();
            
            // Initialize animation modules
            initHoverAnimations();
            initScrollAnimations();
            initUIAnimations();
            initStatsAnimations();
            initFAQAnimation();
            initHeroAnimations();
            
            // Handle page transitions if this is a SPA
            if (document.querySelector('.transition-overlay')) {
                initPageTransitions();
            }
            
            // Report animation metrics periodically
            setInterval(() => {
                animationMetrics.report();
            }, 10000);
            
            console.log('✅ GSAP animations initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing GSAP animations:', error);
            animationMetrics.logError(error);
        }
    },

    // Cleanup all animations
    cleanup() {
        // Kill all GSAP animations
        gsap.globalTimeline.clear();
        
        // Reset ScrollTriggers
        ScrollTrigger.getAll().forEach(st => st.kill());
        
        // Cleanup modules
        animationMetrics.cleanup();
    },

    // Get performance metrics
    getMetrics() {
        return animationMetrics.getSummary();
    }
};

// Auto-initialize when GSAP is available
if (typeof gsap !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        AnimationController.init();
    });
} else {
    // Wait for GSAP to load
    let gsapCheckInterval = setInterval(() => {
        if (typeof gsap !== 'undefined') {
            clearInterval(gsapCheckInterval);
            document.addEventListener('DOMContentLoaded', () => {
                AnimationController.init();
            });
        }
    }, 100);

    // Fallback timeout
    setTimeout(() => {
        clearInterval(gsapCheckInterval);
        console.warn('⚠️ GSAP failed to load within timeout');
    }, 10000);
}

// Cleanup before page unload
window.addEventListener('beforeunload', () => {
    AnimationController.cleanup();
});

// ===== OPTIMIZED PRELOADER ANIMATIONS =====
function initPreloaderAnimations() {
    const preloader = document.getElementById('preloader');
    if (!preloader) return;

    const tl = gsap.timeline();

    // Faster, simpler logo animation
    tl.from('.logo-animation h1', {
        duration: 0.6, // Reduced from 1
        y: 30, // Reduced from 50
        opacity: 0,
        ease: 'power2.out' // Simpler easing
    })
    .from('.loading-bar', {
        duration: 0.3, // Reduced from 0.5
        scaleX: 0,
        transformOrigin: 'left',
        ease: 'power2.out'
    }, '-=0.2'); // Reduced overlap

    // Faster preloader exit
    window.addEventListener('load', function() {
        setTimeout(() => {
            gsap.to('#preloader', {
                duration: 0.4, // Reduced from 0.8
                opacity: 0,
                scale: 0.95, // Subtle scale instead of y movement
                ease: 'power2.inOut',
                onComplete: function() {
                    preloader.style.display = 'none';
                    document.body.style.overflow = 'visible';
                    // Trigger custom event for other components
                    window.dispatchEvent(new CustomEvent('preloaderComplete'));
                }
            });
        }, 300); // Reduced from 800
    });
}

// ===== OPTIMIZED HERO SECTION ANIMATIONS =====
function initHeroAnimations() {
    // Check if elements exist before animating
    const titleLines = document.querySelectorAll('.title-line');
    const heroSubtitle = document.querySelector('.hero-subtitle');
    const heroButtons = document.querySelector('.hero-buttons');
    const robotContainer = document.querySelector('.robot-container');
    const floatingCards = document.querySelectorAll('.floating-card');

    if (titleLines.length === 0) return;

    const heroTl = gsap.timeline({ delay: 0.2 }); // Further reduced delay

    // Faster, simpler hero animations
    heroTl.from(titleLines, {
        duration: 0.8, // Reduced from 1
        y: 60, // Reduced from 100
        opacity: 0,
        stagger: 0.15, // Reduced from 0.2
        ease: 'power2.out' // Simpler easing
    });

    if (heroSubtitle) {
        heroTl.from(heroSubtitle, {
            duration: 0.6, // Reduced from 0.8
            y: 30, // Reduced from 50
            opacity: 0,
            ease: 'power2.out'
        }, '-=0.3'); // Reduced overlap
    }

    if (heroButtons) {
        heroTl.from(heroButtons, {
            duration: 0.6, // Reduced from 0.8
            y: 20, // Reduced from 30
            opacity: 0,
            ease: 'power2.out'
        }, '-=0.3');
    }

    if (robotContainer) {
        heroTl.from(robotContainer, {
            duration: 0.8, // Reduced from 1.2
            scale: 0.8, // Less dramatic scale
            opacity: 0,
            ease: 'power2.out' // Simpler easing
        }, '-=0.5');

        // Optimized floating animation for robot container
        const robotFloat = gsap.to(robotContainer, {
            duration: 8, // Slower for better performance
            y: '+=6', // Reduced movement
            repeat: -1,
            yoyo: true,
            ease: 'none', // Simpler easing
            paused: true
        });

        // Only animate when visible
        const robotObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    robotFloat.play();
                    animationMetrics.logAnimation('Robot float');
                } else {
                    robotFloat.pause();
                    animationMetrics.endAnimation('Robot float');
                }
            });
        }, { threshold: 0.1 });

        robotObserver.observe(robotContainer);
    }

    if (floatingCards.length > 0) {
        heroTl.from(floatingCards, {
            duration: 0.6, // Reduced from 1
            scale: 0.5, // Less dramatic scale
            opacity: 0,
            stagger: 0.08, // Reduced from 0.1
            ease: 'back.out(1.2)' // Less bouncy
        }, '-=0.4');

        // Optimized floating animation for cards
        const cardFloats = gsap.to(floatingCards, {
            duration: 5, // Slower for better performance
            y: '+=8', // Reduced movement
            rotation: '+=2', // Reduced rotation
            repeat: -1,
            yoyo: true,
            ease: 'none', // Simpler easing
            stagger: 0.2,
            paused: true
        });

        // Only animate when visible
        const cardsObserver = new IntersectionObserver((entries) => {
            const anyVisible = entries.some(entry => entry.isIntersecting);
            if (anyVisible) {
                cardFloats.play();
                animationMetrics.logAnimation('Cards float');
            } else {
                cardFloats.pause();
                animationMetrics.endAnimation('Cards float');
            }
        }, { threshold: 0.1 });

        floatingCards.forEach(card => cardsObserver.observe(card));
    }

    // Scroll indicator animation
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        gsap.from(scrollIndicator, {
            duration: 0.6, // Reduced from 1
            opacity: 0,
            y: 20, // Reduced from 30
            delay: 0.8, // Reduced from 1.5
            ease: 'power2.out'
        });
    }
}

// ===== SCROLL-TRIGGERED ANIMATIONS =====
const scrollTriggerManager = {
    maxInstances: 10,
    activeTriggers: [],
    addTrigger: function(trigger) {
        if (this.activeTriggers.length >= this.maxInstances) {
            // Clean up oldest inactive trigger
            const oldestInactive = this.activeTriggers.findIndex(t => !t.isActive);
            if (oldestInactive > -1) {
                this.activeTriggers[oldestInactive].kill();
                this.activeTriggers.splice(oldestInactive, 1);
            }
        }
        this.activeTriggers.push(trigger);
        animationMetrics.logAnimation(`ScrollTrigger: ${trigger.trigger?.id || trigger.trigger?.className || 'anonymous'}`);
    },
    cleanup: function() {
        this.activeTriggers.forEach(trigger => trigger.kill());
        this.activeTriggers = [];
    }
};

function initScrollAnimations() {
    // Clean up any existing triggers first
    scrollTriggerManager.cleanup();
    
    // Section headers animation
    gsap.utils.toArray('.section-header').forEach(header => {
        gsap.from(header.children, {
            duration: 1,
            y: 50,
            opacity: 0,
            stagger: 0.2,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: header,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse',
                onToggle: self => scrollTriggerManager.addTrigger(self),
                onUpdate: self => {
                    if (!self.isActive && self.progress > 0.9) {
                        self.kill();
                        scrollTriggerManager.activeTriggers =
                            scrollTriggerManager.activeTriggers.filter(t => t !== self);
                    }
                }
            }
        });
    });

    // Service cards animation
    gsap.utils.toArray('.service-card').forEach((card, index) => {
        gsap.from(card, {
            duration: 0.8,
            y: 80,
            opacity: 0,
            rotation: 5,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: card,
                start: 'top 85%',
                end: 'bottom 15%',
                toggleActions: 'play none none reverse',
                onToggle: self => scrollTriggerManager.addTrigger(self),
                onUpdate: self => {
                    if (!self.isActive && self.progress > 0.9) {
                        self.kill();
                        scrollTriggerManager.activeTriggers =
                            scrollTriggerManager.activeTriggers.filter(t => t !== self);
                    }
                }
            },
            delay: index * 0.1
        });
    });

    // Feature items animation
    gsap.utils.toArray('.feature-item').forEach((item, index) => {
        gsap.from(item, {
            duration: 0.8,
            x: -50,
            opacity: 0,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: item,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            },
            delay: index * 0.2
        });
    });

    // Stats animation - handled by initStatisticsAnimation function

    // CTA section animation
    gsap.from('.cta-content > *', {
        duration: 1,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.cta-section',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
        }
    });

    // Feature blocks animation (Why Choose WIDDX)
    gsap.utils.toArray('.feature-block').forEach((block, index) => {
        gsap.from(block, {
            duration: 0.8,
            y: 60,
            opacity: 0,
            scale: 0.9,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: block,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            },
            delay: index * 0.15
        });
    });

    // Portfolio preview animation
    gsap.utils.toArray('.portfolio-item-preview').forEach((item, index) => {
        gsap.from(item, {
            duration: 0.8,
            y: 80,
            opacity: 0,
            rotation: 5,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: item,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            },
            delay: index * 0.1
        });
    });

    // Portfolio CTA animation
    gsap.from('.portfolio-cta', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.portfolio-cta',
            start: 'top 85%',
            toggleActions: 'play none none reverse'
        }
    });

    // Testimonials animation
    gsap.utils.toArray('.testimonial-card').forEach((card, index) => {
        gsap.from(card, {
            duration: 0.8,
            y: 60,
            opacity: 0,
            scale: 0.95,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: card,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            },
            delay: index * 0.2
        });
    });

    // Footer animation
    gsap.from('.footer-section', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power2.out',
        scrollTrigger: {
            trigger: '.footer',
            start: 'top 90%',
            toggleActions: 'play none none reverse'
        }
    });
}

// ===== HOVER ANIMATIONS =====
function initHoverAnimations() {
    // Throttle hover events
    const throttle = (fn, delay) => {
        let last = 0;
        return (...args) => {
            const now = new Date().getTime();
            if (now - last < delay) return;
            last = now;
            return fn(...args);
        };
    };

    // Service card hover effects
    gsap.utils.toArray('.service-card').forEach(card => {
        // Cache DOM references
        const icon = card.querySelector('.service-icon');
        const link = card.querySelector('.service-link');
        let isAnimating = false;

        card.addEventListener('mouseenter', throttle(() => {
            if (isAnimating) return;
            isAnimating = true;
            animationMetrics.logAnimation('Service card hover');
            // Simplified combined animation
            gsap.to([card, icon, link], {
                duration: 0.4,
                y: -8,
                scale: 1.01,
                rotation: 180,
                x: 5,
                ease: 'power1.out',
                onComplete: () => {
                    isAnimating = false;
                    animationMetrics.endAnimation('Service card hover');
                }
            });
        });

        card.addEventListener('mouseleave', throttle(() => {
            if (isAnimating) return;
            isAnimating = true;
            animationMetrics.logAnimation('Service card leave');
            
            gsap.to([card, icon, link], {
                duration: 0.4,
                y: 0,
                scale: 1,
                rotation: 0,
                x: 0,
                ease: 'power1.out',
                onComplete: () => {
                    isAnimating = false;
                    animationMetrics.endAnimation('Service card leave');
                }
            });
        });
    });

    // Button hover effects (optimized)
    const buttons = gsap.utils.toArray('.btn');
    const buttonHoverHandler = throttle((btn, isEnter) => {
        animationMetrics.logAnimation('Button hover');
        gsap.to(btn, {
            duration: 0.3,
            scale: isEnter ? 1.05 : 1,
            y: isEnter ? -3 : 0,
            ease: 'power2.out',
            onComplete: () => animationMetrics.endAnimation('Button hover')
        });
    }, 100);

    buttons.forEach(btn => {
        btn.addEventListener('mouseenter', () => buttonHoverHandler(btn, true));
        btn.addEventListener('mouseleave', () => buttonHoverHandler(btn, false));
    });

    // Floating card hover effects (optimized)
    const floatingCards = gsap.utils.toArray('.floating-card');
    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            entry.target.dataset.visible = entry.isIntersecting;
        });
    }, { threshold: 0.1 });

    const cardHoverHandler = throttle((card, isEnter) => {
        if (card.dataset.visible !== 'true') return;
        
        animationMetrics.logAnimation('Card hover');
        gsap.to(card, {
            duration: 0.3,
            scale: isEnter ? 1.1 : 1,
            rotation: isEnter ? 10 : 0,
            ease: 'power2.out',
            onComplete: () => animationMetrics.endAnimation('Card hover')
        });
    }, 100);

    floatingCards.forEach(card => {
        cardObserver.observe(card);
        card.addEventListener('mouseenter', () => cardHoverHandler(card, true));
        card.addEventListener('mouseleave', () => cardHoverHandler(card, false));
    });

    // Social links hover effects (optimized)
    const socialLinks = gsap.utils.toArray('.social-links a');
    const socialHoverHandler = throttle((link, isEnter) => {
        animationMetrics.logAnimation('Social link hover');
        gsap.to(link, {
            duration: 0.3,
            scale: isEnter ? 1.2 : 1,
            rotation: isEnter ? 360 : 0,
            ease: isEnter ? 'back.out(1.7)' : 'power2.out',
            onComplete: () => animationMetrics.endAnimation('Social link hover')
        });
    }, 150);

    socialLinks.forEach(link => {
        link.addEventListener('mouseenter', () => socialHoverHandler(link, true));
        link.addEventListener('mouseleave', () => socialHoverHandler(link, false));
    });

    // Feature block hover effects (optimized)
    const featureBlocks = gsap.utils.toArray('.feature-block');
    const featureHoverHandler = throttle((block, icon, isEnter) => {
        animationMetrics.logAnimation('Feature block hover');
        gsap.to([block, icon], {
            duration: 0.3,
            y: isEnter ? -10 : 0,
            scale: isEnter ? 1.02 : 1,
            rotation: isEnter ? 360 : 0,
            ease: 'power2.out',
            onComplete: () => animationMetrics.endAnimation('Feature block hover')
        });
    }, 100);

    featureBlocks.forEach(block => {
        const icon = block.querySelector('.feature-icon');
        block.addEventListener('mouseenter', () => featureHoverHandler(block, icon, true));
        block.addEventListener('mouseleave', () => featureHoverHandler(block, icon, false));
    });

    // Portfolio preview hover effects (optimized)
    const portfolioItems = gsap.utils.toArray('.portfolio-item-preview');
    const portfolioHoverHandler = throttle((item, isEnter) => {
        animationMetrics.logAnimation('Portfolio hover');
        gsap.to(item, {
            duration: 0.3,
            y: isEnter ? -10 : 0,
            scale: isEnter ? 1.03 : 1,
            ease: 'power2.out',
            onComplete: () => animationMetrics.endAnimation('Portfolio hover')
        });
    }, 100);

    portfolioItems.forEach(item => {
        item.addEventListener('mouseenter', () => portfolioHoverHandler(item, true));
        item.addEventListener('mouseleave', () => portfolioHoverHandler(item, false));
    });

    // Testimonial card hover effects (optimized)
    const testimonialCards = gsap.utils.toArray('.testimonial-card');
    const testimonialHoverHandler = throttle((card, isEnter) => {
        animationMetrics.logAnimation('Testimonial hover');
        gsap.to(card, {
            duration: 0.3,
            y: isEnter ? -5 : 0,
            scale: isEnter ? 1.02 : 1,
            ease: 'power2.out',
            onComplete: () => animationMetrics.endAnimation('Testimonial hover')
        });
    }, 100);

    testimonialCards.forEach(card => {
        card.addEventListener('mouseenter', () => testimonialHoverHandler(card, true));
        card.addEventListener('mouseleave', () => testimonialHoverHandler(card, false));
    });
}

// ===== PAGE TRANSITION ANIMATIONS =====
function initPageTransitions() {
    // Smooth page load animation
    gsap.from('body', {
        duration: 0.5,
        opacity: 0,
        ease: 'power2.out'
    });

    // Navigation link click animations
    gsap.utils.toArray('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            if (link.getAttribute('href').startsWith('#')) return;

            e.preventDefault();
            const href = link.getAttribute('href');

            gsap.to('body', {
                duration: 0.5,
                opacity: 0,
                ease: 'power2.inOut',
                onComplete: () => {
                    window.location.href = href;
                }
            });
        });
    });
}

// ===== SCROLL-TRIGGERED PARALLAX =====
function initParallaxScrolling() {
    // Hero background parallax
    gsap.to('.hero-background', {
        yPercent: -50,
        ease: 'none',
        scrollTrigger: {
            trigger: '.hero',
            start: 'top bottom',
            end: 'bottom top',
            scrub: true
        }
    });

    // Floating elements parallax
    gsap.utils.toArray('.floating-card').forEach((card, index) => {
        const speed = (index + 1) * 0.5;
        gsap.to(card, {
            yPercent: -50 * speed,
            ease: 'none',
            scrollTrigger: {
                trigger: '.hero',
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    });
}

// ===== NAVBAR SCROLL ANIMATION =====
function initNavbarAnimation() {
    gsap.to('.navbar', {
        backgroundColor: 'rgba(1, 8, 21, 0.98)',
        backdropFilter: 'blur(20px)',
        padding: '10px 0',
        ease: 'power2.out',
        scrollTrigger: {
            trigger: 'body',
            start: 'top -100',
            end: 'top -101',
            toggleActions: 'play none none reverse'
        }
    });
}

// ===== SCROLL TO TOP BUTTON ANIMATION =====
function initScrollToTopAnimation() {
    const scrollBtn = document.getElementById('scrollToTop');

    if (scrollBtn) {
        gsap.set(scrollBtn, { scale: 0, rotation: -180 });

        ScrollTrigger.create({
            start: 'top -300',
            end: 99999,
            toggleClass: { className: 'visible', targets: scrollBtn },
            onToggle: self => {
                if (self.isActive) {
                    gsap.to(scrollBtn, {
                        duration: 0.5,
                        scale: 1,
                        rotation: 0,
                        ease: 'back.out(1.7)'
                    });
                } else {
                    gsap.to(scrollBtn, {
                        duration: 0.3,
                        scale: 0,
                        rotation: -180,
                        ease: 'power2.in'
                    });
                }
            }
        });

        scrollBtn.addEventListener('click', () => {
            gsap.to(window, {
                duration: 1.5,
                scrollTo: { y: 0 },
                ease: 'power3.inOut'
            });
        });
    }
}

// ===== NEW SECTIONS ANIMATIONS =====

// Statistics counter animation
function initStatisticsAnimation() {
    gsap.utils.toArray('.stat-item').forEach((stat, index) => {
        const number = stat.querySelector('.stat-number');
        const target = parseFloat(number.getAttribute('data-target'));

        // Reset initial state
        gsap.set(stat, { opacity: 0, y: 60, scale: 0.9 });
        gsap.set(number, { textContent: 0 });

        // Create timeline for this stat item
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: stat,
                start: 'top 85%',
                toggleActions: 'play none none reverse',
                onEnter: () => {
                    // Animate the stat item appearance
                    gsap.to(stat, {
                        duration: 0.8,
                        y: 0,
                        opacity: 1,
                        scale: 1,
                        ease: 'power3.out',
                        delay: index * 0.1
                    });

                    // Animate the counter
                    gsap.to(number, {
                        duration: 2,
                        textContent: target,
                        ease: 'power2.out',
                        snap: { textContent: target < 100 ? 0.1 : 1 },
                        delay: (index * 0.1) + 0.5,
                        onUpdate: function() {
                            // Format the number properly
                            const currentValue = parseFloat(this.targets()[0].textContent);
                            if (target >= 1000) {
                                this.targets()[0].textContent = Math.round(currentValue).toLocaleString();
                            } else {
                                this.targets()[0].textContent = currentValue.toFixed(target < 100 ? 1 : 0);
                            }
                        }
                    });
                },
                onLeave: () => {
                    // Reset when leaving viewport
                    gsap.set(stat, { opacity: 0, y: 60, scale: 0.9 });
                    gsap.set(number, { textContent: 0 });
                }
            }
        });

        // Add hover effects for stat items
        stat.addEventListener('mouseenter', () => {
            gsap.to(stat, {
                duration: 0.3,
                y: -10,
                scale: 1.05,
                ease: 'power2.out'
            });

            gsap.to(stat.querySelector('.stat-icon'), {
                duration: 0.4,
                rotation: 360,
                scale: 1.1,
                ease: 'back.out(1.7)'
            });
        });

        stat.addEventListener('mouseleave', () => {
            gsap.to(stat, {
                duration: 0.3,
                y: 0,
                scale: 1,
                ease: 'power2.out'
            });

            gsap.to(stat.querySelector('.stat-icon'), {
                duration: 0.3,
                rotation: 0,
                scale: 1,
                ease: 'power2.out'
            });
        });
    });
}

// Technical features animation
function initTechnicalFeaturesAnimation() {
    gsap.utils.toArray('.tech-feature').forEach((feature, index) => {
        gsap.from(feature, {
            duration: 0.8,
            x: -80,
            opacity: 0,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: feature,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            },
            delay: index * 0.2
        });
    });

    // Server stack animation
    gsap.utils.toArray('.server-layer').forEach((layer, index) => {
        gsap.from(layer, {
            duration: 0.6,
            x: 100,
            opacity: 0,
            rotation: 10,
            ease: 'back.out(1.7)',
            scrollTrigger: {
                trigger: layer,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            },
            delay: index * 0.15
        });
    });
}

// FAQ animation and functionality
function initFAQAnimation() {
    gsap.utils.toArray('.faq-item').forEach((item, index) => {
        gsap.from(item, {
            duration: 0.6,
            y: 50,
            opacity: 0,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: item,
                start: 'top 90%',
                toggleActions: 'play none none reverse'
            },
            delay: index * 0.1
        });

        // FAQ click functionality
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');

        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');

            // Close all other FAQ items
            document.querySelectorAll('.faq-item.active').forEach(activeItem => {
                if (activeItem !== item) {
                    activeItem.classList.remove('active');
                    gsap.to(activeItem.querySelector('.faq-answer'), {
                        duration: 0.3,
                        maxHeight: 0,
                        ease: 'power2.inOut'
                    });
                }
            });

            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
                gsap.to(answer, {
                    duration: 0.3,
                    maxHeight: 0,
                    ease: 'power2.inOut'
                });
            } else {
                item.classList.add('active');
                gsap.set(answer, { maxHeight: 'auto' });
                const height = answer.scrollHeight;
                gsap.fromTo(answer,
                    { maxHeight: 0 },
                    {
                        duration: 0.3,
                        maxHeight: height,
                        ease: 'power2.inOut'
                    }
                );
            }
        });
    });
}

// Initialize additional animations after page load
window.addEventListener('load', function() {
    initParallaxScrolling();
    initNavbarAnimation();
    initScrollToTopAnimation();
    initStatisticsAnimation();
    initTechnicalFeaturesAnimation();
    initFAQAnimation();
});
