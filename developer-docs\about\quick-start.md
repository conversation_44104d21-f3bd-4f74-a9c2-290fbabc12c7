+++
title = "Quick Start"
weight = 5

+++

## What would you like to do?

* [Create a custom WHMCS client area theme](/themes/getting-started)<br>A theme in WHMCS controls the client facing user interface. Create a seamless experience for your visitors by creating a custom client area theme that matches the rest of your website.
* [Create a Provisioning Module](/provisioning-modules)<br>Provisioning Modules enable provisioning and management of services in WHMCS. They are also sometimes referred to as Product or Server Modules.
* [Create an Addon Module](/addon-modules)<br>Addon Modules allow you to create both admin pages and hooks to extend WHMCS further.
* [Create a Domain Registar Module](/domain-registrars)<br>Registrar Modules allow for the registration and management of domains within WHMCS. Registrar Modules are also referred to as Domain Modules.
* [Create a Payment Gateway Module](/payment-gateways)<br>Creating a Payment Gateway Module allows you to connect and integrate WHMCS with additional payment service providers.
* [Create a Mail Provider Module](/mail-providers)<br>Mail Provider Modules allow you to add custom mail providers to WHMCS.
* [Integrate WHMCS with 3rd party systems using the API](/api)<br>The WHMCS API allows you to perform operations and actions within WHMCS from external third party and custom code.
* [Browse the API Reference](/api-reference)<br>A complete listing of API functions available in WHMCS.
* [Integrate 3rd party systems into WHMCS using Hooks](/hooks)<br>Hooks allow you to execute your own code when events occur inside WHMCS.
* [Browse the Hook Reference](/hooks-reference)<br>A complete listing of Hook Points available in WHMCS.
* [Interact and retrieve data from the WHMCS Database](/advanced/db-interaction)<br>Learn how to interact with the WHMCS database.
* [Create a new language](/languages/adding-a-language)<br>Create your own additional language translations.
* [Upgrade to WHMCS 8.0](/advanced/upgrade-to-whmcs-8)<br>View important information about updating your code for WHMCS 8.0.
