<?php
// Security Configuration
if (!defined('SECURE_WEBSOCKET_CONFIG_LOADED')) {
    define('SECURE_WEBSOCKET_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// WebSocket Configuration
const WEBSOCKET_ENABLED = Env::get('WEBSOCKET_ENABLED', true);
const WEBSOCKET_HOST = Env::get('WEBSOCKET_HOST', 'localhost');
const WEBSOCKET_PORT = Env::get('WEBSOCKET_PORT', 8080);
const WEBSOCKET_SSL = Env::get('WEBSOCKET_SSL', false);
const WEBSOCKET_SSL_CERT = Env::get('WEBSOCKET_SSL_CERT', '');
const WEBSOCKET_SSL_KEY = Env::get('WEBSOCKET_SSL_KEY', '');

// Connection Settings
const WEBSOCKET_UPDATE_INTERVAL = Env::get('WEBSOCKET_UPDATE_INTERVAL', 5);
const WEBSOCKET_MAX_CLIENTS = Env::get('WEBSOCKET_MAX_CLIENTS', 100);
const WEBSOCKET_TIMEOUT = Env::get('WEBSOCKET_TIMEOUT', 30);

// Logging Configuration
const WEBSOCKET_LOG_FILE = Env::get('WEBSOCKET_LOG_FILE', __DIR__ . '/../logs/websocket.log');
const WEBSOCKET_DEBUG_ENABLED = Env::get('WEBSOCKET_DEBUG_ENABLED', Env::get('APP_DEBUG', false));
const WEBSOCKET_DEBUG_FILE = Env::get('WEBSOCKET_DEBUG_FILE', __DIR__ . '/../logs/websocket_debug.log');

// Security Settings
const WEBSOCKET_ALLOWED_ORIGINS = Env::get('WEBSOCKET_ALLOWED_ORIGINS', ['http://localhost', 'https://localhost']);
const WEBSOCKET_ALLOWED_PROTOCOLS = Env::get('WEBSOCKET_ALLOWED_PROTOCOLS', ['widdx-analytics']);
const WEBSOCKET_MAX_MESSAGE_SIZE = Env::get('WEBSOCKET_MAX_MESSAGE_SIZE', 1048576);
const WEBSOCKET_COMPRESSION = Env::get('WEBSOCKET_COMPRESSION', true);

// Rate Limiting
const WEBSOCKET_RATE_LIMIT_ENABLED = Env::get('WEBSOCKET_RATE_LIMIT_ENABLED', true);
const WEBSOCKET_RATE_LIMIT_WINDOW = Env::get('WEBSOCKET_RATE_LIMIT_WINDOW', 60);
const WEBSOCKET_RATE_LIMIT_MAX = Env::get('WEBSOCKET_RATE_LIMIT_MAX', 1000);
const WEBSOCKET_RATE_LIMIT_BURST = Env::get('WEBSOCKET_RATE_LIMIT_BURST', 2000);
const WEBSOCKET_RATE_LIMIT_RECOVER = Env::get('WEBSOCKET_RATE_LIMIT_RECOVER', 300);
const WEBSOCKET_RATE_LIMIT_STORAGE = Env::get('WEBSOCKET_RATE_LIMIT_STORAGE', 'redis');

// Token Security
const WEBSOCKET_SECURITY_ENABLED = Env::get('WEBSOCKET_SECURITY_ENABLED', true);
const WEBSOCKET_SECURITY_TOKEN_ENABLED = Env::get('WEBSOCKET_SECURITY_TOKEN_ENABLED', true);
const WEBSOCKET_SECURITY_TOKEN_NAME = Env::get('WEBSOCKET_SECURITY_TOKEN_NAME', '_websocket_token');
const WEBSOCKET_SECURITY_TOKEN_LENGTH = Env::get('WEBSOCKET_SECURITY_TOKEN_LENGTH', 32);
const WEBSOCKET_SECURITY_TOKEN_TTL = Env::get('WEBSOCKET_SECURITY_TOKEN_TTL', 3600);
const WEBSOCKET_SECURITY_TOKEN_STORAGE = Env::get('WEBSOCKET_SECURITY_TOKEN_STORAGE', 'session');

// IP Security
const WEBSOCKET_IP_WHITELIST_ENABLED = Env::get('WEBSOCKET_IP_WHITELIST_ENABLED', true);
const WEBSOCKET_IP_WHITELIST = Env::get('WEBSOCKET_IP_WHITELIST', ['127.0.0.1', '::1']);
const WEBSOCKET_IP_BLACKLIST_ENABLED = Env::get('WEBSOCKET_IP_BLACKLIST_ENABLED', true);
const WEBSOCKET_IP_BLACKLIST = Env::get('WEBSOCKET_IP_BLACKLIST', []);
const WEBSOCKET_IP_LOGGING_ENABLED = Env::get('WEBSOCKET_IP_LOGGING_ENABLED', true);

// Request Validation
const WEBSOCKET_REQUEST_VALIDATION_ENABLED = Env::get('WEBSOCKET_REQUEST_VALIDATION_ENABLED', true);
const WEBSOCKET_REQUEST_MAX_HEADERS = Env::get('WEBSOCKET_REQUEST_MAX_HEADERS', 100);
const WEBSOCKET_REQUEST_MAX_BODY_SIZE = Env::get('WEBSOCKET_REQUEST_MAX_BODY_SIZE', 10485760);

// Validate configuration
if (WEBSOCKET_ENABLED && WEBSOCKET_SSL && (!WEBSOCKET_SSL_CERT || !WEBSOCKET_SSL_KEY)) {
    trigger_error('SSL is enabled but SSL certificate or key is not configured', E_USER_WARNING);
}

if (WEBSOCKET_MAX_CLIENTS < 1) {
    trigger_error('Invalid maximum clients value', E_USER_WARNING);
}

if (WEBSOCKET_TIMEOUT < 5) {
    trigger_error('WebSocket timeout is too low', E_USER_WARNING);
}

// Create log directories with proper permissions
$logDir = dirname(WEBSOCKET_LOG_FILE);
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

// Set proper permissions
chmod($logDir, 0755);

// Validate rate limiting configuration
if (WEBSOCKET_RATE_LIMIT_MAX < 0) {
    trigger_error('Invalid rate limit maximum value', E_USER_WARNING);
}

if (WEBSOCKET_RATE_LIMIT_BURST < 0) {
    trigger_error('Invalid rate limit burst value', E_USER_WARNING);
}

// Validate security token configuration
if (WEBSOCKET_SECURITY_TOKEN_ENABLED && WEBSOCKET_SECURITY_TOKEN_LENGTH < 16) {
    trigger_error('Security token length is too short', E_USER_WARNING);
}
define('WEBSOCKET_REQUEST_TIMEOUT', 30);
define('WEBSOCKET_REQUEST_LOGGING_ENABLED', true);
define('WEBSOCKET_REQUEST_LOGGING_FILE', __DIR__ . '/../logs/websocket_requests.log');

define('WEBSOCKET_SESSION_ENABLED', true);
define('WEBSOCKET_SESSION_NAME', 'widdx_websocket');
define('WEBSOCKET_SESSION_LIFETIME', 3600);
define('WEBSOCKET_SESSION_COOKIE_SECURE', true);
define('WEBSOCKET_SESSION_COOKIE_HTTPONLY', true);
define('WEBSOCKET_SESSION_COOKIE_SAMESITE', 'Strict');
define('WEBSOCKET_SESSION_REGENERATE', true);
define('WEBSOCKET_SESSION_REGENERATE_INTERVAL', 300);

define('WEBSOCKET_LOGGING_ENABLED', true);
define('WEBSOCKET_LOGGING_LEVEL', 'INFO');
define('WEBSOCKET_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');

define('WEBSOCKET_METRICS_ENABLED', true);
define('WEBSOCKET_METRICS_INTERVAL', 60);
define('WEBSOCKET_METRICS_FILE', __DIR__ . '/../logs/websocket_metrics.log');
define('WEBSOCKET_METRICS_FORMAT', '{timestamp} {metric} {value}');

define('WEBSOCKET_ERROR_HANDLING_ENABLED', true);
define('WEBSOCKET_ERROR_RETRY_ENABLED', true);
define('WEBSOCKET_ERROR_RETRY_MAX_ATTEMPTS', 3);
define('WEBSOCKET_ERROR_RETRY_DELAY', 1000);
define('WEBSOCKET_ERROR_RETRY_BACKOFF', 2);
define('WEBSOCKET_ERROR_LOGGING_ENABLED', true);
define('WEBSOCKET_ERROR_LOGGING_LEVEL', 'ERROR');

define('WEBSOCKET_SECURITY_INPUT_VALIDATION', true);
define('WEBSOCKET_SECURITY_OUTPUT_VALIDATION', true);
define('WEBSOCKET_SECURITY_RATE_LIMITING', true);
define('WEBSOCKET_SECURITY_API_KEY_VALIDATION', true);
define('WEBSOCKET_SECURITY_IP_RESTRICTIONS', true);
define('WEBSOCKET_SECURITY_REQUEST_VALIDATION', true);

define('WEBSOCKET_AUTH_ENABLED', true);
define('WEBSOCKET_AUTH_METHOD', 'session'); // session, jwt, cookie
define('WEBSOCKET_AUTH_TTL', 3600);
define('WEBSOCKET_AUTH_REFRESH_TTL', 86400);
define('WEBSOCKET_AUTH_TOKEN_NAME', 'websocket_token');
define('WEBSOCKET_AUTH_COOKIE_NAME', 'widdx_websocket_auth');
define('WEBSOCKET_AUTH_COOKIE_SECURE', true);
define('WEBSOCKET_AUTH_COOKIE_HTTPONLY', true);
define('WEBSOCKET_AUTH_COOKIE_SAMESITE', 'Strict');
define('WEBSOCKET_AUTH_PASSWORD_MIN_LENGTH', 8);
define('WEBSOCKET_AUTH_PASSWORD_MAX_LENGTH', 128);
define('WEBSOCKET_AUTH_PASSWORD_REQUIRE_UPPERCASE', true);
define('WEBSOCKET_AUTH_PASSWORD_REQUIRE_LOWERCASE', true);
define('WEBSOCKET_AUTH_PASSWORD_REQUIRE_NUMBER', true);
define('WEBSOCKET_AUTH_PASSWORD_REQUIRE_SPECIAL', true);
define('WEBSOCKET_AUTH_PASSWORD_SPECIAL_CHARS', '!@#$%^&*()');

define('WEBSOCKET_API_KEYS', [
    'DEEPSEEK' => [
        'enabled' => true,
        'required' => true,
        'validate' => true,
        'rotate' => true
    ],
    'GEMINI' => [
        'enabled' => true,
        'required' => true,
        'validate' => true,
        'rotate' => true
    ]
]);

define('WEBSOCKET_IP_RESTRICTIONS', [
    'whitelist' => [
        'enabled' => true,
        'ips' => ['127.0.0.1', '::1']
    ],
    'blacklist' => [
        'enabled' => true,
        'ips' => []
    ]
]);

define('WEBSOCKET_REQUEST_VALIDATION', [
    'enabled' => true,
    'max_headers' => 100,
    'max_body_size' => 10485760,
    'timeout' => 30,
    'validate_content_type' => true,
    'validate_content_length' => true,
    'validate_headers' => true
]);

define('WEBSOCKET_ERROR_MESSAGES', [
    'generic' => 'حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
    'timeout' => 'تمت معالجة الطلب لفترة طويلة. يرجى المحاولة مرة أخرى.',
    'rate_limit' => 'تم تجاوز حد معدل الطلبات. يرجى الانتظار قليلاً.',
    'invalid_input' => 'المدخلات غير صالحة. يرجى التأكد من صحة المدخلات.',
    'api_error' => 'حدث خطأ في خدمة API. يرجى المحاولة مرة أخرى.',
    'authentication' => 'فشل التحقق من الهوية. يرجى التأكد من بيانات تسجيل الدخول.',
    'permission' => 'ليس لديك صلاحيات كافية للوصول إلى هذه الميزة.',
    'not_found' => 'لم يتم العثور على المورد المطلوب.',
    'server_error' => 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.'
]);
