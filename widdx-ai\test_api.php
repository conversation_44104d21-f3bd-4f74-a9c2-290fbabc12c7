<?php
// Test script for the API endpoint
$apiUrl = 'http://localhost/widdx-ai/api/ask.php';

// Test data
$testData = [
    'question' => 'مرحبا',
    'context' => 'test context'
];

// Initialize cURL
$ch = curl_init($apiUrl);

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

// Execute the request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// Check for errors
if (curl_errno($ch)) {
    echo "cURL Error: " . curl_error($ch);
} else {
    echo "<h2>API Test Results</h2>";
    echo "<p>Status Code: $httpCode</p>";
    
    // Try to decode JSON response
    $decoded = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<h3>Response (JSON):</h3>";
        echo "<pre>";
        print_r($decoded);
        echo "</pre>";
    } else {
        echo "<h3>Response (Raw):</h3>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
}

// Close cURL resource
curl_close($ch);
?>
