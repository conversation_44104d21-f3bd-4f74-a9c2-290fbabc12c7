<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Cache.php';
require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/../config/websocket.php';

class WebSocketHandler {
    private $clients;
    private $db;
    private $cache;
    private $logger;
    private $security;
    private $updateInterval;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->db = new Database();
        $this->cache = new Cache();
        $this->logger = new Logger();
        $this->security = new Security();
        $this->updateInterval = WEBSOCKET_UPDATE_INTERVAL;
        
        // Start background update process
        $this->startUpdateProcess();
    }

    public function onOpen(ConnectionInterface $conn) {
        try {
            // Validate connection
            $this->security->validateRequest($conn);
            
            // Add client
            $this->clients->attach($conn);
            
            // Send welcome message
            $conn->send(json_encode([
                'type' => 'welcome',
                'message' => 'مرحباً! تم الاتصال بنجاح بـ WIDDX WebSocket Server',
                'timestamp' => time()
            ]));
            
            $this->logger->log('INFO', 'New WebSocket connection', [
                'ip' => $conn->remoteAddress,
                'clients' => count($this->clients)
            ]);
            
        } catch (Exception $e) {
            $this->handleError($conn, $e);
        }
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        try {
            $data = json_decode($msg, true);
            if (!$data) {
                throw new Exception('Invalid JSON message');
            }
            
            switch ($data['type']) {
                case 'subscribe':
                    $this->handleSubscribe($from, $data);
                    break;
                case 'unsubscribe':
                    $this->handleUnsubscribe($from, $data);
                    break;
                case 'ping':
                    $this->handlePing($from, $data);
                    break;
                default:
                    throw new Exception('Unknown message type: ' . $data['type']);
            }
            
        } catch (Exception $e) {
            $this->handleError($from, $e);
        }
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        $this->logger->log('INFO', 'WebSocket connection closed', [
            'ip' => $conn->remoteAddress,
            'clients' => count($this->clients)
        ]);
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        $this->handleError($conn, $e);
    }

    private function handleSubscribe(ConnectionInterface $conn, $data) {
        // Implement subscription logic
        $this->logger->log('INFO', 'Client subscribed to updates', [
            'ip' => $conn->remoteAddress,
            'subscription' => $data['subscription'] ?? 'all'
        ]);
    }

    private function handleUnsubscribe(ConnectionInterface $conn, $data) {
        // Implement unsubscription logic
        $this->logger->log('INFO', 'Client unsubscribed from updates', [
            'ip' => $conn->remoteAddress,
            'subscription' => $data['subscription'] ?? 'all'
        ]);
    }

    private function handlePing(ConnectionInterface $conn, $data) {
        $conn->send(json_encode([
            'type' => 'pong',
            'timestamp' => time()
        ]));
    }

    private function handleError(ConnectionInterface $conn, \Exception $e) {
        $this->logger->log('ERROR', 'WebSocket error', [
            'ip' => $conn->remoteAddress,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        $conn->send(json_encode([
            'type' => 'error',
            'message' => WEBSOCKET_ERROR_MESSAGES['generic'],
            'timestamp' => time()
        ]));
        
        $conn->close();
    }

    private function startUpdateProcess() {
        // Create background process
        $pid = pcntl_fork();
        
        if ($pid == -1) {
            die('Could not fork');
        } else if ($pid) {
            // Parent process
            return;
        }

        // Child process
        while (true) {
            $this->sendUpdates();
            sleep($this->updateInterval);
        }
    }

    private function sendUpdates() {
        try {
            $data = $this->getAnalyticsData();
            
            foreach ($this->clients as $client) {
                $client->send(json_encode([
                    'type' => 'update',
                    'data' => $data,
                    'timestamp' => time()
                ]));
            }
            
        } catch (Exception $e) {
            $this->logger->log('ERROR', 'Error sending updates', [
                'error' => $e->getMessage()
            ]);
        }
    }

    private function getAnalyticsData() {
        return [
            'system' => $this->getSystemStatus(),
            'metrics' => $this->getMetrics(),
            'activity' => $this->getRecentActivity(),
            'errors' => $this->getRecentErrors()
        ];
    }

    private function getSystemStatus() {
        return [
            'uptime' => time() - $_SERVER['REQUEST_TIME'],
            'memory' => memory_get_usage(true),
            'clients' => count($this->clients),
            'status' => $this->cache->get('system_status') ?? 'healthy'
        ];
    }

    private function getMetrics() {
        return [
            'questions' => $this->db->getQuestionsCount(),
            'cache' => $this->cache->getStats(),
            'response_time' => $this->db->getAverageResponseTime(),
            'error_rate' => $this->db->getErrorRate()
        ];
    }

    private function getRecentActivity() {
        return $this->db->getRecentActivity();
    }

    private function getRecentErrors() {
        return $this->db->getRecentErrors();
    }
}
