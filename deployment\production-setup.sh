#!/bin/bash
#
# WIDDX Production Environment Setup Script
# Configures production server for optimal performance and security
#
# @package    WIDDX Production Deployment
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration variables
WHMCS_PATH="/var/www/html/whmcs"
BACKUP_PATH="/var/backups/whmcs"
LOG_PATH="/var/log/widdx-deployment"
DOMAIN="yourdomain.com"
EMAIL="<EMAIL>"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/deployment.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/deployment.log"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/deployment.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/deployment.log"
}

# Create log directory
mkdir -p "${LOG_PATH}"

log "🚀 Starting WIDDX Production Environment Setup"

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    error "This script should not be run as root for security reasons"
fi

# Verify WHMCS installation
if [[ ! -d "${WHMCS_PATH}" ]]; then
    error "WHMCS installation not found at ${WHMCS_PATH}"
fi

log "✅ WHMCS installation verified at ${WHMCS_PATH}"

# 1. System Requirements Check
log "🔍 Checking system requirements..."

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
if [[ $(echo "${PHP_VERSION}" | cut -d. -f1,2) < "7.4" ]]; then
    error "PHP 7.4+ required. Current version: ${PHP_VERSION}"
fi
log "✅ PHP version ${PHP_VERSION} meets requirements"

# Check required PHP extensions
REQUIRED_EXTENSIONS=("curl" "json" "openssl" "mbstring" "mysqli" "gd")
for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if ! php -m | grep -q "^${ext}$"; then
        error "Required PHP extension missing: ${ext}"
    fi
done
log "✅ All required PHP extensions are installed"

# Check MySQL/MariaDB connection
if ! mysql -e "SELECT 1;" >/dev/null 2>&1; then
    error "Cannot connect to MySQL/MariaDB database"
fi
log "✅ Database connection verified"

# 2. SSL Certificate Setup
log "🔒 Setting up SSL certificate..."

# Check if SSL certificate exists
if [[ ! -f "/etc/ssl/certs/${DOMAIN}.crt" ]]; then
    warning "SSL certificate not found. Please ensure SSL is properly configured."
    info "You can use Let's Encrypt: certbot --apache -d ${DOMAIN}"
else
    log "✅ SSL certificate found for ${DOMAIN}"
fi

# 3. Apache/Nginx Configuration
log "⚙️ Configuring web server..."

# Detect web server
if systemctl is-active --quiet apache2; then
    WEB_SERVER="apache2"
    CONFIG_PATH="/etc/apache2/sites-available"
elif systemctl is-active --quiet nginx; then
    WEB_SERVER="nginx"
    CONFIG_PATH="/etc/nginx/sites-available"
else
    error "No supported web server (Apache/Nginx) detected"
fi

log "✅ Detected web server: ${WEB_SERVER}"

# Create Apache configuration
if [[ "${WEB_SERVER}" == "apache2" ]]; then
    cat > "${CONFIG_PATH}/widdx-production.conf" << 'EOF'
<VirtualHost *:80>
    ServerName DOMAIN_PLACEHOLDER
    DocumentRoot /var/www/html/whmcs
    
    # Force HTTPS redirect
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName DOMAIN_PLACEHOLDER
    DocumentRoot /var/www/html/whmcs
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/DOMAIN_PLACEHOLDER.crt
    SSLCertificateKeyFile /etc/ssl/private/DOMAIN_PLACEHOLDER.key
    SSLCertificateChainFile /etc/ssl/certs/DOMAIN_PLACEHOLDER-chain.crt
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com; style-src 'self' 'unsafe-inline' *.googleapis.com; img-src 'self' data: *.gravatar.com; font-src 'self' *.googleapis.com *.gstatic.com; connect-src 'self' *.lahza.io"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # Performance Headers
    Header set Cache-Control "public, max-age=31536000" "expr=%{REQUEST_URI} =~ m#\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$#"
    
    # Compression
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/json
    </IfModule>
    
    # Browser Caching
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType text/css "access plus 1 year"
        ExpiresByType application/javascript "access plus 1 year"
        ExpiresByType image/png "access plus 1 year"
        ExpiresByType image/jpg "access plus 1 year"
        ExpiresByType image/jpeg "access plus 1 year"
        ExpiresByType image/gif "access plus 1 year"
        ExpiresByType image/svg+xml "access plus 1 year"
        ExpiresByType font/woff "access plus 1 year"
        ExpiresByType font/woff2 "access plus 1 year"
    </IfModule>
    
    # Directory Security
    <Directory "/var/www/html/whmcs">
        Options -Indexes -ExecCGI
        AllowOverride All
        Require all granted
        
        # Protect sensitive files
        <FilesMatch "\.(log|sql|conf|ini)$">
            Require all denied
        </FilesMatch>
    </Directory>
    
    # Protect admin directory
    <Directory "/var/www/html/whmcs/admin">
        # Add IP restrictions here if needed
        # Require ip ***********/24
    </Directory>
    
    # Error and Access Logs
    ErrorLog ${APACHE_LOG_DIR}/widdx_error.log
    CustomLog ${APACHE_LOG_DIR}/widdx_access.log combined
    LogLevel warn
</VirtualHost>
EOF

    # Replace placeholders
    sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" "${CONFIG_PATH}/widdx-production.conf"
    
    # Enable site and modules
    a2ensite widdx-production
    a2enmod rewrite ssl headers expires deflate
    systemctl reload apache2
    
    log "✅ Apache configuration created and enabled"
fi

# 4. PHP Configuration Optimization
log "🐘 Optimizing PHP configuration..."

# Create PHP configuration for production
cat > "/etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/apache2/conf.d/99-widdx-production.ini" << 'EOF'
; WIDDX Production PHP Configuration

; Security Settings
expose_php = Off
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php/widdx_errors.log

; Performance Settings
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
post_max_size = 64M
upload_max_filesize = 32M
max_file_uploads = 20

; Session Security
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
session.cookie_samesite = "Strict"

; OPcache Settings
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.validate_timestamps = 0
EOF

# Create PHP error log directory
mkdir -p /var/log/php
chown www-data:www-data /var/log/php

systemctl reload apache2
log "✅ PHP configuration optimized for production"

# 5. Database Optimization
log "🗄️ Optimizing database configuration..."

# Create database optimization script
cat > "${LOG_PATH}/db-optimize.sql" << 'EOF'
-- WIDDX Database Optimization

-- Performance indexes
ALTER TABLE tblinvoices ADD INDEX IF NOT EXISTS idx_widdx_status_date (status, date);
ALTER TABLE tblaccounts ADD INDEX IF NOT EXISTS idx_widdx_gateway_date (gateway, date);
ALTER TABLE tblclients ADD INDEX IF NOT EXISTS idx_widdx_status_email (status, email);
ALTER TABLE tblgatewaylog ADD INDEX IF NOT EXISTS idx_widdx_gateway_date (gateway, date);
ALTER TABLE tblgatewaylog ADD INDEX IF NOT EXISTS idx_widdx_result_date (result, date);

-- Optimize tables
OPTIMIZE TABLE tblinvoices;
OPTIMIZE TABLE tblaccounts;
OPTIMIZE TABLE tblclients;
OPTIMIZE TABLE tblgatewaylog;

-- Update table statistics
ANALYZE TABLE tblinvoices;
ANALYZE TABLE tblaccounts;
ANALYZE TABLE tblclients;
ANALYZE TABLE tblgatewaylog;
EOF

mysql < "${LOG_PATH}/db-optimize.sql"
log "✅ Database optimization completed"

# 6. File Permissions and Security
log "🔐 Setting secure file permissions..."

# Set proper ownership
chown -R www-data:www-data "${WHMCS_PATH}"

# Set secure permissions
find "${WHMCS_PATH}" -type d -exec chmod 755 {} \;
find "${WHMCS_PATH}" -type f -exec chmod 644 {} \;

# Protect sensitive directories
chmod 750 "${WHMCS_PATH}/admin"
chmod 750 "${WHMCS_PATH}/modules/gateways"
chmod 750 "${WHMCS_PATH}/includes"

# Create secure log directories
mkdir -p "${WHMCS_PATH}/modules/gateways/logs"
chmod 750 "${WHMCS_PATH}/modules/gateways/logs"
chown www-data:www-data "${WHMCS_PATH}/modules/gateways/logs"

log "✅ File permissions configured securely"

# 7. Firewall Configuration
log "🛡️ Configuring firewall..."

# Configure UFW if available
if command -v ufw >/dev/null 2>&1; then
    ufw --force enable
    ufw default deny incoming
    ufw default allow outgoing
    ufw allow ssh
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw reload
    log "✅ UFW firewall configured"
else
    warning "UFW not available. Please configure firewall manually."
fi

# 8. Monitoring Setup
log "📊 Setting up monitoring..."

# Create monitoring directory
mkdir -p "${LOG_PATH}/monitoring"

# Create system monitoring script
cat > "${LOG_PATH}/monitoring/system-monitor.sh" << 'EOF'
#!/bin/bash
# WIDDX System Monitoring Script

TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
LOG_FILE="/var/log/widdx-deployment/monitoring/system.log"

# System metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
DISK_USAGE=$(df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1)
LOAD_AVERAGE=$(uptime | awk -F'load average:' '{print $2}')

# Log metrics
echo "${TIMESTAMP} CPU:${CPU_USAGE}% MEM:${MEMORY_USAGE}% DISK:${DISK_USAGE}% LOAD:${LOAD_AVERAGE}" >> "${LOG_FILE}"

# Check for high resource usage
if (( $(echo "${CPU_USAGE} > 80" | bc -l) )); then
    echo "${TIMESTAMP} HIGH CPU USAGE: ${CPU_USAGE}%" >> "${LOG_FILE}"
fi

if (( $(echo "${MEMORY_USAGE} > 85" | bc -l) )); then
    echo "${TIMESTAMP} HIGH MEMORY USAGE: ${MEMORY_USAGE}%" >> "${LOG_FILE}"
fi

if [[ ${DISK_USAGE} -gt 90 ]]; then
    echo "${TIMESTAMP} HIGH DISK USAGE: ${DISK_USAGE}%" >> "${LOG_FILE}"
fi
EOF

chmod +x "${LOG_PATH}/monitoring/system-monitor.sh"

# Add to crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * ${LOG_PATH}/monitoring/system-monitor.sh") | crontab -

log "✅ System monitoring configured"

# 9. Backup Configuration
log "💾 Configuring backup system..."

# Create backup script
cat > "${LOG_PATH}/backup-system.sh" << 'EOF'
#!/bin/bash
# WIDDX Automated Backup Script

BACKUP_DIR="/var/backups/whmcs"
WHMCS_PATH="/var/www/html/whmcs"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create backup directory
mkdir -p "${BACKUP_DIR}"

# Database backup
mysqldump --single-transaction --routines --triggers whmcs > "${BACKUP_DIR}/whmcs_db_${TIMESTAMP}.sql"
gzip "${BACKUP_DIR}/whmcs_db_${TIMESTAMP}.sql"

# File backup
tar -czf "${BACKUP_DIR}/whmcs_files_${TIMESTAMP}.tar.gz" -C "$(dirname ${WHMCS_PATH})" "$(basename ${WHMCS_PATH})"

# Clean old backups
find "${BACKUP_DIR}" -name "whmcs_*" -mtime +${RETENTION_DAYS} -delete

echo "$(date): Backup completed successfully" >> "${BACKUP_DIR}/backup.log"
EOF

chmod +x "${LOG_PATH}/backup-system.sh"

# Schedule daily backups
(crontab -l 2>/dev/null; echo "0 2 * * * ${LOG_PATH}/backup-system.sh") | crontab -

log "✅ Automated backup system configured"

# 10. Final Security Hardening
log "🔒 Applying final security hardening..."

# Create .htaccess for additional security
cat > "${WHMCS_PATH}/.htaccess" << 'EOF'
# WIDDX Security Hardening

# Disable server signature
ServerTokens Prod
ServerSignature Off

# Prevent access to sensitive files
<FilesMatch "\.(log|sql|conf|ini|bak|old|tmp)$">
    Require all denied
</FilesMatch>

# Protect against common attacks
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block suspicious requests
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} \.\./\.\./\.\./\.\./\.\./\.\./\.\./\.\./\.\./\.\./ [OR]
    RewriteCond %{QUERY_STRING} (NULL|OUTFILE|LOAD_FILE) [OR]
    RewriteCond %{QUERY_STRING} (\.{1,}/)+(motd|etc|bin) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # Force HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Security headers via .htaccess
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
EOF

log "✅ Security hardening applied"

# Summary
log "🎉 Production environment setup completed successfully!"
log "📋 Setup Summary:"
log "   - Web server: ${WEB_SERVER} configured with SSL and security headers"
log "   - PHP: Optimized for production with OPcache enabled"
log "   - Database: Optimized with performance indexes"
log "   - Security: File permissions, firewall, and hardening applied"
log "   - Monitoring: System monitoring and alerting configured"
log "   - Backups: Automated daily backup system enabled"
log ""
log "📁 Important paths:"
log "   - WHMCS: ${WHMCS_PATH}"
log "   - Logs: ${LOG_PATH}"
log "   - Backups: ${BACKUP_PATH}"
log ""
log "⚠️  Next steps:"
log "   1. Verify SSL certificate configuration"
log "   2. Test web server configuration"
log "   3. Run deployment validation tests"
log "   4. Proceed with WIDDX theme deployment"

info "Production environment setup completed. Check ${LOG_PATH}/deployment.log for detailed logs."
EOF
