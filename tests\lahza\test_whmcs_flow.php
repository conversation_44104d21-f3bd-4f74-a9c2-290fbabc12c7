<?php
/**
 * WHMCS + Lahza Payment Gateway - Complete Flow Test
 * 
 * This script simulates the complete payment flow:
 * 1. Create a test invoice
 * 2. Process payment through Lahza
 * 3. Simulate Lahza callback
 * 4. Verify invoice status update
 */

// Load configuration
$config = require __DIR__ . '/test_config.php';
require_once __DIR__ . '/mock_whmcs.php';

// Set error reporting for better debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Clear previous log file
if (file_exists(__DIR__ . '/whmcs_simulation.log')) {
    unlink(__DIR__ . '/whmcs_simulation.log');
}

// Generate test invoice data
$invoice = [
    'reference' => 'INV-' . time(),
    'amount' => '10000', // 100.00 in smallest currency unit
    'currency' => 'ILS',
    'description' => 'Test Invoice for Lahza Gateway',
    'email' => '<EMAIL>',
    'mobile' => '+966501234567',
    'first_name' => 'Test',
    'last_name' => 'User'
];

$invoiceId = str_replace('INV-', '', $invoice['reference']);

// Simulate WHMCS invoice creation
WHMCS_DB::updateInvoice($invoiceId, [
    'status' => 'Unpaid',
    'total' => $invoice['amount'] / 100, // Convert back to decimal
    'paymentmethod' => 'lahza'
]);

echo "=== WHMCS + Lahza Payment Flow Test ===\n";
echo "Invoice #$invoiceId created with status: Unpaid\n";

/**
 * Simulate Lahza Payment Process
 */
function processLahzaPayment($invoice) {
    global $config;
    $baseUrl = $config['test_mode'] ? $config['sandbox_url'] : $config['base_url'];
    $invoiceId = str_replace('INV-', '', $invoice['reference']);
    
    // Create payment request
    $paymentData = [
        'amount' => (int)$invoice['amount'],
        'currency' => $invoice['currency'],
        'reference' => $invoice['reference'],
        'description' => $invoice['description'],
        'customer' => [
            'email' => $invoice['email'],
            'name' => $invoice['first_name'] . ' ' . $invoice['last_name'],
            'phone' => $invoice['mobile']
        ],
        'metadata' => [
            'invoice_id' => $invoiceId,
            'whmcs_invoice_id' => $invoiceId
        ],
        'return_url' => rtrim($config['whmcs']['system_url'], '/') . $config['whmcs']['return_path'] . '?payment_status=success&reference=' . $invoice['reference'],
        'callback_url' => rtrim($config['whmcs']['system_url'], '/') . $config['whmcs']['callback_path']
    ];

    // Simulate successful payment
    $transactionId = 'TXN_' . time() . '_' . rand(1000, 9999);
    
    // Log the payment attempt
    logTransaction('lahza', [
        'invoiceid' => $invoiceId,
        'transid' => $transactionId,
        'amount' => $invoice['amount'] / 100,
        'status' => 'Paid'
    ], 'Success');
    
    return [
        'success' => true,
        'transaction_id' => $transactionId,
        'status' => 'success',
        'message' => 'Payment processed successfully'
    ];
}

/**
 * Simulate Lahza Callback
 */
function simulateLahzaCallback($invoice, $transactionId) {
    global $invoiceId;
    $invoiceId = str_replace('INV-', '', $invoice['reference']);
    
    // Simulate callback data from Lahza
    $callbackData = [
        'event' => 'payment.success',
        'data' => [
            'id' => $transactionId,
            'reference' => $invoice['reference'],
            'amount' => $invoice['amount'],
            'currency' => $invoice['currency'],
            'status' => 'success',
            'paid_at' => date('Y-m-d H:i:s'),
            'metadata' => [
                'invoice_id' => $invoiceId,
                'whmcs_invoice_id' => $invoiceId
            ]
        ]
    ];
    
    // Process the callback (simulate callback handler)
    if (checkCbInvoiceID($invoiceId, 'lahza') && checkCbTransID($transactionId)) {
        $result = addInvoicePayment(
            $invoiceId,
            $transactionId,
            $invoice['amount'] / 100, // Convert back to decimal
            0, // No fee
            'lahza'
        );
        
        if ($result) {
            logTransaction('lahza', [
                'invoiceid' => $invoiceId,
                'transid' => $transactionId,
                'status' => 'Paid',
                'message' => 'Callback processed successfully'
            ], 'Success');
            
            return [
                'success' => true,
                'message' => 'Payment recorded successfully',
                'invoice_status' => 'Paid'
            ];
        }
    }
    
    return [
        'success' => false,
        'message' => 'Failed to process callback'
    ];
}

// Simulate the payment process
echo "\n=== Processing Payment ===\n";
$paymentResult = processLahzaPayment($invoice);

if ($paymentResult['success']) {
    echo "✅ Payment processed successfully\n";
    echo "Transaction ID: " . $paymentResult['transaction_id'] . "\n";
    
    // Simulate callback from Lahza
    echo "\n=== Simulating Callback from Lahza ===\n";
    $callbackResult = simulateLahzaCallback($invoice, $paymentResult['transaction_id']);
    
    if ($callbackResult['success']) {
        echo "✅ Callback processed successfully\n";
        echo "Invoice status updated to: " . $callbackResult['invoice_status'] . "\n";
    } else {
        echo "❌ Callback processing failed: " . $callbackResult['message'] . "\n";
    }
    
    // Verify invoice status
    $updatedInvoice = WHMCS_DB::getInvoice(str_replace('INV-', '', $invoice['reference']));
    echo "\n=== Final Invoice Status ===\n";
    echo "Invoice #" . $updatedInvoice['id'] . "\n";
    echo "Status: " . $updatedInvoice['status'] . "\n";
    echo "Transaction ID: " . ($updatedInvoice['transid'] ?? 'N/A') . "\n";
    echo "Paid At: " . ($updatedInvoice['paid_at'] ?? 'N/A') . "\n";
    
} else {
    echo "❌ Payment failed: " . ($paymentResult['message'] ?? 'Unknown error') . "\n";
}

// Show log file
echo "\n=== Simulation Log ===\n";
if (file_exists(__DIR__ . '/whmcs_simulation.log')) {
    echo file_get_contents(__DIR__ . '/whmcs_simulation.log');
} else {
    echo "No log entries found.\n";
}

echo "\n=== Test Completed ===\n";
?>
