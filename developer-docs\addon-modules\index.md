+++
chapter = true
icon = "<i class='fa fa-file-o fa-fw'></i>"
title = "Addon Modules"
next = "/addon-modules/getting-started"
weight = 0

+++

## Introduction

Addon Modules allow you to create both admin pages and hooks to extend WHMCS further.

Addon Modules can consist of just an admin page, just hooks, or both.
They are all managed through the **Setup** > **Addon Modules** interface.

There are other types of module in WHMCS. These are [Payment Gateways][gateway-modules], [Provisioning Modules][provisioning-modules], [Mail Provider Modules][mail-providers] and [Registrar Modules][registrar-modules].

Once activated, modules will display in the **Addons** menu within the admin area for access from any page.

Management options consist of activating and deactivating of the modules.
Access control allows full admins to define which of the administrator roles can access each addon module.

We have a **Video Tutorial** demonstrating addon module management and access control [here][video-tutorial].



[gateway-modules]: ../payment-gateways "Gateway Module Documentation"
[provisioning-modules]: ../provisioning-modules "Provisioning Module Developer Documentation"
[mail-providers]: ../mail-providers "Mail Provider Module Developer Documentation"
[registrar-modules]: ../domain-registrars "Registrar Module Developer Documentation"
[video-tutorial]: https://www.youtube.com/watch?v=39TpVTs8onE "Addon Modules"
