# WIDDX Theme and Lahza Payment Gateway - Installation Guide

## 📋 Table of Contents

1. [System Requirements](#system-requirements)
2. [Pre-Installation Checklist](#pre-installation-checklist)
3. [Theme Installation](#theme-installation)
4. [Payment Gateway Installation](#payment-gateway-installation)
5. [Order Form Installation](#order-form-installation)
6. [Configuration](#configuration)
7. [Testing Installation](#testing-installation)
8. [Troubleshooting](#troubleshooting)

## 🔧 System Requirements

### Minimum Requirements
- **WHMCS Version**: 8.0 or higher (Recommended: 8.12+)
- **PHP Version**: 7.4 or higher (Recommended: 8.1+)
- **MySQL Version**: 5.7 or higher (Recommended: 8.0+)
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **SSL Certificate**: Required for payment processing
- **Memory Limit**: 256MB minimum (Recommended: 512MB+)

### Recommended Extensions
- `curl` - For API communications
- `json` - For data processing
- `openssl` - For encryption
- `mbstring` - For string handling
- `gd` or `imagick` - For image processing

### Browser Support
- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+
- **Accessibility**: Screen readers (NVDA, JAWS, VoiceOver)

## ✅ Pre-Installation Checklist

### 1. Backup Your System
```bash
# Backup WHMCS files
tar -czf whmcs_backup_$(date +%Y%m%d).tar.gz /path/to/whmcs/

# Backup database
mysqldump -u username -p database_name > whmcs_backup_$(date +%Y%m%d).sql
```

### 2. Verify Permissions
```bash
# Set proper permissions
chmod 755 /path/to/whmcs/templates/
chmod 755 /path/to/whmcs/modules/gateways/
chmod 644 /path/to/whmcs/templates/WIDDX/
```

### 3. Test Environment
- Verify WHMCS is functioning correctly
- Ensure SSL certificate is valid
- Test database connectivity
- Confirm PHP extensions are loaded

## 🎨 Theme Installation

### Step 1: Upload Theme Files

1. **Extract the WIDDX theme package**
   ```bash
   unzip widdx-theme-v1.0.0.zip
   ```

2. **Upload theme files to WHMCS**
   ```bash
   # Copy theme directory
   cp -r WIDDX/ /path/to/whmcs/templates/
   
   # Set permissions
   chmod -R 644 /path/to/whmcs/templates/WIDDX/
   chmod 755 /path/to/whmcs/templates/WIDDX/
   ```

### Step 2: Activate Theme

1. **Login to WHMCS Admin Area**
2. **Navigate to**: Setup → General Settings → General
3. **Set Template**: Select "WIDDX" from dropdown
4. **Save Changes**

### Step 3: Configure Theme Settings

1. **Navigate to**: Setup → General Settings → Ordering
2. **Configure**:
   - Order Form Template: "WIDDX Modern"
   - Enable SSL: Yes
   - Force SSL: Yes

### Step 4: Verify Installation

1. **Visit your client area**: `https://yourdomain.com/clientarea.php`
2. **Check for**:
   - WIDDX branding and colors
   - Responsive design on mobile
   - Accessibility features (skip links, focus indicators)

## 💳 Payment Gateway Installation

### Step 1: Upload Gateway Files

1. **Upload main gateway file**
   ```bash
   cp lahza.php /path/to/whmcs/modules/gateways/
   chmod 644 /path/to/whmcs/modules/gateways/lahza.php
   ```

2. **Upload callback files**
   ```bash
   cp lahza.php /path/to/whmcs/modules/gateways/callback/
   cp lahza_3ds.php /path/to/whmcs/modules/gateways/callback/
   chmod 644 /path/to/whmcs/modules/gateways/callback/lahza*.php
   ```

3. **Upload support files**
   ```bash
   mkdir -p /path/to/whmcs/modules/gateways/lahza/
   cp -r lahza/* /path/to/whmcs/modules/gateways/lahza/
   chmod -R 644 /path/to/whmcs/modules/gateways/lahza/
   ```

### Step 2: Configure Gateway

1. **Login to WHMCS Admin Area**
2. **Navigate to**: Setup → Payment Gateways
3. **Find "Lahza Payment Gateway"** and click "Activate"
4. **Configure Settings**:

   | Setting | Value | Description |
   |---------|-------|-------------|
   | Display Name | `Lahza Payment Gateway` | Name shown to customers |
   | Public Key | `pk_live_xxxxx` | Your Lahza public key |
   | Secret Key | `sk_live_xxxxx` | Your Lahza secret key |
   | Webhook Secret | `whsec_xxxxx` | Webhook verification secret |
   | Test Mode | `No` | Set to Yes for testing |
   | 3D Secure | `Yes` | Enable 3D Secure authentication |
   | 3DS Timeout | `300` | Timeout in seconds |

### Step 3: Configure Webhooks

1. **Login to Lahza Dashboard**
2. **Navigate to**: Developers → Webhooks
3. **Add Endpoint**: `https://yourdomain.com/modules/gateways/callback/lahza.php`
4. **Add 3DS Endpoint**: `https://yourdomain.com/modules/gateways/callback/lahza_3ds.php`
5. **Select Events**:
   - `payment.succeeded`
   - `payment.failed`
   - `payment.refunded`
   - `3ds.authentication.completed`

## 🛒 Order Form Installation

### Step 1: Upload Order Form

1. **Upload order form files**
   ```bash
   cp -r widdx_modern/ /path/to/whmcs/templates/orderforms/
   chmod -R 644 /path/to/whmcs/templates/orderforms/widdx_modern/
   ```

### Step 2: Configure Order Form

1. **Navigate to**: Setup → General Settings → Ordering
2. **Set Order Form Template**: "WIDDX Modern"
3. **Configure Options**:
   - Show Sidebar: Yes
   - Enable Animations: Yes
   - Progress Indicator: Yes
   - Real-time Validation: Yes

## ⚙️ Configuration

### Security Configuration

1. **SSL Configuration**
   ```apache
   # Apache .htaccess
   RewriteEngine On
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   ```

2. **Security Headers**
   ```apache
   Header always set X-Content-Type-Options nosniff
   Header always set X-Frame-Options DENY
   Header always set X-XSS-Protection "1; mode=block"
   Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
   ```

### Performance Configuration

1. **Enable Compression**
   ```apache
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/css
       AddOutputFilterByType DEFLATE application/javascript
       AddOutputFilterByType DEFLATE text/html
   </IfModule>
   ```

2. **Browser Caching**
   ```apache
   <IfModule mod_expires.c>
       ExpiresActive On
       ExpiresByType text/css "access plus 1 year"
       ExpiresByType application/javascript "access plus 1 year"
       ExpiresByType image/png "access plus 1 year"
   </IfModule>
   ```

### Database Optimization

1. **Index Optimization**
   ```sql
   -- Add indexes for better performance
   ALTER TABLE tblinvoices ADD INDEX idx_status_date (status, date);
   ALTER TABLE tblaccounts ADD INDEX idx_gateway_date (gateway, date);
   ```

## 🧪 Testing Installation

### Automated Testing

1. **Run Test Suite**
   ```bash
   cd /path/to/whmcs/tests/
   php WIDDXTestSuite.php
   ```

2. **Security Testing**
   ```bash
   php SecurityTestSuite.php
   ```

3. **Accessibility Testing**
   ```bash
   php AccessibilityTestSuite.php
   ```

### Manual Testing

1. **Theme Testing**
   - [ ] Homepage loads correctly
   - [ ] Navigation works on all devices
   - [ ] Forms are accessible
   - [ ] Colors and branding are correct

2. **Payment Testing**
   - [ ] Test payment with valid card
   - [ ] Test 3D Secure flow
   - [ ] Test payment failure scenarios
   - [ ] Verify webhook callbacks

3. **Order Form Testing**
   - [ ] Product selection works
   - [ ] Configuration options display
   - [ ] Cart functionality works
   - [ ] Checkout process completes

## 🔧 Troubleshooting

### Common Issues

#### Theme Not Displaying
```bash
# Check file permissions
ls -la /path/to/whmcs/templates/WIDDX/

# Check WHMCS logs
tail -f /path/to/whmcs/storage/logs/laravel.log
```

#### Payment Gateway Not Working
```bash
# Check gateway logs
tail -f /path/to/whmcs/modules/gateways/logs/lahza_secure.log

# Verify webhook endpoints
curl -I https://yourdomain.com/modules/gateways/callback/lahza.php
```

#### 3D Secure Issues
```bash
# Check 3DS logs
tail -f /path/to/whmcs/modules/gateways/logs/lahza_3ds.log

# Verify 3DS callback
curl -I https://yourdomain.com/modules/gateways/callback/lahza_3ds.php
```

### Log Locations

| Component | Log File |
|-----------|----------|
| Theme | `/storage/logs/laravel.log` |
| Payment Gateway | `/modules/gateways/logs/lahza_secure.log` |
| 3D Secure | `/modules/gateways/logs/lahza_3ds.log` |
| Transactions | `/modules/gateways/logs/transactions/` |
| Security | `/modules/gateways/logs/lahza_security.log` |

### Support Resources

- **Documentation**: `/docs/`
- **Test Reports**: `/tests/reports/`
- **Log Analysis**: Use provided log analysis tools
- **Community**: WHMCS Community Forums
- **Professional Support**: Contact WIDDX support team

## 📞 Getting Help

If you encounter issues during installation:

1. **Check the logs** for specific error messages
2. **Run the test suite** to identify problems
3. **Review the troubleshooting section**
4. **Contact support** with detailed error information

---

**Next Steps**: After successful installation, proceed to the [Configuration Guide](CONFIGURATION.md) for detailed setup instructions.

## 📋 Installation Checklist

### Pre-Installation ✅
- [ ] System requirements verified
- [ ] WHMCS backup completed
- [ ] Database backup completed
- [ ] SSL certificate installed
- [ ] File permissions set correctly

### Theme Installation ✅
- [ ] Theme files uploaded
- [ ] Theme activated in WHMCS
- [ ] Theme settings configured
- [ ] Client area tested
- [ ] Responsive design verified

### Payment Gateway Installation ✅
- [ ] Gateway files uploaded
- [ ] Gateway activated in WHMCS
- [ ] API credentials configured
- [ ] Webhooks configured
- [ ] Test payment completed

### Order Form Installation ✅
- [ ] Order form files uploaded
- [ ] Order form activated
- [ ] Order form settings configured
- [ ] Order process tested
- [ ] Cart functionality verified

### Testing & Validation ✅
- [ ] Automated tests passed
- [ ] Security tests passed
- [ ] Accessibility tests passed
- [ ] Manual testing completed
- [ ] Performance verified

### Go-Live Preparation ✅
- [ ] Production credentials configured
- [ ] Monitoring setup completed
- [ ] Backup procedures verified
- [ ] Support contacts established
- [ ] Documentation reviewed
