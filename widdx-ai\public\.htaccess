Options -Indexes +FollowSymLinks
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Force HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Handle API requests
    RewriteCond %{REQUEST_URI} ^/widdx-ai/public/api/ [NC]
    RewriteRule ^api/(.*)$ /widdx-ai/api/$1 [L,QSA]
    
    # Prevent direct access to sensitive files
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteCond %{REQUEST_FILENAME} \.(env|ini|json|yml|yaml|php)$ [NC]
    RewriteRule ^.*$ - [F,L]
    
    # Handle frontend routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.html [L]
</IfModule>

# Secure API access
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
    Allow from 127.0.0.1
    Allow from ::1
    
    # Add security headers
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self'"
</FilesMatch>

# Block directory listing and prevent directory traversal
Options -Indexes -MultiViews

# Prevent directory traversal attacks
<FilesMatch "^\.(htaccess|htpasswd|env|ini|json|yml|yaml|php)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Set security headers
Header set X-Frame-Options "SAMEORIGIN"
Header set X-XSS-Protection "1; mode=block"
Header set X-Content-Type-Options "nosniff"
Header set Strict-Transport-Security "max-age=31536000; includeSubDomains"

# Cache control
<FilesMatch "\.(js|css|png|jpg|jpeg|gif|svg)$">
    Header set Cache-Control "public, max-age=31536000"
</FilesMatch>
