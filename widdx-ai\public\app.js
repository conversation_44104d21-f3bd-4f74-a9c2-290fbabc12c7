document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.getElementById('chat-messages');
    const questionInput = document.getElementById('question-input');
    const askButton = document.getElementById('ask-button');
    const typingIndicator = document.getElementById('typing-indicator');
    
    // Auto-resize textarea
    questionInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Handle Enter key
    questionInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            askQuestion();
        }
    });
    
    // Handle send button click
    askButton.addEventListener('click', askQuestion);
    
    function askQuestion() {
        const question = questionInput.value.trim();
        if (!question) return;
        
        // Add user message to chat
        addMessage('user', question);
        
        // Clear input
        questionInput.value = '';
        questionInput.style.height = 'auto';
        
        // Show typing indicator
        typingIndicator.style.display = 'flex';
        
        // Disable input and button while waiting for response
        questionInput.disabled = true;
        askButton.disabled = true;
        
        // Scroll to bottom
        scrollToBottom();
        
        // Send question to server
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        fetch('/widdx-ai/api/ask.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin',
            body: JSON.stringify({ 
                question: question,
                timestamp: Date.now(),
                userAgent: navigator.userAgent
            })
        })
        .then(response => {
            if (!response.ok) {
                const error = new Error(response.statusText);
                error.response = response;
                throw error;
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                const source = data.data.source || 'AI';
                const isCached = data.data.cached || false;
                addMessage('ai', data.data.answer, source, isCached);
            } else {
                throw new Error(data.message || 'حدث خطأ غير معروف');
            }
        })
        .catch(error => {
            console.error('API Error:', error);
            
            // Don't show sensitive error details in production
            if (window.location.hostname !== 'localhost') {
                addMessage('ai', 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقاً.', 'SYSTEM');
            } else {
                addMessage('ai', 'Error: ' + error.message, 'SYSTEM');
            }
        })
        .finally(() => {
            // Hide typing indicator
            typingIndicator.style.display = 'none';
            
            // Re-enable input and button
            questionInput.disabled = false;
            askButton.disabled = false;
            questionInput.focus();
            
            // Scroll to bottom
            scrollToBottom();
        });
    }
    
    function addMessage(sender, text, source = null, isCached = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${sender}`;
        
        const bubbleDiv = document.createElement('div');
        bubbleDiv.className = `message-bubble ${sender}-bubble`;
        
        // Sanitize text
        bubbleDiv.textContent = text;
        
        // Add loading state for long messages
        if (text.length > 200) {
            bubbleDiv.classList.add('loading');
            setTimeout(() => {
                bubbleDiv.classList.remove('loading');
            }, 1000);
        }
        
        messageDiv.appendChild(bubbleDiv);
        
        if (sender === 'ai' && source) {
            const infoDiv = document.createElement('div');
            infoDiv.className = 'message-info';
            
            const sourceSpan = document.createElement('span');
            sourceSpan.className = `source-tag ${isCached ? 'cached-tag' : ''}`;
            sourceSpan.textContent = isCached ? `من التخزين (${source})` : source;
            
            infoDiv.appendChild(document.createTextNode('المصدر: '));
            infoDiv.appendChild(sourceSpan);
            messageDiv.appendChild(infoDiv);
        }
        
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }
    
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // Focus input on load
    questionInput.focus();
});
