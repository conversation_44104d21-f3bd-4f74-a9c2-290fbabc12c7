#!/bin/bash
#
# WIDDX Monitoring Validation Script
# Confirm all monitoring dashboards and alerting are functioning correctly
#
# @package    WIDDX Production Finalization
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration
MONITORING_PATH="/var/monitoring/widdx"
LOG_PATH="/var/log/widdx-deployment"
DOMAIN="yourdomain.com"
ALERT_EMAIL="<EMAIL>"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
MONITORING_ISSUES=()

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/monitoring-validation.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/monitoring-validation.log"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/monitoring-validation.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/monitoring-validation.log"
}

# Test execution function
run_monitoring_test() {
    local test_name="$1"
    local test_command="$2"
    local is_critical="${3:-true}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Testing ${test_name}... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log "✅ ${test_name}: PASSED"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        if [[ "$is_critical" == "true" ]]; then
            MONITORING_ISSUES+=("CRITICAL: ${test_name}")
            error "❌ ${test_name}: CRITICAL FAILURE"
        else
            MONITORING_ISSUES+=("WARNING: ${test_name}")
            warning "⚠️ ${test_name}: WARNING"
        fi
        return 1
    fi
}

# Create log directory
mkdir -p "${LOG_PATH}"

log "📊 Starting WIDDX Monitoring Validation"

echo ""
echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                                                                              ║${NC}"
echo -e "${PURPLE}║                        📊 MONITORING VALIDATION 📊                          ║${NC}"
echo -e "${PURPLE}║                                                                              ║${NC}"
echo -e "${PURPLE}║  Validating all monitoring systems, dashboards, and alerting                ║${NC}"
echo -e "${PURPLE}║                                                                              ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""

# 1. Monitoring Infrastructure Tests
log "🏗️ Phase 1: Monitoring Infrastructure"

run_monitoring_test "Monitoring Directory Structure" "test -d ${MONITORING_PATH}" true
run_monitoring_test "System Health Script" "test -x ${MONITORING_PATH}/system-health.sh" true
run_monitoring_test "Payment Monitor Script" "test -x ${MONITORING_PATH}/payment-monitor.sh" true
run_monitoring_test "Performance Monitor Script" "test -x ${MONITORING_PATH}/performance-monitor.sh" true
run_monitoring_test "Security Monitor Script" "test -x ${MONITORING_PATH}/security-monitor.sh" true
run_monitoring_test "Log Analyzer Script" "test -x ${MONITORING_PATH}/log-analyzer.sh" true

# 2. Cron Job Validation
log "⏰ Phase 2: Cron Job Validation"

run_monitoring_test "System Health Cron Job" "crontab -l | grep -q 'system-health.sh'" true
run_monitoring_test "Payment Monitor Cron Job" "crontab -l | grep -q 'payment-monitor.sh'" true
run_monitoring_test "Performance Monitor Cron Job" "crontab -l | grep -q 'performance-monitor.sh'" true
run_monitoring_test "Security Monitor Cron Job" "crontab -l | grep -q 'security-monitor.sh'" true
run_monitoring_test "Log Analyzer Cron Job" "crontab -l | grep -q 'log-analyzer.sh'" true

# 3. Log File Generation Tests
log "📝 Phase 3: Log File Generation"

# Run monitoring scripts to generate initial logs
"${MONITORING_PATH}/system-health.sh" || true
"${MONITORING_PATH}/payment-monitor.sh" || true
"${MONITORING_PATH}/performance-monitor.sh" || true
"${MONITORING_PATH}/security-monitor.sh" || true

# Wait a moment for logs to be written
sleep 2

run_monitoring_test "Health Log Generation" "test -f ${MONITORING_PATH}/health.log" true
run_monitoring_test "Payment Log Generation" "test -f ${MONITORING_PATH}/payments.log" true
run_monitoring_test "Performance Log Generation" "test -f ${MONITORING_PATH}/performance.log" true
run_monitoring_test "Security Log Generation" "test -f ${MONITORING_PATH}/security.log" true

# 4. Monitoring Script Functionality
log "🔧 Phase 4: Script Functionality"

# Test system health monitoring
run_monitoring_test "System Health Check Execution" "${MONITORING_PATH}/system-health.sh" false

# Test payment monitoring
run_monitoring_test "Payment Monitor Execution" "${MONITORING_PATH}/payment-monitor.sh" false

# Test performance monitoring
run_monitoring_test "Performance Monitor Execution" "${MONITORING_PATH}/performance-monitor.sh" false

# Test security monitoring
run_monitoring_test "Security Monitor Execution" "${MONITORING_PATH}/security-monitor.sh" false

# 5. Dashboard Validation
log "📊 Phase 5: Dashboard Validation"

run_monitoring_test "Monitoring Dashboard File" "test -f ${MONITORING_PATH}/dashboard.html" true

# Test dashboard accessibility
if [[ -f "${MONITORING_PATH}/dashboard.html" ]]; then
    # Copy dashboard to web directory for testing
    cp "${MONITORING_PATH}/dashboard.html" "/var/www/html/monitoring-dashboard.html"
    chmod 644 "/var/www/html/monitoring-dashboard.html"
    
    run_monitoring_test "Dashboard Web Accessibility" "curl -f -s -o /dev/null https://${DOMAIN}/monitoring-dashboard.html" false
else
    MONITORING_ISSUES+=("WARNING: Dashboard file not found")
fi

# 6. Alert System Tests
log "🚨 Phase 6: Alert System Tests"

# Test email configuration
run_monitoring_test "Mail System Available" "command -v mail" false

# Create test alert
if command -v mail >/dev/null 2>&1; then
    echo "WIDDX Monitoring Test Alert - $(date)" | mail -s "WIDDX Test Alert" "${ALERT_EMAIL}" 2>/dev/null || true
    run_monitoring_test "Test Alert Sent" "echo 'Test alert sent to ${ALERT_EMAIL}'" false
else
    warning "Mail system not available - alerts will be logged only"
fi

# 7. Real-time Monitoring Tests
log "⚡ Phase 7: Real-time Monitoring"

# Test website monitoring
run_monitoring_test "Website Health Check" "curl -f -s -o /dev/null https://${DOMAIN}/" true

# Test SSL certificate monitoring
run_monitoring_test "SSL Certificate Check" "echo | openssl s_client -servername ${DOMAIN} -connect ${DOMAIN}:443 2>/dev/null | openssl x509 -checkend 86400 -noout" true

# Test database connectivity
WHMCS_PATH="/var/www/html/whmcs"
if [[ -f "${WHMCS_PATH}/configuration.php" ]]; then
    DB_HOST=$(grep -oP "(?<=\\\$db_host = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "localhost")
    DB_NAME=$(grep -oP "(?<=\\\$db_name = ')[^']*" "${WHMCS_PATH}/configuration.php")
    DB_USER=$(grep -oP "(?<=\\\$db_username = ')[^']*" "${WHMCS_PATH}/configuration.php")
    DB_PASS=$(grep -oP "(?<=\\\$db_password = ')[^']*" "${WHMCS_PATH}/configuration.php")
    
    run_monitoring_test "Database Connectivity Check" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT 1;'" true
fi

# 8. Performance Monitoring Tests
log "⚡ Phase 8: Performance Monitoring"

# Test response time monitoring
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "https://${DOMAIN}/")
run_monitoring_test "Response Time Measurement" "echo '${RESPONSE_TIME} > 0' | bc -l | grep -q 1" true

# Test resource monitoring
run_monitoring_test "CPU Usage Monitoring" "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1 | grep -E '^[0-9]+\.?[0-9]*$'" true
run_monitoring_test "Memory Usage Monitoring" "free | grep Mem | awk '{printf(\"%.2f\", \$3/\$2 * 100.0)}' | grep -E '^[0-9]+\.?[0-9]*$'" true
run_monitoring_test "Disk Usage Monitoring" "df -h / | awk 'NR==2{print \$5}' | cut -d'%' -f1 | grep -E '^[0-9]+$'" true

# 9. Security Monitoring Tests
log "🔒 Phase 9: Security Monitoring"

# Test log analysis for security events
run_monitoring_test "Security Log Analysis" "test -f /var/log/auth.log || test -f /var/log/secure" false

# Test failed login detection
run_monitoring_test "Failed Login Detection" "grep -c 'Failed password' /var/log/auth.log 2>/dev/null || echo '0' | grep -E '^[0-9]+$'" false

# Test file integrity monitoring
run_monitoring_test "File Integrity Check" "find ${WHMCS_PATH} -name '*.php' -type f | head -5 | xargs ls -la" true

# 10. Log Rotation and Cleanup Tests
log "🔄 Phase 10: Log Rotation Tests"

# Test log rotation functionality
run_monitoring_test "Log Rotation Script" "${MONITORING_PATH}/log-analyzer.sh" false

# Test log cleanup
run_monitoring_test "Old Log Cleanup" "find ${MONITORING_PATH} -name '*.log.*' -mtime +30 | wc -l | grep -E '^[0-9]+$'" false

# 11. Integration Tests
log "🔗 Phase 11: Integration Tests"

# Test monitoring integration with payment system
GATEWAY_LOG_PATH="/var/www/html/whmcs/modules/gateways/logs"
run_monitoring_test "Gateway Log Directory" "test -d ${GATEWAY_LOG_PATH}" true

# Test monitoring integration with WHMCS
run_monitoring_test "WHMCS Log Integration" "test -f /var/www/html/whmcs/storage/logs/laravel.log || test -d /var/www/html/whmcs/storage/logs/" false

# 12. Generate Monitoring Report
log "📊 Generating monitoring validation report..."

cat > "${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt" << EOF
WIDDX Monitoring Validation Report
=================================
Validation ID: ${TIMESTAMP}
Completed: $(date)
Domain: ${DOMAIN}

MONITORING VALIDATION SUMMARY:
Total Tests: ${TOTAL_TESTS}
Passed: ${PASSED_TESTS}
Failed: ${FAILED_TESTS}
Success Rate: $(echo "scale=2; ${PASSED_TESTS} * 100 / ${TOTAL_TESTS}" | bc)%

MONITORING COMPONENTS STATUS:
✅ System Health Monitoring: $([ -x "${MONITORING_PATH}/system-health.sh" ] && echo "ACTIVE" || echo "INACTIVE")
✅ Payment Gateway Monitoring: $([ -x "${MONITORING_PATH}/payment-monitor.sh" ] && echo "ACTIVE" || echo "INACTIVE")
✅ Performance Monitoring: $([ -x "${MONITORING_PATH}/performance-monitor.sh" ] && echo "ACTIVE" || echo "INACTIVE")
✅ Security Monitoring: $([ -x "${MONITORING_PATH}/security-monitor.sh" ] && echo "ACTIVE" || echo "INACTIVE")
✅ Log Analysis: $([ -x "${MONITORING_PATH}/log-analyzer.sh" ] && echo "ACTIVE" || echo "INACTIVE")

CRON JOBS STATUS:
$(crontab -l | grep -c "monitoring\|system-health\|payment-monitor\|performance-monitor\|security-monitor\|log-analyzer" || echo "0") monitoring jobs scheduled

DASHBOARD STATUS:
$([ -f "${MONITORING_PATH}/dashboard.html" ] && echo "✅ Dashboard Available" || echo "❌ Dashboard Missing")
$(curl -f -s -o /dev/null "https://${DOMAIN}/monitoring-dashboard.html" 2>/dev/null && echo "✅ Dashboard Accessible" || echo "❌ Dashboard Not Accessible")

ALERT SYSTEM STATUS:
$(command -v mail >/dev/null 2>&1 && echo "✅ Email Alerts Available" || echo "⚠️ Email Alerts Not Available")
Alert Email: ${ALERT_EMAIL}

REAL-TIME MONITORING STATUS:
Website Health: $(curl -f -s -o /dev/null "https://${DOMAIN}/" && echo "✅ HEALTHY" || echo "❌ UNHEALTHY")
SSL Certificate: $(echo | openssl s_client -servername ${DOMAIN} -connect ${DOMAIN}:443 2>/dev/null | openssl x509 -checkend 86400 -noout && echo "✅ VALID" || echo "❌ EXPIRING")
Database: $(mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'SELECT 1;' >/dev/null 2>&1 && echo "✅ CONNECTED" || echo "❌ DISCONNECTED")

PERFORMANCE METRICS:
Response Time: ${RESPONSE_TIME}s
CPU Usage: $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1)%
Memory Usage: $(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')%
Disk Usage: $(df -h / | awk 'NR==2{print $5}')

ISSUES DETECTED:
EOF

if [[ ${#MONITORING_ISSUES[@]} -eq 0 ]]; then
    echo "None - All monitoring systems functioning correctly ✅" >> "${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt"
else
    for issue in "${MONITORING_ISSUES[@]}"; do
        echo "- ${issue}" >> "${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt"
    done
fi

cat >> "${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt" << EOF

MONITORING READINESS:
EOF

if [[ ${#MONITORING_ISSUES[@]} -eq 0 ]] || [[ $FAILED_TESTS -lt 3 ]]; then
    cat >> "${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt" << EOF
✅ MONITORING SYSTEMS READY
All critical monitoring components are functioning correctly.

RECOMMENDATIONS:
1. Monitor system performance for the first 24 hours
2. Review alert thresholds and adjust as needed
3. Set up additional custom alerts based on business requirements
4. Schedule regular monitoring system maintenance
5. Test alert delivery periodically

NEXT STEPS:
1. Perform final smoke tests
2. Authorize go-live
3. Begin continuous monitoring
EOF
else
    cat >> "${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt" << EOF
❌ MONITORING SYSTEMS NEED ATTENTION
Critical monitoring issues detected that should be addressed.

REQUIRED ACTIONS:
EOF
    for issue in "${MONITORING_ISSUES[@]}"; do
        if [[ "$issue" =~ "CRITICAL" ]]; then
            echo "- Fix: ${issue}" >> "${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt"
        fi
    done
fi

# Summary
echo ""
echo "=" . str_repeat("=", 80)
log "📊 MONITORING VALIDATION COMPLETED"
echo "=" . str_repeat("=", 80)

echo ""
echo "📊 VALIDATION SUMMARY:"
echo "   Total Tests: ${TOTAL_TESTS}"
echo "   Passed: ${PASSED_TESTS}"
echo "   Failed: ${FAILED_TESTS}"
echo "   Success Rate: $(echo "scale=2; ${PASSED_TESTS} * 100 / ${TOTAL_TESTS}" | bc)%"

echo ""
echo "📊 MONITORING STATUS:"
echo "   System Health: $([ -x "${MONITORING_PATH}/system-health.sh" ] && echo "✅ ACTIVE" || echo "❌ INACTIVE")"
echo "   Payment Monitoring: $([ -x "${MONITORING_PATH}/payment-monitor.sh" ] && echo "✅ ACTIVE" || echo "❌ INACTIVE")"
echo "   Performance Monitoring: $([ -x "${MONITORING_PATH}/performance-monitor.sh" ] && echo "✅ ACTIVE" || echo "❌ INACTIVE")"
echo "   Security Monitoring: $([ -x "${MONITORING_PATH}/security-monitor.sh" ] && echo "✅ ACTIVE" || echo "❌ INACTIVE")"
echo "   Dashboard: $([ -f "${MONITORING_PATH}/dashboard.html" ] && echo "✅ AVAILABLE" || echo "❌ MISSING")"

if [[ ${#MONITORING_ISSUES[@]} -eq 0 ]] || [[ $FAILED_TESTS -lt 3 ]]; then
    echo ""
    echo -e "${GREEN}✅ MONITORING STATUS: READY FOR PRODUCTION${NC}"
    echo ""
    echo "🎉 All critical monitoring systems validated!"
    echo "📊 Real-time monitoring active"
    echo "🚨 Alert systems configured"
    echo "📈 Performance tracking enabled"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Perform final smoke tests"
    echo "   2. Authorize go-live"
    echo "   3. Begin continuous monitoring"
else
    echo ""
    echo -e "${RED}❌ MONITORING STATUS: ISSUES DETECTED${NC}"
    echo ""
    echo "🚨 Issues detected:"
    for issue in "${MONITORING_ISSUES[@]}"; do
        echo "   - ${issue}"
    done
    echo ""
    echo "📋 Required Actions:"
    echo "   1. Address critical monitoring issues"
    echo "   2. Re-run monitoring validation"
    echo "   3. Verify all systems before go-live"
fi

echo ""
echo "📄 Detailed report: ${LOG_PATH}/monitoring_validation_${TIMESTAMP}.txt"
echo "📋 Validation logs: ${LOG_PATH}/monitoring-validation.log"
echo "📊 Dashboard: https://${DOMAIN}/monitoring-dashboard.html"

info "Monitoring validation completed. Check ${LOG_PATH}/monitoring-validation.log for detailed logs."
EOF
