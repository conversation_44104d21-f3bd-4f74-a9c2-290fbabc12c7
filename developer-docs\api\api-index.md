+++
title = "API Index"
toc = true
weight = 100

+++

<div class="row"><div class="col-sm-6">
<h3>Orders</h3>

<ul><li> <a href="/api-reference/acceptorder/">AcceptOrder</a>
<li> <a href="/api-reference/addorder/">AddOrder</a>
<li> <a href="/api-reference/cancelorder/">CancelOrder</a>
<li> <a href="/api-reference/deleteorder/">DeleteOrder</a>
<li> <a href="/api-reference/fraudorder/">FraudOrder</a>
<li> <a href="/api-reference/getorders/">GetOrders</a>
<li> <a href="/api-reference/getorderstatuses/">GetOrderStatuses</a>
<li> <a href="/api-reference/getproducts/">GetProducts</a>
<li> <a href="/api-reference/getpromotions/">GetPromotions</a>
<li> <a href="/api-reference/orderfraudcheck/">OrderFraudCheck</a>
<li> <a href="/api-reference/pendingorder/">PendingOrder</a>
</ul>
<h3>Billing</h3>

<ul><li> <a href="/api-reference/acceptquote/">AcceptQuote</a>
<li> <a href="/api-reference/addbillableitem/">AddBillableItem</a>
<li> <a href="/api-reference/addcredit/">AddCredit</a>
<li> <a href="/api-reference/addinvoicepayment/">AddInvoicePayment</a>
<li> <a href="/api-reference/addpaymethod/">AddPayMethod</a>
<li> <a href="/api-reference/addtransaction/">AddTransaction</a>
<li> <a href="/api-reference/applycredit/">ApplyCredit</a>
<li> <a href="/api-reference/capturepayment/">CapturePayment</a>
<li> <a href="/api-reference/createinvoice/">CreateInvoice</a>
<li> <a href="/api-reference/createquote/">CreateQuote</a>
<li> <a href="/api-reference/deletepaymethod/">DeletePayMethod</a>
<li> <a href="/api-reference/deletequote/">DeleteQuote</a>
<li> <a href="/api-reference/geninvoices/">GenInvoices</a>
<li> <a href="/api-reference/getcredits/">GetCredits</a>
<li> <a href="/api-reference/getinvoice/">GetInvoice</a>
<li> <a href="/api-reference/getinvoices/">GetInvoices</a>
<li> <a href="/api-reference/getpaymethods/">GetPayMethods</a>
<li> <a href="/api-reference/getquotes/">GetQuotes</a>
<li> <a href="/api-reference/gettransactions/">GetTransactions</a>
<li> <a href="/api-reference/sendquote/">SendQuote</a>
<li> <a href="/api-reference/updateinvoice/">UpdateInvoice</a>
<li> <a href="/api-reference/updatepaymethod/">UpdatePayMethod</a>
<li> <a href="/api-reference/updatequote/">UpdateQuote</a>
<li> <a href="/api-reference/updatetransaction/">UpdateTransaction</a>
</ul>
<h3>Module</h3>

<ul><li> <a href="/api-reference/activatemodule/">ActivateModule</a>
<li> <a href="/api-reference/deactivatemodule/">DeactivateModule</a>
<li> <a href="/api-reference/getmoduleconfigurationparameters/">GetModuleConfigurationParameters</a>
<li> <a href="/api-reference/getmodulequeue/">GetModuleQueue</a>
<li> <a href="/api-reference/updatemoduleconfiguration/">UpdateModuleConfiguration</a>
</ul>
<h3>Support</h3>

<ul><li> <a href="/api-reference/addannouncement/">AddAnnouncement</a>
<li> <a href="/api-reference/addcancelrequest/">AddCancelRequest</a>
<li> <a href="/api-reference/addclientnote/">AddClientNote</a>
<li> <a href="/api-reference/addticketnote/">AddTicketNote</a>
<li> <a href="/api-reference/addticketreply/">AddTicketReply</a>
<li> <a href="/api-reference/blockticketsender/">BlockTicketSender</a>
<li> <a href="/api-reference/deleteannouncement/">DeleteAnnouncement</a>
<li> <a href="/api-reference/deleteticket/">DeleteTicket</a>
<li> <a href="/api-reference/deleteticketnote/">DeleteTicketNote</a>
<li> <a href="/api-reference/deleteticketreply/">DeleteTicketReply</a>
<li> <a href="/api-reference/getannouncements/">GetAnnouncements</a>
<li> <a href="/api-reference/mergeticket/">MergeTicket</a>
<li> <a href="/api-reference/openticket/">OpenTicket</a>
<li> <a href="/api-reference/updateticket/">UpdateTicket</a>
<li> <a href="/api-reference/updateticketreply/">UpdateTicketReply</a>
</ul>
<h3>System</h3>

<ul><li> <a href="/api-reference/addbannedip/">AddBannedIp</a>
<li> <a href="/api-reference/decryptpassword/">DecryptPassword</a>
<li> <a href="/api-reference/encryptpassword/">EncryptPassword</a>
<li> <a href="/api-reference/getactivitylog/">GetActivityLog</a>
<li> <a href="/api-reference/getadmindetails/">GetAdminDetails</a>
<li> <a href="/api-reference/getadminusers/">GetAdminUsers</a>
<li> <a href="/api-reference/getautomationlog/">GetAutomationLog</a>
<li> <a href="/api-reference/getconfigurationvalue/">GetConfigurationValue</a>
<li> <a href="/api-reference/getcurrencies/">GetCurrencies</a>
<li> <a href="/api-reference/getemailtemplates/">GetEmailTemplates</a>
<li> <a href="/api-reference/getpaymentmethods/">GetPaymentMethods</a>
<li> <a href="/api-reference/getstaffonline/">GetStaffOnline</a>
<li> <a href="/api-reference/getstats/">GetStats</a>
<li> <a href="/api-reference/gettodoitems/">GetToDoItems</a>
<li> <a href="/api-reference/gettodoitemstatuses/">GetToDoItemStatuses</a>
<li> <a href="/api-reference/logactivity/">LogActivity</a>
<li> <a href="/api-reference/sendadminemail/">SendAdminEmail</a>
<li> <a href="/api-reference/sendemail/">SendEmail</a>
<li> <a href="/api-reference/setconfigurationvalue/">SetConfigurationValue</a>
<li> <a href="/api-reference/triggernotificationevent/">TriggerNotificationEvent</a>
<li> <a href="/api-reference/updateadminnotes/">UpdateAdminNotes</a>
<li> <a href="/api-reference/updateannouncement/">UpdateAnnouncement</a>
<li> <a href="/api-reference/updatetodoitem/">UpdateToDoItem</a>
<li> <a href="/api-reference/whmcsdetails/">WhmcsDetails</a>
</ul>
<h3>Client</h3>

<ul><li> <a href="/api-reference/addclient/">AddClient</a>
<li> <a href="/api-reference/addcontact/">AddContact</a>
<li> <a href="/api-reference/closeclient/">CloseClient</a>
<li> <a href="/api-reference/deleteclient/">DeleteClient</a>
<li> <a href="/api-reference/deletecontact/">DeleteContact</a>
<li> <a href="/api-reference/getcancelledpackages/">GetCancelledPackages</a>
<li> <a href="/api-reference/getclientgroups/">GetClientGroups</a>
<li> <a href="/api-reference/getclientpassword/">GetClientPassword</a>
<li> <a href="/api-reference/getclients/">GetClients</a>
<li> <a href="/api-reference/getclientsaddons/">GetClientsAddons</a>
<li> <a href="/api-reference/getclientsdetails/">GetClientsDetails</a>
<li> <a href="/api-reference/getclientsdomains/">GetClientsDomains</a>
<li> <a href="/api-reference/getclientsproducts/">GetClientsProducts</a>
<li> <a href="/api-reference/getcontacts/">GetContacts</a>
<li> <a href="/api-reference/getemails/">GetEmails</a>
<li> <a href="/api-reference/updateclient/">UpdateClient</a>
<li> <a href="/api-reference/updatecontact/">UpdateContact</a>
</ul>
<h3>Products</h3>

<ul><li> <a href="/api-reference/addproduct/">AddProduct</a>
</ul>
<h3>Project Management</h3>

<ul><li> <a href="/api-reference/addprojectmessage/">AddProjectMessage</a>
<li> <a href="/api-reference/addprojecttask/">AddProjectTask</a>
<li> <a href="/api-reference/createproject/">CreateProject</a>
<li> <a href="/api-reference/deleteprojecttask/">DeleteProjectTask</a>
<li> <a href="/api-reference/endtasktimer/">EndTaskTimer</a>
<li> <a href="/api-reference/getproject/">GetProject</a>
<li> <a href="/api-reference/getprojects/">GetProjects</a>
<li> <a href="/api-reference/starttasktimer/">StartTaskTimer</a>
<li> <a href="/api-reference/updateproject/">UpdateProject</a>
<li> <a href="/api-reference/updateprojecttask/">UpdateProjectTask</a>
</ul>
</div><div class="col-sm-6"><h3>Users</h3>

<ul><li> <a href="/api-reference/adduser/">AddUser</a>
<li> <a href="/api-reference/createclientinvite/">CreateClientInvite</a>
<li> <a href="/api-reference/deleteuserclient/">DeleteUserClient</a>
<li> <a href="/api-reference/getpermissionslist/">GetPermissionsList</a>
<li> <a href="/api-reference/getuserpermissions/">GetUserPermissions</a>
<li> <a href="/api-reference/getusers/">GetUsers</a>
<li> <a href="/api-reference/resetpassword/">ResetPassword</a>
<li> <a href="/api-reference/updateuser/">UpdateUser</a>
<li> <a href="/api-reference/updateuserpermissions/">UpdateUserPermissions</a>
</ul>
<h3>Affiliates</h3>

<ul><li> <a href="/api-reference/affiliateactivate/">AffiliateActivate</a>
<li> <a href="/api-reference/getaffiliates/">GetAffiliates</a>
</ul>
<h3>Authentication</h3>

<ul><li> <a href="/api-reference/createoauthcredential/">CreateOAuthCredential</a>
<li> <a href="/api-reference/createssotoken/">CreateSsoToken</a>
<li> <a href="/api-reference/deleteoauthcredential/">DeleteOAuthCredential</a>
<li> <a href="/api-reference/listoauthcredentials/">ListOAuthCredentials</a>
<li> <a href="/api-reference/updateoauthcredential/">UpdateOAuthCredential</a>
<li> <a href="/api-reference/validatelogin/">ValidateLogin</a>
</ul>
<h3>Domains</h3>

<ul><li> <a href="/api-reference/createorupdatetld/">CreateOrUpdateTLD</a>
<li> <a href="/api-reference/domaingetlockingstatus/">DomainGetLockingStatus</a>
<li> <a href="/api-reference/domaingetnameservers/">DomainGetNameservers</a>
<li> <a href="/api-reference/domaingetwhoisinfo/">DomainGetWhoisInfo</a>
<li> <a href="/api-reference/domainregister/">DomainRegister</a>
<li> <a href="/api-reference/domainrelease/">DomainRelease</a>
<li> <a href="/api-reference/domainrenew/">DomainRenew</a>
<li> <a href="/api-reference/domainrequestepp/">DomainRequestEPP</a>
<li> <a href="/api-reference/domaintoggleidprotect/">DomainToggleIdProtect</a>
<li> <a href="/api-reference/domaintransfer/">DomainTransfer</a>
<li> <a href="/api-reference/domainupdatelockingstatus/">DomainUpdateLockingStatus</a>
<li> <a href="/api-reference/domainupdatenameservers/">DomainUpdateNameservers</a>
<li> <a href="/api-reference/domainupdatewhoisinfo/">DomainUpdateWhoisInfo</a>
<li> <a href="/api-reference/domainwhois/">DomainWhois</a>
<li> <a href="/api-reference/getregistrars/">GetRegistrars</a>
<li> <a href="/api-reference/gettldpricing/">GetTLDPricing</a>
<li> <a href="/api-reference/updateclientdomain/">UpdateClientDomain</a>
</ul>
<h3>Servers</h3>

<ul><li> <a href="/api-reference/gethealthstatus/">GetHealthStatus</a>
<li> <a href="/api-reference/getservers/">GetServers</a>
</ul>
<h3>Tickets</h3>

<ul><li> <a href="/api-reference/getsupportdepartments/">GetSupportDepartments</a>
<li> <a href="/api-reference/getsupportstatuses/">GetSupportStatuses</a>
<li> <a href="/api-reference/getticket/">GetTicket</a>
<li> <a href="/api-reference/getticketattachment/">GetTicketAttachment</a>
<li> <a href="/api-reference/getticketcounts/">GetTicketCounts</a>
<li> <a href="/api-reference/getticketnotes/">GetTicketNotes</a>
<li> <a href="/api-reference/getticketpredefinedcats/">GetTicketPredefinedCats</a>
<li> <a href="/api-reference/getticketpredefinedreplies/">GetTicketPredefinedReplies</a>
<li> <a href="/api-reference/gettickets/">GetTickets</a>
</ul>
<h3>Service</h3>

<ul><li> <a href="/api-reference/modulechangepackage/">ModuleChangePackage</a>
<li> <a href="/api-reference/modulechangepw/">ModuleChangePw</a>
<li> <a href="/api-reference/modulecreate/">ModuleCreate</a>
<li> <a href="/api-reference/modulecustom/">ModuleCustom</a>
<li> <a href="/api-reference/modulesuspend/">ModuleSuspend</a>
<li> <a href="/api-reference/moduleterminate/">ModuleTerminate</a>
<li> <a href="/api-reference/moduleunsuspend/">ModuleUnsuspend</a>
<li> <a href="/api-reference/updateclientproduct/">UpdateClientProduct</a>
<li> <a href="/api-reference/upgradeproduct/">UpgradeProduct</a>
</ul>
<h3>Addons</h3>

<ul><li> <a href="/api-reference/updateclientaddon/">UpdateClientAddon</a>
</ul>
</div></div>