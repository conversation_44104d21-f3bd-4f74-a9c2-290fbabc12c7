+++
chapter = true
icon = "<i class='fa fa-language fa-fw'></i>"
next = "/languages/adding-a-language/"
title = "Languages"
weight = 0

+++

## Introduction

WHMCS is fully multi-lingual and supports over 25 different languages out of the box.

Words and phrases are defined in language files stored in the `lang` directories within both the admin and client areas.

If you wish to customise any of the language strings, we recommend using [Overrides](/languages/overrides/).

{{% notice tip %}}
If you spot an inaccuracy or inconsistency in a translation, please contact us and let us know. We rely on the dedication and generosity of our users for contributing translations.
{{% /notice %}}
