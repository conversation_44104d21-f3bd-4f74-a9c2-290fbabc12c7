<?php
// Load environment variables
require_once __DIR__ . '/Env.php';

class Database {
    private $connection;
    private static $instance = null;

    private function __construct() {
        try {
            // Get database configuration from environment
            $host = Env::get('DB_HOST', 'localhost');
            $dbname = Env::get('DB_DATABASE', 'widdx_ai');
            $username = Env::get('DB_USERNAME', 'widdx');
            $password = Env::get('DB_PASSWORD', 'widdx');
            $port = Env::get('DB_PORT', '3306');
            
            $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
            
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::ATTR_PERSISTENT         => true, // Use persistent connections
            ];
            
            $this->connection = new PDO($dsn, $username, $password, $options);
            
            // Set timezone if specified
            if ($timezone = Env::get('APP_TIMEZONE', 'UTC')) {
                $this->connection->exec("SET time_zone='$timezone'");
            }
            
        } catch (PDOException $e) {
            error_log("Database Connection Error: " . $e->getMessage());
            throw new Exception("Database connection failed. Please check your configuration and try again.");
        }
    }
    
    /**
     * Get database instance (singleton pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }

    public function log($type, $message) {
        try {
            $stmt = $this->connection->prepare("
                INSERT INTO logs (log_type, message)
                VALUES (:type, :message)
            ");
            
            $stmt->execute([
                ':type' => $type,
                ':message' => $message
            ]);
        } catch (Exception $e) {
            error_log("Error logging message: " . $e->getMessage());
        }
    }
}
