:root {
    --primary: #4a6cf7;
    --secondary: #6c757d;
    --light: #f8f9fa;
    --dark: #343a40;
    --success: #28a745;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f7ff;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
}

h1 {
    color: var(--primary);
    margin-bottom: 10px;
}

.chat-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    height: 80vh;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.message {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.message-user {
    align-items: flex-end;
}

.message-ai {
    align-items: flex-start;
}

.message-bubble {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    margin-bottom: 5px;
    position: relative;
    line-height: 1.5;
}

.user-bubble {
    background-color: var(--primary);
    color: white;
    border-top-right-radius: 4px;
}

.ai-bubble {
    background-color: #f0f2f5;
    color: var(--dark);
    border-top-left-radius: 4px;
}

.message-info {
    font-size: 0.8em;
    color: var(--secondary);
    margin-bottom: 3px;
}

.typing-indicator {
    display: flex;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.typing-indicator span {
    height: 10px;
    width: 10px;
    background-color: var(--primary);
    border-radius: 50%;
    display: inline-block;
    margin: 0 2px;
    animation: bounce 1.5s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-5px);
    }
}

.input-area {
    display: flex;
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

#question-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 24px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s;
    direction: rtl;
}

#question-input:focus {
    border-color: var(--primary);
}

#ask-button {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 24px;
    padding: 0 24px;
    margin-left: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s;
}

#ask-button:hover {
    background-color: #3a5bd9;
}

#ask-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.source-tag {
    display: inline-block;
    font-size: 0.7em;
    padding: 2px 8px;
    border-radius: 10px;
    background-color: #e9ecef;
    color: var(--secondary);
    margin-left: 10px;
    vertical-align: middle;
}

.cached-tag {
    background-color: #d4edda;
    color: #155724;
}

@media (max-width: 768px) {
    .chat-container {
        height: 90vh;
    }
    
    .message-bubble {
        max-width: 90%;
    }
}
