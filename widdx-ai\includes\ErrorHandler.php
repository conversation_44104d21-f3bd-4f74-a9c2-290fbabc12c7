<?php
// Load environment variables first
require_once __DIR__ . '/Env.php';

// Load database configuration
require_once __DIR__ . '/../config/database.php';

// Now load the Database class
require_once __DIR__ . '/Database.php';

class ErrorHandler {
    private $db;
    private $pdo;
    private $errors = [];
    private $warnings = [];
    private $debug = false;

    public function __construct($debug = false) {
        $this->db = new Database();
        $this->pdo = $this->db->getConnection();
        $this->debug = $debug;
    }

    public function handleError($errno, $errstr, $errfile, $errline) {
        $error = [
            'type' => $errno,
            'message' => $errstr,
            'file' => $errfile,
            'line' => $errline,
            'time' => time()
        ];

        $this->errors[] = $error;
        $this->logError($error);

        if ($this->debug) {
            return false; // Let PHP's default error handler handle it
        }

        return true; // Prevent PHP's default error handler
    }

    public function handleException($exception) {
        $error = [
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'time' => time()
        ];

        $this->errors[] = $error;
        $this->logError($error);

        if ($this->debug) {
            throw $exception; // Re-throw in debug mode
        }

        // Return a user-friendly error message
        return [
            'status' => 'error',
            'message' => 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.'
        ];
    }

    public function logError($error) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO logs (log_type, message)
                VALUES (:type, :message)
            ");
            
            $stmt->execute([
                ':type' => 'ERROR',
                ':message' => json_encode($error)
            ]);
        } catch (Exception $e) {
            // If logging fails, at least write to error log
            error_log("Error logging failed: " . $e->getMessage());
            error_log(json_encode($error));
        }
    }

    public function getErrors() {
        return $this->errors;
    }

    public function addWarning($message) {
        $this->warnings[] = [
            'message' => $message,
            'time' => time()
        ];
    }

    public function getWarnings() {
        return $this->warnings;
    }

    public function clear() {
        $this->errors = [];
        $this->warnings = [];
    }

    public function setDebug($debug) {
        $this->debug = $debug;
    }

    public function isDebug() {
        return $this->debug;
    }
}

// Register error and exception handlers
$errorHandler = new ErrorHandler(APP_DEBUG);
set_error_handler([$errorHandler, 'handleError']);
set_exception_handler([$errorHandler, 'handleException']);
