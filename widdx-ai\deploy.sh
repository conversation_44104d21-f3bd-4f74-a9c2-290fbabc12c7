#!/bin/bash

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo "Please run as root"
    exit 1
fi

# Configuration
APP_DIR="/var/www/widdx-ai"
BACKUP_DIR="/var/www/backups"
LOG_FILE="/var/log/widdx-ai/deploy.log"

# Create directories
mkdir -p $APP_DIR
mkdir -p $BACKUP_DIR
mkdir -p /var/log/widdx-ai

# Log function
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# Backup existing installation
log "Starting backup..."
if [ -d "$APP_DIR" ]; then
    BACKUP_NAME="widdx-ai_$(date +'%Y%m%d_%H%M%S')"
    tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" -C "$APP_DIR" .
    log "Backup created at $BACKUP_DIR/$BACKUP_NAME.tar.gz"
fi

# Deploy new code
log "Deploying new code..."

cd $APP_DIR

# Pull latest code
if [ -d ".git" ]; then
    git pull origin main
else
    git clone https://github.com/yourusername/widdx-ai.git .
fi

# Install dependencies
log "Installing dependencies..."
composer install --no-dev --optimize-autoloader

# Run database migrations
log "Running database migrations..."
php install.php

# Clear cache
log "Clearing cache..."
rm -rf cache/*

# Set permissions
log "Setting permissions..."
chown -R www-data:www-data $APP_DIR
chmod -R 755 $APP_DIR
chmod -R 777 logs cache uploads

# Restart services
log "Restarting services..."
service apache2 restart
service redis-server restart

# Verify deployment
log "Verifying deployment..."
php -v
php install.php

log "Deployment completed successfully!"
