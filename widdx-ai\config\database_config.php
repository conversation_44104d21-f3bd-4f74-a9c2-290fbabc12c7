<?php
// Database Configuration
if (!defined('SECURE_DB_CONFIG_LOADED')) {
    define('SECURE_DB_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// Database Settings
const DB_ENABLED = Env::get('DB_ENABLED', true);
const DB_HOST = Env::get('DB_HOST', 'localhost');
const DB_PORT = Env::get('DB_PORT', 3306);
const DB_NAME = Env::get('DB_NAME', 'widdx_ai');
const DB_USER = Env::get('DB_USER', 'root');
const DB_PASS = Env::get('DB_PASS', '');
const DB_CHARSET = Env::get('DB_CHARSET', 'utf8mb4');
const DB_COLLATE = Env::get('DB_COLLATE', 'utf8mb4_unicode_ci');
const DB_PREFIX = Env::get('DB_PREFIX', 'widdx_');

// SSL Settings
const DB_SSL_ENABLED = Env::get('DB_SSL_ENABLED', true);
const DB_SSL_CA = Env::get('DB_SSL_CA', '');
const DB_SSL_CERT = Env::get('DB_SSL_CERT', '');
const DB_SSL_KEY = Env::get('DB_SSL_KEY', '');
const DB_SSL_VERIFY = Env::get('DB_SSL_VERIFY', true);

// Connection Pooling
const DB_POOL_ENABLED = Env::get('DB_POOL_ENABLED', true);
const DB_POOL_SIZE = Env::get('DB_POOL_SIZE', 10);
const DB_POOL_TIMEOUT = Env::get('DB_POOL_TIMEOUT', 30);
const DB_POOL_MAX_IDLE = Env::get('DB_POOL_MAX_IDLE', 300);
const DB_POOL_MAX_LIFETIME = Env::get('DB_POOL_MAX_LIFETIME', 3600);

// Connection Settings
const DB_CONNECTION_TIMEOUT = Env::get('DB_CONNECTION_TIMEOUT', 30);
const DB_QUERY_TIMEOUT = Env::get('DB_QUERY_TIMEOUT', 30);
const DB_IDLE_TIMEOUT = Env::get('DB_IDLE_TIMEOUT', 300);
const DB_MAX_RETRIES = Env::get('DB_MAX_RETRIES', 3);
const DB_RETRY_DELAY = Env::get('DB_RETRY_DELAY', 1000);

// Logging
const DB_LOGGING_ENABLED = Env::get('DB_LOGGING_ENABLED', true);
const DB_LOGGING_LEVEL = Env::get('DB_LOGGING_LEVEL', 'INFO');
const DB_LOGGING_FILE = Env::get('DB_LOGGING_FILE', __DIR__ . '/../logs/database.log');
const DB_LOGGING_FORMAT = Env::get('DB_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');

// Metrics
const DB_METRICS_ENABLED = Env::get('DB_METRICS_ENABLED', true);
const DB_METRICS_INTERVAL = Env::get('DB_METRICS_INTERVAL', 60);
const DB_METRICS_FILE = Env::get('DB_METRICS_FILE', __DIR__ . '/../logs/db_metrics.log');
const DB_METRICS_FORMAT = Env::get('DB_METRICS_FORMAT', '{timestamp} {metric} {value}');

// Error Handling
const DB_ERROR_HANDLING_ENABLED = Env::get('DB_ERROR_HANDLING_ENABLED', true);
const DB_ERROR_RETRY_ENABLED = Env::get('DB_ERROR_RETRY_ENABLED', true);
const DB_ERROR_RETRY_MAX_ATTEMPTS = Env::get('DB_ERROR_RETRY_MAX_ATTEMPTS', 3);
const DB_ERROR_RETRY_DELAY = Env::get('DB_ERROR_RETRY_DELAY', 1000);
const DB_ERROR_RETRY_BACKOFF = Env::get('DB_ERROR_RETRY_BACKOFF', 2);
const DB_ERROR_LOGGING_ENABLED = Env::get('DB_ERROR_LOGGING_ENABLED', true);
const DB_ERROR_LOGGING_LEVEL = Env::get('DB_ERROR_LOGGING_LEVEL', 'ERROR');

// Security
const DB_SECURITY_ENABLED = Env::get('DB_SECURITY_ENABLED', true);
const DB_SECURITY_INPUT_VALIDATION = Env::get('DB_SECURITY_INPUT_VALIDATION', true);
const DB_SECURITY_OUTPUT_VALIDATION = Env::get('DB_SECURITY_OUTPUT_VALIDATION', true);

// Security and Validation
if (DB_ENABLED) {
    // Validate database credentials
    if (empty(DB_HOST) || empty(DB_NAME)) {
        trigger_error('Invalid database connection configuration', E_USER_WARNING);
    }
    
    // Validate SSL settings
    if (DB_SSL_ENABLED && (!DB_SSL_CA || !DB_SSL_CERT || !DB_SSL_KEY)) {
        trigger_error('SSL is enabled but SSL certificates are not configured', E_USER_WARNING);
    }
    
    // Validate connection settings
    if (DB_CONNECTION_TIMEOUT < 1 || DB_QUERY_TIMEOUT < 1) {
        trigger_error('Invalid connection timeout values', E_USER_WARNING);
    }
    
    // Validate pool settings
    if (DB_POOL_ENABLED && (DB_POOL_SIZE < 1 || DB_POOL_MAX_IDLE < 1)) {
        trigger_error('Invalid connection pool settings', E_USER_WARNING);
    }
    
    // Validate retry settings
    if (DB_ERROR_RETRY_MAX_ATTEMPTS < 1 || DB_ERROR_RETRY_DELAY < 1) {
        trigger_error('Invalid error retry configuration', E_USER_WARNING);
    }
    
    // Validate logging settings
    if (!in_array(DB_LOGGING_LEVEL, ['DEBUG', 'INFO', 'WARNING', 'ERROR'])) {
        trigger_error('Invalid logging level configuration', E_USER_WARNING);
    }
    
    // Create log directories with proper permissions
    $logDir = dirname(DB_LOGGING_FILE);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    chmod($logDir, 0755);
}

// Create metrics directory with proper permissions
$metricsDir = dirname(DB_METRICS_FILE);
if (!file_exists($metricsDir)) {
    mkdir($metricsDir, 0755, true);
}

chmod($metricsDir, 0755);
define('DB_SECURITY_RATE_LIMITING', true);
define('DB_SECURITY_IP_RESTRICTIONS', true);
define('DB_SECURITY_REQUEST_VALIDATION', true);

define('DB_AUTH_ENABLED', true);
define('DB_AUTH_METHOD', 'session'); // session, jwt, cookie
define('DB_AUTH_TTL', 3600);
define('DB_AUTH_REFRESH_TTL', 86400);
define('DB_AUTH_TOKEN_NAME', 'db_token');
define('DB_AUTH_COOKIE_NAME', 'widdx_db_auth');
define('DB_AUTH_COOKIE_SECURE', true);
define('DB_AUTH_COOKIE_HTTPONLY', true);
define('DB_AUTH_COOKIE_SAMESITE', 'Strict');
define('DB_AUTH_PASSWORD_MIN_LENGTH', 8);
define('DB_AUTH_PASSWORD_MAX_LENGTH', 128);
define('DB_AUTH_PASSWORD_REQUIRE_UPPERCASE', true);
define('DB_AUTH_PASSWORD_REQUIRE_LOWERCASE', true);
define('DB_AUTH_PASSWORD_REQUIRE_NUMBER', true);
define('DB_AUTH_PASSWORD_REQUIRE_SPECIAL', true);
define('DB_AUTH_PASSWORD_SPECIAL_CHARS', '!@#$%^&*()');

define('DB_IP_WHITELIST_ENABLED', true);
define('DB_IP_WHITELIST', [
    '127.0.0.1',
    '::1'
]);
define('DB_IP_BLACKLIST_ENABLED', true);
define('DB_IP_BLACKLIST', []);
define('DB_IP_LOGGING_ENABLED', true);
define('DB_IP_LOGGING_FILE', __DIR__ . '/../logs/db_ip.log');

define('DB_REQUEST_VALIDATION', [
    'enabled' => true,
    'max_headers' => 100,
    'max_body_size' => 10485760,
    'timeout' => 30,
    'validate_content_type' => true,
    'validate_content_length' => true,
    'validate_headers' => true
]);

define('DB_TABLES', [
    'questions' => [
        'columns' => [
            'id' => 'INT PRIMARY KEY AUTO_INCREMENT',
            'question_text' => 'TEXT',
            'answer_text' => 'TEXT',
            'source_model' => 'VARCHAR(50)',
            'created_at' => 'DATETIME',
            'updated_at' => 'DATETIME',
            'error' => 'TEXT',
            'confidence' => 'DECIMAL(5,2)',
            'response_time' => 'INT',
            'user_id' => 'INT',
            'session_id' => 'VARCHAR(255)'
        ],
        'indexes' => [
            'idx_question_text' => 'FULLTEXT(question_text)',
            'idx_source_model' => 'INDEX(source_model)',
            'idx_created_at' => 'INDEX(created_at)'
        ]
    ],
    'logs' => [
        'columns' => [
            'id' => 'INT PRIMARY KEY AUTO_INCREMENT',
            'level' => 'VARCHAR(20)',
            'message' => 'TEXT',
            'context' => 'TEXT',
            'timestamp' => 'DATETIME',
            'ip' => 'VARCHAR(45)',
            'user_agent' => 'TEXT',
            'request_id' => 'VARCHAR(255)'
        ],
        'indexes' => [
            'idx_level' => 'INDEX(level)',
            'idx_timestamp' => 'INDEX(timestamp)'
        ]
    ],
    'metrics' => [
        'columns' => [
            'id' => 'INT PRIMARY KEY AUTO_INCREMENT',
            'metric' => 'VARCHAR(50)',
            'value' => 'DECIMAL(20,2)',
            'timestamp' => 'DATETIME',
            'unit' => 'VARCHAR(20)',
            'context' => 'TEXT'
        ],
        'indexes' => [
            'idx_metric' => 'INDEX(metric)',
            'idx_timestamp' => 'INDEX(timestamp)'
        ]
    ]
]);

define('DB_ERROR_MESSAGES', [
    'generic' => 'حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.',
    'connection' => 'فشل الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى.',
    'query' => 'حدث خطأ في استعلام قاعدة البيانات. يرجى المحاولة مرة أخرى.',
    'transaction' => 'فشل المعاملة. يرجى المحاولة مرة أخرى.',
    'lock' => 'حدث خطأ في قفل البيانات. يرجى المحاولة مرة أخرى.',
    'timeout' => 'انتهت مهلة الانتظار. يرجى المحاولة مرة أخرى.',
    'rate_limit' => 'تم تجاوز حد معدل الطلبات. يرجى الانتظار قليلاً.',
    'auth' => 'فشل التحقق من الهوية. يرجى التأكد من بيانات تسجيل الدخول.',
    'permission' => 'ليس لديك صلاحيات كافية للوصول إلى هذه الميزة.',
    'not_found' => 'لم يتم العثور على المورد المطلوب.',
    'server_error' => 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.'
]);
