[2025-06-28 00:48:36] [HEADER] === Starting Professional Payment Test Suite ===
[2025-06-28 00:48:36] [INFO] Test started at: 2025-06-28 00:48:36
[2025-06-28 00:48:36] [INFO] Log file: C:\xampp\htdocs\tests\lahza/payment_test_20250628_004836.log
[2025-06-28 00:48:36] [TEST] 🚀 Starting test: Individual customer with successful payment
[2025-06-28 00:48:36] [INFO]    ℹ️ Creating individual customer
[2025-06-28 00:48:36] [DETAIL]       Details: {
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "phone": "+966501234567",
    "address1": "شارع الملك فهد",
    "city": "الرياض",
    "state": "الرياض",
    "postcode": "12345",
    "country": "SA",
    "currency": "SAR"
}
[2025-06-28 00:48:37] [SUCCESS]    ✅ Customer data validated successfully
[2025-06-28 00:48:37] [INFO]    ℹ️ Creating test invoice #INV-1751064517-D49BD0 for 203.78 SAR
[2025-06-28 00:48:37] [INFO]    ℹ️ Processing payment with Visa card ending with 1111
[2025-06-28 00:48:38] [SUCCESS]    ✅ Payment processed with status: succeeded
[2025-06-28 00:48:38] [DETAIL]       Details: {
    "success": true,
    "status": "succeeded",
    "transaction_id": "TXN_1751064518_8417",
    "amount": 203.78,
    "currency": "SAR",
    "card": {
        "type": "Visa",
        "last4": "1111"
    },
    "customer": {
        "name": "أحمد محمد",
        "email": "<EMAIL>"
    },
    "timestamp": "2025-06-28T00:48:38+02:00"
}
[2025-06-28 00:48:38] [ERROR]    ❌ Assertion failed: Invoice status should be Paid after payment
[2025-06-28 00:48:38] [PASS] ✅ Test passed: Individual customer with successful payment (1.28s)
[2025-06-28 00:48:38] [SEPARATOR] 
[2025-06-28 00:48:38] [TEST] 🚀 Starting test: Business customer with insufficient funds
[2025-06-28 00:48:38] [INFO]    ℹ️ Creating business customer
[2025-06-28 00:48:38] [DETAIL]       Details: {
    "company_name": "شركة التقنية المتطورة",
    "first_name": "سارة",
    "last_name": "علي",
    "email": "<EMAIL>",
    "phone": "+966502345678",
    "address1": "حي العليا",
    "city": "جدة",
    "state": "مكة المكرمة",
    "postcode": "23451",
    "country": "SA",
    "currency": "SAR",
    "tax_id": "1234567890"
}
[2025-06-28 00:48:38] [SUCCESS]    ✅ Customer data validated successfully
[2025-06-28 00:48:38] [INFO]    ℹ️ Creating test invoice #INV-1751064518-D95307 for 267.01 SAR
[2025-06-28 00:48:38] [INFO]    ℹ️ Processing payment with Visa card ending with 9995
[2025-06-28 00:48:39] [ERROR]    ❌ Payment processed with status: declined
[2025-06-28 00:48:39] [DETAIL]       Details: {
    "success": false,
    "status": "declined",
    "transaction_id": "TXN_1751064519_4731",
    "decline_code": "insufficient_funds",
    "message": "Insufficient funds",
    "timestamp": "2025-06-28T00:48:39+02:00"
}
[2025-06-28 00:48:39] [SUCCESS]    ✅ Invoice status should be Unpaid after payment
[2025-06-28 00:48:39] [PASS] ✅ Test passed: Business customer with insufficient funds (1.29s)
[2025-06-28 00:48:39] [SEPARATOR] 
[2025-06-28 00:48:39] [SUMMARY] 
=== Test Summary ===
Total tests: 2
✅ Passed: 2
❌ Failed: 0
⏱️  Duration: 2.57s
Test completed at: 2025-06-28 00:48:39
Log file: C:\xampp\htdocs\tests\lahza/payment_test_20250628_004836.log

[2025-06-28 00:48:39] [RESULT] ✅ Individual customer with successful payment (1.28s)
[2025-06-28 00:48:39] [RESULT] ✅ Business customer with insufficient funds (1.29s)
