#!/bin/bash
#
# WIDDX Production Validation and QA Script
# Comprehensive production environment validation and testing
#
# @package    WIDDX Production Deployment
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration
WHMCS_PATH="/var/www/html/whmcs"
LOG_PATH="/var/log/widdx-deployment"
DOMAIN="yourdomain.com"
ADMIN_EMAIL="<EMAIL>"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
CRITICAL_FAILURES=()
WARNINGS=()

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/validation.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/validation.log"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/validation.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/validation.log"
}

# Test execution function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local is_critical="${3:-false}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Testing ${test_name}... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log "✅ ${test_name}: PASSED"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        if [[ "$is_critical" == "true" ]]; then
            CRITICAL_FAILURES+=("$test_name")
            error "❌ ${test_name}: CRITICAL FAILURE"
        else
            WARNINGS+=("$test_name")
            warning "⚠️ ${test_name}: WARNING"
        fi
        return 1
    fi
}

# Create log directory
mkdir -p "${LOG_PATH}"

log "🚀 Starting WIDDX Production Validation and QA"

# 1. Infrastructure Validation
log "🏗️ Phase 1: Infrastructure Validation"
echo "=" . str_repeat("=", 50)

run_test "Website Accessibility" "curl -f -s -o /dev/null https://${DOMAIN}/" true
run_test "HTTPS Redirect" "curl -s -o /dev/null -w '%{redirect_url}' http://${DOMAIN}/ | grep -q https" true
run_test "SSL Certificate Validity" "echo | openssl s_client -servername ${DOMAIN} -connect ${DOMAIN}:443 2>/dev/null | openssl x509 -checkend 86400 -noout" true
run_test "Admin Area Accessibility" "curl -f -s -o /dev/null https://${DOMAIN}/admin/" true
run_test "Client Area Accessibility" "curl -f -s -o /dev/null https://${DOMAIN}/clientarea.php" true

# 2. WIDDX Theme Validation
log "🎨 Phase 2: WIDDX Theme Validation"
echo "=" . str_repeat("=", 50)

run_test "WIDDX Theme Files Present" "test -f ${WHMCS_PATH}/templates/WIDDX/theme.yaml" true
run_test "WIDDX CSS Files Present" "test -f ${WHMCS_PATH}/templates/WIDDX/css/theme.css" true
run_test "WIDDX JavaScript Files Present" "test -f ${WHMCS_PATH}/templates/WIDDX/js/widdx-modern.js" true
run_test "Theme Configuration Active" "grep -q 'WIDDX' ${WHMCS_PATH}/configuration.php" true

# Test theme rendering
run_test "Homepage Renders WIDDX Theme" "curl -s https://${DOMAIN}/ | grep -q 'WIDDX'" true
run_test "Client Area Renders WIDDX Theme" "curl -s https://${DOMAIN}/clientarea.php | grep -q 'WIDDX'" true

# 3. Lahza Payment Gateway Validation
log "💳 Phase 3: Lahza Payment Gateway Validation"
echo "=" . str_repeat("=", 50)

run_test "Lahza Gateway Files Present" "test -f ${WHMCS_PATH}/modules/gateways/lahza.php" true
run_test "Lahza Callback Files Present" "test -f ${WHMCS_PATH}/modules/gateways/callback/lahza.php" true
run_test "Lahza 3DS Callback Present" "test -f ${WHMCS_PATH}/modules/gateways/callback/lahza_3ds.php" true
run_test "Lahza Support Files Present" "test -d ${WHMCS_PATH}/modules/gateways/lahza" true
run_test "Gateway Logs Directory Present" "test -d ${WHMCS_PATH}/modules/gateways/logs" true

# Test gateway configuration
DB_HOST=$(grep -oP "(?<=\\\$db_host = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "localhost")
DB_NAME=$(grep -oP "(?<=\\\$db_name = ')[^']*" "${WHMCS_PATH}/configuration.php")
DB_USER=$(grep -oP "(?<=\\\$db_username = ')[^']*" "${WHMCS_PATH}/configuration.php")
DB_PASS=$(grep -oP "(?<=\\\$db_password = ')[^']*" "${WHMCS_PATH}/configuration.php")

run_test "Lahza Gateway Database Configuration" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT COUNT(*) FROM tblpaymentgateways WHERE gateway=\"lahza\";' | tail -n 1 | grep -q '[1-9]'" true

# 4. Order Form Validation
log "🛒 Phase 4: Order Form Validation"
echo "=" . str_repeat("=", 50)

run_test "WIDDX Modern Order Form Present" "test -f ${WHMCS_PATH}/templates/orderforms/widdx_modern/theme.yaml" true
run_test "Order Form CSS Present" "test -f ${WHMCS_PATH}/templates/orderforms/widdx_modern/css/order-form.css" true
run_test "Order Form JavaScript Present" "test -f ${WHMCS_PATH}/templates/orderforms/widdx_modern/js/order-form.js" true

# Test order form accessibility
run_test "Order Form Accessible" "curl -f -s -o /dev/null https://${DOMAIN}/cart.php" true
run_test "Order Form Renders Correctly" "curl -s https://${DOMAIN}/cart.php | grep -q 'widdx'" true

# 5. Performance Validation
log "⚡ Phase 5: Performance Validation"
echo "=" . str_repeat("=", 50)

# Homepage performance
HOMEPAGE_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://${DOMAIN}/)
run_test "Homepage Load Time (<3s)" "echo '${HOMEPAGE_TIME} < 3' | bc -l | grep -q 1" false

# Client area performance
CLIENTAREA_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://${DOMAIN}/clientarea.php)
run_test "Client Area Load Time (<4s)" "echo '${CLIENTAREA_TIME} < 4' | bc -l | grep -q 1" false

# Order form performance
ORDERFORM_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://${DOMAIN}/cart.php)
run_test "Order Form Load Time (<3s)" "echo '${ORDERFORM_TIME} < 3' | bc -l | grep -q 1" false

# Database performance
DB_TIME=$(mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e "USE ${DB_NAME}; SET @start_time = NOW(6); SELECT COUNT(*) FROM tblinvoices; SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, NOW(6)) / 1000000;" | tail -n 1)
run_test "Database Query Performance (<1s)" "echo '${DB_TIME} < 1' | bc -l | grep -q 1" false

# 6. Security Validation
log "🔒 Phase 6: Security Validation"
echo "=" . str_repeat("=", 50)

run_test "HTTPS Enforcement" "curl -s -o /dev/null -w '%{http_code}' http://${DOMAIN}/ | grep -q '301'" true
run_test "Security Headers Present" "curl -s -I https://${DOMAIN}/ | grep -q 'X-Content-Type-Options'" false
run_test "HSTS Header Present" "curl -s -I https://${DOMAIN}/ | grep -q 'Strict-Transport-Security'" false
run_test "XSS Protection Header" "curl -s -I https://${DOMAIN}/ | grep -q 'X-XSS-Protection'" false

# File permissions check
run_test "Secure File Permissions" "find ${WHMCS_PATH} -type f -perm /o+w | wc -l | grep -q '^0$'" true
run_test "Secure Directory Permissions" "find ${WHMCS_PATH} -type d -perm /o+w | wc -l | grep -q '^0$'" true

# 7. Accessibility Validation
log "♿ Phase 7: Accessibility Validation"
echo "=" . str_repeat("=", 50)

# Check for accessibility features
run_test "Skip Links Present" "curl -s https://${DOMAIN}/ | grep -q 'skip-link'" false
run_test "ARIA Labels Present" "curl -s https://${DOMAIN}/ | grep -q 'aria-label'" false
run_test "Semantic HTML Present" "curl -s https://${DOMAIN}/ | grep -q '<main\|<nav\|<header\|<footer'" false

# 8. Responsive Design Validation
log "📱 Phase 8: Responsive Design Validation"
echo "=" . str_repeat("=", 50)

run_test "Viewport Meta Tag Present" "curl -s https://${DOMAIN}/ | grep -q 'viewport'" true
run_test "Responsive CSS Present" "curl -s https://${DOMAIN}/templates/WIDDX/css/theme.css | grep -q '@media'" false
run_test "Mobile-Friendly Design" "curl -s https://${DOMAIN}/ | grep -q 'responsive\|mobile'" false

# 9. Monitoring System Validation
log "📊 Phase 9: Monitoring System Validation"
echo "=" . str_repeat("=", 50)

run_test "Monitoring Scripts Present" "test -f /var/monitoring/widdx/system-health.sh" true
run_test "Monitoring Cron Jobs Active" "crontab -l | grep -q monitoring" true
run_test "Log Directories Writable" "test -w /var/log/widdx-deployment" true
run_test "Monitoring Logs Present" "test -f /var/monitoring/widdx/health.log" false

# 10. Integration Testing
log "🔗 Phase 10: Integration Testing"
echo "=" . str_repeat("=", 50)

# Test WHMCS core functionality
run_test "WHMCS Database Connection" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT 1;'" true
run_test "WHMCS Configuration Valid" "php -l ${WHMCS_PATH}/configuration.php" true
run_test "WHMCS Init File Valid" "php -l ${WHMCS_PATH}/init.php" true

# Test gateway integration
run_test "Gateway PHP Syntax Valid" "php -l ${WHMCS_PATH}/modules/gateways/lahza.php" true
run_test "Callback PHP Syntax Valid" "php -l ${WHMCS_PATH}/modules/gateways/callback/lahza.php" true

# Generate Validation Report
log "📊 Generating Production Validation Report"

cat > "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt" << EOF
WIDDX Production Validation Report
=================================
Validation ID: ${TIMESTAMP}
Completed: $(date)
Domain: ${DOMAIN}

VALIDATION SUMMARY:
Total Tests: ${TOTAL_TESTS}
Passed: ${PASSED_TESTS}
Failed: ${FAILED_TESTS}
Success Rate: $(echo "scale=2; ${PASSED_TESTS} * 100 / ${TOTAL_TESTS}" | bc)%

PERFORMANCE METRICS:
- Homepage Load Time: ${HOMEPAGE_TIME}s
- Client Area Load Time: ${CLIENTAREA_TIME}s
- Order Form Load Time: ${ORDERFORM_TIME}s
- Database Query Time: ${DB_TIME}s

CRITICAL FAILURES:
EOF

if [[ ${#CRITICAL_FAILURES[@]} -eq 0 ]]; then
    echo "None - All critical tests passed ✅" >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt"
else
    for failure in "${CRITICAL_FAILURES[@]}"; do
        echo "- ${failure}" >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt"
    done
fi

cat >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt" << EOF

WARNINGS:
EOF

if [[ ${#WARNINGS[@]} -eq 0 ]]; then
    echo "None - All non-critical tests passed ✅" >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt"
else
    for warning in "${WARNINGS[@]}"; do
        echo "- ${warning}" >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt"
    done
fi

cat >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt" << EOF

DEPLOYMENT STATUS:
EOF

if [[ ${#CRITICAL_FAILURES[@]} -eq 0 ]]; then
    cat >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt" << EOF
✅ PRODUCTION READY
All critical tests passed. System is ready for live traffic.

RECOMMENDATIONS:
1. Monitor system performance for the first 24 hours
2. Address any warnings in the next maintenance window
3. Continue regular monitoring and maintenance
4. Schedule performance optimization review in 30 days
EOF
else
    cat >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt" << EOF
❌ PRODUCTION NOT READY
Critical failures detected. Address these issues before going live:

REQUIRED ACTIONS:
EOF
    for failure in "${CRITICAL_FAILURES[@]}"; do
        echo "- Fix: ${failure}" >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt"
    done
    
    cat >> "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt" << EOF

ROLLBACK PLAN:
1. Restore from pre-deployment backup
2. Revert DNS changes if applicable
3. Investigate and fix critical issues
4. Re-run validation tests
5. Retry deployment when all tests pass
EOF
fi

# Final Summary
echo ""
echo "=" . str_repeat("=", 80)
log "🎯 PRODUCTION VALIDATION COMPLETED"
echo "=" . str_repeat("=", 80)

echo ""
echo "📊 VALIDATION SUMMARY:"
echo "   Total Tests: ${TOTAL_TESTS}"
echo "   Passed: ${PASSED_TESTS}"
echo "   Failed: ${FAILED_TESTS}"
echo "   Success Rate: $(echo "scale=2; ${PASSED_TESTS} * 100 / ${TOTAL_TESTS}" | bc)%"

echo ""
echo "⚡ PERFORMANCE METRICS:"
echo "   Homepage: ${HOMEPAGE_TIME}s"
echo "   Client Area: ${CLIENTAREA_TIME}s"
echo "   Order Form: ${ORDERFORM_TIME}s"
echo "   Database: ${DB_TIME}s"

if [[ ${#CRITICAL_FAILURES[@]} -eq 0 ]]; then
    echo ""
    echo -e "${GREEN}✅ PRODUCTION STATUS: READY FOR LIVE TRAFFIC${NC}"
    echo ""
    echo "🎉 All critical tests passed!"
    echo "🌐 WIDDX system is production-ready"
    echo "📊 Monitoring systems are active"
    echo "🔒 Security validations passed"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Begin live traffic monitoring"
    echo "   2. Monitor payment processing"
    echo "   3. Track user experience metrics"
    echo "   4. Schedule 24-hour review"
else
    echo ""
    echo -e "${RED}❌ PRODUCTION STATUS: NOT READY${NC}"
    echo ""
    echo "🚨 Critical failures detected:"
    for failure in "${CRITICAL_FAILURES[@]}"; do
        echo "   - ${failure}"
    done
    echo ""
    echo "📋 Required Actions:"
    echo "   1. Address all critical failures"
    echo "   2. Re-run validation tests"
    echo "   3. Consider rollback if issues persist"
fi

if [[ ${#WARNINGS[@]} -gt 0 ]]; then
    echo ""
    echo -e "${YELLOW}⚠️ WARNINGS TO ADDRESS:${NC}"
    for warning in "${WARNINGS[@]}"; do
        echo "   - ${warning}"
    done
fi

echo ""
echo "📄 Detailed report: ${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt"
echo "📋 Validation logs: ${LOG_PATH}/validation.log"

# Email report if mail is available
if command -v mail >/dev/null 2>&1; then
    mail -s "WIDDX Production Validation Report - ${TIMESTAMP}" "${ADMIN_EMAIL}" < "${LOG_PATH}/production_validation_report_${TIMESTAMP}.txt"
    echo "📧 Report emailed to: ${ADMIN_EMAIL}"
fi

echo ""
echo "=" . str_repeat("=", 80)

info "Production validation completed. Check ${LOG_PATH}/validation.log for detailed logs."
EOF
