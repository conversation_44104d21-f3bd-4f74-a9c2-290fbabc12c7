<?php

/**
 * Go-Live Authorization System for WIDDX
 * 
 * Comprehensive production readiness validation and authorization system
 * 
 * @package WIDDX
 * @subpackage PaymentGateway
 * @version 1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/EnhancedTransactionManager.php';

class GoLiveAuthorization {
    
    private $logger;
    private $gatewayParams;
    private $validationResults = [];
    private $criticalIssues = [];
    private $warnings = [];
    private $authorizationScore = 0;
    
    // Authorization thresholds
    const MINIMUM_SCORE = 95;
    const CRITICAL_THRESHOLD = 0;
    const WARNING_THRESHOLD = 3;
    
    // Validation categories
    const CATEGORY_SECURITY = 'security';
    const CATEGORY_CONFIGURATION = 'configuration';
    const CATEGORY_INTEGRATION = 'integration';
    const CATEGORY_PERFORMANCE = 'performance';
    const CATEGORY_COMPLIANCE = 'compliance';
    const CATEGORY_MONITORING = 'monitoring';
    
    public function __construct($gatewayParams = null) {
        $this->gatewayParams = $gatewayParams ?: getGatewayVariables('lahza');
        $this->logger = new LahzaLogger();
    }
    
    /**
     * Run comprehensive go-live authorization
     */
    public function runAuthorization() {
        $this->logger->info('Starting Go-Live Authorization Process', [], 'go_live');
        
        echo "🚀 WIDDX Go-Live Authorization System\n";
        echo str_repeat("=", 50) . "\n\n";
        
        // Run all validation phases
        $this->validateSecurity();
        $this->validateConfiguration();
        $this->validateIntegration();
        $this->validatePerformance();
        $this->validateCompliance();
        $this->validateMonitoring();
        
        // Calculate final authorization
        $this->calculateAuthorizationScore();
        $this->generateAuthorizationReport();
        
        return $this->isAuthorizedForGoLive();
    }
    
    /**
     * Validate security implementation
     */
    private function validateSecurity() {
        echo "🔒 Validating Security Implementation...\n";
        
        // Test 1: API Key Security
        $this->runValidation('API Key Security', self::CATEGORY_SECURITY, function() {
            $publicKey = $this->gatewayParams['publicKey'] ?? '';
            $secretKey = $this->gatewayParams['secretKey'] ?? '';
            $webhookSecret = $this->gatewayParams['webhookSecret'] ?? '';
            
            if (empty($publicKey) || empty($secretKey) || empty($webhookSecret)) {
                throw new Exception('Missing required API keys');
            }
            
            // Validate key formats
            if (!preg_match('/^pk_live_/', $publicKey) && $this->gatewayParams['testMode'] !== 'on') {
                throw new Exception('Production public key required for live mode');
            }
            
            if (!preg_match('/^sk_live_/', $secretKey) && $this->gatewayParams['testMode'] !== 'on') {
                throw new Exception('Production secret key required for live mode');
            }
            
            return true;
        });
        
        // Test 2: SSL/TLS Configuration
        $this->runValidation('SSL/TLS Configuration', self::CATEGORY_SECURITY, function() {
            if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
                if (!isset($_SERVER['HTTP_X_FORWARDED_PROTO']) || $_SERVER['HTTP_X_FORWARDED_PROTO'] !== 'https') {
                    throw new Exception('HTTPS is required for production');
                }
            }
            
            // Check SSL certificate
            $context = stream_context_create([
                "ssl" => [
                    "verify_peer" => true,
                    "verify_peer_name" => true,
                ]
            ]);
            
            $url = $this->gatewayParams['systemurl'] ?? '';
            if (!empty($url)) {
                $result = @file_get_contents($url, false, $context);
                if ($result === false) {
                    $this->warnings[] = 'SSL certificate validation failed';
                }
            }
            
            return true;
        });
        
        // Test 3: 3D Secure Implementation
        $this->runValidation('3D Secure Implementation', self::CATEGORY_SECURITY, function() {
            $enable3DS = $this->gatewayParams['enable3DS'] ?? 'off';
            if ($enable3DS !== 'on') {
                $this->warnings[] = '3D Secure is not enabled - recommended for production';
            }
            
            // Check 3DS files
            $threeDSFiles = [
                __DIR__ . '/Enhanced3DSecure.php',
                __DIR__ . '/3ds_init.php',
                __DIR__ . '/3ds_status.php'
            ];
            
            foreach ($threeDSFiles as $file) {
                if (!file_exists($file)) {
                    throw new Exception("3D Secure file missing: " . basename($file));
                }
            }
            
            return true;
        });
        
        // Test 4: Input Validation
        $this->runValidation('Input Validation', self::CATEGORY_SECURITY, function() {
            // Check if validation functions exist
            $validationFunctions = [
                'filter_var',
                'htmlspecialchars',
                'hash_hmac'
            ];
            
            foreach ($validationFunctions as $function) {
                if (!function_exists($function)) {
                    throw new Exception("Required validation function not available: {$function}");
                }
            }
            
            return true;
        });
        
        // Test 5: Rate Limiting
        $this->runValidation('Rate Limiting', self::CATEGORY_SECURITY, function() {
            $cacheDir = __DIR__ . '/cache';
            if (!is_dir($cacheDir)) {
                if (!mkdir($cacheDir, 0755, true)) {
                    throw new Exception('Cannot create cache directory for rate limiting');
                }
            }
            
            if (!is_writable($cacheDir)) {
                throw new Exception('Cache directory is not writable');
            }
            
            return true;
        });
        
        echo "   Security validation completed\n\n";
    }
    
    /**
     * Validate configuration settings
     */
    private function validateConfiguration() {
        echo "⚙️  Validating Configuration...\n";
        
        // Test 1: Gateway Configuration
        $this->runValidation('Gateway Configuration', self::CATEGORY_CONFIGURATION, function() {
            $requiredParams = ['publicKey', 'secretKey', 'webhookSecret'];
            
            foreach ($requiredParams as $param) {
                if (empty($this->gatewayParams[$param])) {
                    throw new Exception("Missing required parameter: {$param}");
                }
            }
            
            // Validate test mode setting
            $testMode = $this->gatewayParams['testMode'] ?? 'on';
            if ($testMode === 'on') {
                $this->warnings[] = 'Gateway is in test mode - switch to live mode for production';
            }
            
            return true;
        });
        
        // Test 2: Database Configuration
        $this->runValidation('Database Configuration', self::CATEGORY_CONFIGURATION, function() {
            try {
                // Test database connection
                $pdo = Capsule::connection()->getPdo();
                if (!$pdo) {
                    throw new Exception('Database connection failed');
                }
                
                // Check required tables
                $requiredTables = [
                    'lahza_transactions',
                    'lahza_transaction_status_history',
                    'lahza_transaction_events'
                ];
                
                foreach ($requiredTables as $table) {
                    $result = Capsule::schema()->hasTable($table);
                    if (!$result) {
                        throw new Exception("Required table missing: {$table}");
                    }
                }
                
                return true;
            } catch (Exception $e) {
                throw new Exception('Database validation failed: ' . $e->getMessage());
            }
        });
        
        // Test 3: File Permissions
        $this->runValidation('File Permissions', self::CATEGORY_CONFIGURATION, function() {
            $criticalPaths = [
                __DIR__ . '/cache',
                __DIR__ . '/logs',
                __DIR__ . '/../../../uploads'
            ];
            
            foreach ($criticalPaths as $path) {
                if (is_dir($path) && !is_writable($path)) {
                    throw new Exception("Directory not writable: {$path}");
                }
            }
            
            return true;
        });
        
        // Test 4: PHP Configuration
        $this->runValidation('PHP Configuration', self::CATEGORY_CONFIGURATION, function() {
            $requiredExtensions = ['curl', 'json', 'openssl', 'mbstring'];
            
            foreach ($requiredExtensions as $extension) {
                if (!extension_loaded($extension)) {
                    throw new Exception("Required PHP extension not loaded: {$extension}");
                }
            }
            
            // Check PHP version
            if (version_compare(PHP_VERSION, '7.4.0', '<')) {
                throw new Exception('PHP 7.4 or higher is required');
            }
            
            // Check memory limit
            $memoryLimit = ini_get('memory_limit');
            if ($memoryLimit !== '-1' && $this->parseMemoryLimit($memoryLimit) < 128 * 1024 * 1024) {
                $this->warnings[] = 'PHP memory limit is below recommended 128MB';
            }
            
            return true;
        });
        
        echo "   Configuration validation completed\n\n";
    }
    
    /**
     * Validate WHMCS integration
     */
    private function validateIntegration() {
        echo "🔗 Validating WHMCS Integration...\n";
        
        // Test 1: WHMCS Functions
        $this->runValidation('WHMCS Functions', self::CATEGORY_INTEGRATION, function() {
            $requiredFunctions = [
                'getGatewayVariables',
                'addInvoicePayment',
                'logModuleCall'
            ];
            
            foreach ($requiredFunctions as $function) {
                if (!function_exists($function)) {
                    throw new Exception("Required WHMCS function not available: {$function}");
                }
            }
            
            return true;
        });
        
        // Test 2: Gateway Files
        $this->runValidation('Gateway Files', self::CATEGORY_INTEGRATION, function() {
            $gatewayFiles = [
                __DIR__ . '/../lahza.php',
                __DIR__ . '/../callback/lahza.php',
                __DIR__ . '/../callback/lahza_3ds.php'
            ];
            
            foreach ($gatewayFiles as $file) {
                if (!file_exists($file)) {
                    throw new Exception("Gateway file missing: " . basename($file));
                }
            }
            
            return true;
        });
        
        // Test 3: Template Integration
        $this->runValidation('Template Integration', self::CATEGORY_INTEGRATION, function() {
            $templatePath = ROOTDIR . '/templates/WIDDX';
            if (!is_dir($templatePath)) {
                throw new Exception('WIDDX template directory not found');
            }
            
            $requiredTemplates = [
                '3dsecure.tpl',
                'orderforms/modern/checkout.tpl'
            ];
            
            foreach ($requiredTemplates as $template) {
                $templateFile = $templatePath . '/' . $template;
                if (!file_exists($templateFile)) {
                    $this->warnings[] = "Template file missing: {$template}";
                }
            }
            
            return true;
        });
        
        echo "   Integration validation completed\n\n";
    }
    
    /**
     * Validate performance requirements
     */
    private function validatePerformance() {
        echo "⚡ Validating Performance...\n";
        
        // Test 1: Response Time
        $this->runValidation('Response Time', self::CATEGORY_PERFORMANCE, function() {
            $startTime = microtime(true);
            
            // Simulate gateway operation
            $testData = ['test' => 'performance'];
            $json = json_encode($testData);
            $decoded = json_decode($json, true);
            
            $endTime = microtime(true);
            $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
            
            if ($responseTime > 100) {
                $this->warnings[] = "Slow response time: {$responseTime}ms";
            }
            
            return true;
        });
        
        // Test 2: Memory Usage
        $this->runValidation('Memory Usage', self::CATEGORY_PERFORMANCE, function() {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
            
            if ($memoryLimit > 0 && $memoryUsage > ($memoryLimit * 0.8)) {
                $this->warnings[] = 'High memory usage detected';
            }
            
            return true;
        });
        
        // Test 3: Database Performance
        $this->runValidation('Database Performance', self::CATEGORY_PERFORMANCE, function() {
            $startTime = microtime(true);
            
            try {
                // Test query performance
                Capsule::table('tblinvoices')->limit(1)->get();
                
                $endTime = microtime(true);
                $queryTime = ($endTime - $startTime) * 1000;
                
                if ($queryTime > 50) {
                    $this->warnings[] = "Slow database query: {$queryTime}ms";
                }
                
                return true;
            } catch (Exception $e) {
                throw new Exception('Database performance test failed: ' . $e->getMessage());
            }
        });
        
        echo "   Performance validation completed\n\n";
    }
    
    /**
     * Validate compliance requirements
     */
    private function validateCompliance() {
        echo "📋 Validating Compliance...\n";
        
        // Test 1: PCI DSS Requirements
        $this->runValidation('PCI DSS Compliance', self::CATEGORY_COMPLIANCE, function() {
            // Check for secure transmission
            if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
                if (!isset($_SERVER['HTTP_X_FORWARDED_PROTO']) || $_SERVER['HTTP_X_FORWARDED_PROTO'] !== 'https') {
                    throw new Exception('HTTPS required for PCI DSS compliance');
                }
            }
            
            // Check for card data handling
            $gatewayFile = file_get_contents(__DIR__ . '/../lahza.php');
            if (strpos($gatewayFile, 'cvv') !== false && strpos($gatewayFile, 'unset') === false) {
                $this->warnings[] = 'Ensure CVV data is not stored or logged';
            }
            
            return true;
        });
        
        // Test 2: GDPR Compliance
        $this->runValidation('GDPR Compliance', self::CATEGORY_COMPLIANCE, function() {
            // Check for data retention policies
            $transactionManager = new EnhancedTransactionManager($this->gatewayParams);
            
            // This would check for data retention and cleanup policies
            // For now, we'll just verify the cleanup method exists
            if (!method_exists($transactionManager, 'cleanupOldTransactions')) {
                $this->warnings[] = 'Data retention cleanup method not found';
            }
            
            return true;
        });
        
        // Test 3: Accessibility Compliance
        $this->runValidation('Accessibility Compliance', self::CATEGORY_COMPLIANCE, function() {
            $templateFile = ROOTDIR . '/templates/WIDDX/3dsecure.tpl';
            if (file_exists($templateFile)) {
                $content = file_get_contents($templateFile);
                
                // Check for ARIA attributes
                if (strpos($content, 'aria-') === false) {
                    $this->warnings[] = 'ARIA attributes not found in templates';
                }
                
                // Check for alt attributes
                if (strpos($content, '<img') !== false && strpos($content, 'alt=') === false) {
                    $this->warnings[] = 'Image alt attributes missing';
                }
            }
            
            return true;
        });
        
        echo "   Compliance validation completed\n\n";
    }
    
    /**
     * Validate monitoring systems
     */
    private function validateMonitoring() {
        echo "📊 Validating Monitoring Systems...\n";
        
        // Test 1: Logging System
        $this->runValidation('Logging System', self::CATEGORY_MONITORING, function() {
            $logDir = __DIR__ . '/logs';
            if (!is_dir($logDir)) {
                if (!mkdir($logDir, 0755, true)) {
                    throw new Exception('Cannot create logs directory');
                }
            }
            
            if (!is_writable($logDir)) {
                throw new Exception('Logs directory is not writable');
            }
            
            // Test logging functionality
            $this->logger->info('Go-Live validation test', [], 'go_live_test');
            
            return true;
        });
        
        // Test 2: Error Handling
        $this->runValidation('Error Handling', self::CATEGORY_MONITORING, function() {
            // Check if error handling is properly implemented
            $gatewayFile = file_get_contents(__DIR__ . '/../lahza.php');
            
            if (strpos($gatewayFile, 'try {') === false || strpos($gatewayFile, 'catch') === false) {
                $this->warnings[] = 'Exception handling not found in gateway file';
            }
            
            return true;
        });
        
        // Test 3: Transaction Monitoring
        $this->runValidation('Transaction Monitoring', self::CATEGORY_MONITORING, function() {
            // Check if transaction manager exists and is functional
            $transactionManager = new EnhancedTransactionManager($this->gatewayParams);
            
            if (!method_exists($transactionManager, 'getTransactionStatistics')) {
                $this->warnings[] = 'Transaction statistics method not available';
            }
            
            return true;
        });
        
        echo "   Monitoring validation completed\n\n";
    }
    
    /**
     * Run individual validation test
     */
    private function runValidation($testName, $category, $testFunction) {
        try {
            $result = $testFunction();
            if ($result) {
                $this->validationResults[] = [
                    'name' => $testName,
                    'category' => $category,
                    'status' => 'PASS',
                    'score' => 10
                ];
                echo "   ✅ {$testName}\n";
            }
        } catch (Exception $e) {
            $this->criticalIssues[] = [
                'name' => $testName,
                'category' => $category,
                'error' => $e->getMessage()
            ];
            $this->validationResults[] = [
                'name' => $testName,
                'category' => $category,
                'status' => 'FAIL',
                'error' => $e->getMessage(),
                'score' => 0
            ];
            echo "   ❌ {$testName}: {$e->getMessage()}\n";
        }
    }
    
    /**
     * Calculate authorization score
     */
    private function calculateAuthorizationScore() {
        $totalScore = 0;
        $maxScore = 0;
        
        foreach ($this->validationResults as $result) {
            $totalScore += $result['score'];
            $maxScore += 10;
        }
        
        $this->authorizationScore = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 0;
        
        // Deduct points for warnings
        $warningPenalty = count($this->warnings) * 2;
        $this->authorizationScore = max(0, $this->authorizationScore - $warningPenalty);
    }
    
    /**
     * Generate authorization report
     */
    private function generateAuthorizationReport() {
        echo "\n📊 GO-LIVE AUTHORIZATION REPORT\n";
        echo str_repeat("=", 50) . "\n";
        
        echo "Authorization Score: " . round($this->authorizationScore, 2) . "/100\n";
        echo "Critical Issues: " . count($this->criticalIssues) . "\n";
        echo "Warnings: " . count($this->warnings) . "\n";
        echo "Tests Passed: " . count(array_filter($this->validationResults, function($r) { return $r['status'] === 'PASS'; })) . "\n";
        echo "Tests Failed: " . count($this->criticalIssues) . "\n\n";
        
        // Show critical issues
        if (!empty($this->criticalIssues)) {
            echo "❌ CRITICAL ISSUES:\n";
            foreach ($this->criticalIssues as $issue) {
                echo "   • {$issue['name']}: {$issue['error']}\n";
            }
            echo "\n";
        }
        
        // Show warnings
        if (!empty($this->warnings)) {
            echo "⚠️  WARNINGS:\n";
            foreach ($this->warnings as $warning) {
                echo "   • {$warning}\n";
            }
            echo "\n";
        }
        
        // Authorization decision
        $authorized = $this->isAuthorizedForGoLive();
        if ($authorized) {
            echo "🎉 AUTHORIZATION GRANTED\n";
            echo "System is approved for production go-live!\n\n";
            
            $this->generateAuthorizationCertificate();
        } else {
            echo "❌ AUTHORIZATION DENIED\n";
            echo "System requires fixes before production deployment.\n\n";
            
            echo "Requirements for authorization:\n";
            echo "• Authorization score must be ≥ {$this->getMinimumScore()}\n";
            echo "• Critical issues must be = 0\n";
            echo "• Warnings should be ≤ {$this->getWarningThreshold()}\n\n";
        }
        
        // Save report
        $this->saveAuthorizationReport();
    }
    
    /**
     * Check if system is authorized for go-live
     */
    private function isAuthorizedForGoLive() {
        return $this->authorizationScore >= self::MINIMUM_SCORE &&
               count($this->criticalIssues) <= self::CRITICAL_THRESHOLD &&
               count($this->warnings) <= self::WARNING_THRESHOLD;
    }
    
    /**
     * Generate authorization certificate
     */
    private function generateAuthorizationCertificate() {
        $certificate = [
            'authorization_id' => 'WIDDX-GOLIVE-' . date('Ymd-His'),
            'authorization_date' => date('Y-m-d H:i:s'),
            'system_version' => 'WIDDX v1.0.0',
            'authorization_score' => $this->authorizationScore,
            'critical_issues' => count($this->criticalIssues),
            'warnings' => count($this->warnings),
            'authorized_by' => 'WIDDX Go-Live Authorization System',
            'valid_until' => date('Y-m-d H:i:s', strtotime('+1 year')),
            'components_authorized' => [
                'WIDDX Theme',
                'Lahza Payment Gateway',
                'Enhanced 3D Secure',
                'Transaction Management',
                'Monitoring System'
            ]
        ];
        
        $certificateFile = __DIR__ . '/go_live_certificate.json';
        file_put_contents($certificateFile, json_encode($certificate, JSON_PRETTY_PRINT));
        
        echo "📜 Authorization certificate generated: {$certificateFile}\n";
    }
    
    /**
     * Save authorization report
     */
    private function saveAuthorizationReport() {
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'authorization_score' => $this->authorizationScore,
            'authorized' => $this->isAuthorizedForGoLive(),
            'validation_results' => $this->validationResults,
            'critical_issues' => $this->criticalIssues,
            'warnings' => $this->warnings
        ];
        
        $reportFile = __DIR__ . '/go_live_report_' . date('Ymd_His') . '.json';
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->logger->info('Go-Live authorization completed', [
            'score' => $this->authorizationScore,
            'authorized' => $this->isAuthorizedForGoLive(),
            'critical_issues' => count($this->criticalIssues),
            'warnings' => count($this->warnings)
        ], 'go_live');
    }
    
    /**
     * Helper methods
     */
    private function parseMemoryLimit($memoryLimit) {
        if ($memoryLimit === '-1') {
            return -1;
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }
    
    public function getMinimumScore() { return self::MINIMUM_SCORE; }
    public function getCriticalThreshold() { return self::CRITICAL_THRESHOLD; }
    public function getWarningThreshold() { return self::WARNING_THRESHOLD; }
    public function getAuthorizationScore() { return $this->authorizationScore; }
    public function getCriticalIssues() { return $this->criticalIssues; }
    public function getWarnings() { return $this->warnings; }
    public function getValidationResults() { return $this->validationResults; }
}
