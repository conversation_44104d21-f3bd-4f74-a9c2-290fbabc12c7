<?php
/**
 * WIDDX Accessibility and Responsive Test Suite
 * WCAG 2.1 compliance and responsive design validation
 * 
 * @package    WIDDX Accessibility Testing
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @version    1.0.0
 */

/**
 * Accessibility and responsive design test suite
 */
class AccessibilityTestSuite {
    
    private $issues = [];
    private $testCount = 0;
    private $passCount = 0;
    private $failCount = 0;
    
    /**
     * Run all accessibility tests
     */
    public function runAccessibilityTests() {
        echo "♿ WIDDX Accessibility Test Suite - WCAG 2.1 Compliance\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
        
        $this->testSemanticHTML();
        $this->testARIALabels();
        $this->testKeyboardNavigation();
        $this->testColorContrast();
        $this->testFocusManagement();
        $this->testScreenReaderSupport();
        $this->testResponsiveDesign();
        $this->testTouchTargets();
        $this->testFormAccessibility();
        $this->testMediaAccessibility();
        
        $this->generateAccessibilityReport();
    }
    
    /**
     * Test semantic HTML structure
     */
    private function testSemanticHTML() {
        echo "🏗️ Testing Semantic HTML Structure\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Semantic HTML Elements', function() {
            $templateFiles = [
                'templates/WIDDX/header.tpl',
                'templates/WIDDX/homepage.tpl',
                'templates/orderforms/widdx_modern/products.tpl'
            ];
            
            foreach ($templateFiles as $file) {
                $fullPath = __DIR__ . '/../' . $file;
                if (!file_exists($fullPath)) continue;
                
                $content = file_get_contents($fullPath);
                
                // Check for semantic elements
                $semanticElements = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'];
                $foundElements = [];
                
                foreach ($semanticElements as $element) {
                    if (preg_match("/<{$element}[^>]*>/", $content)) {
                        $foundElements[] = $element;
                    }
                }
                
                if (empty($foundElements)) {
                    $this->issues[] = "No semantic HTML elements found in {$file}";
                }
            }
            
            return true;
        });
        
        $this->accessibilityTest('Proper Heading Hierarchy', function() {
            $templateFiles = glob(__DIR__ . '/../templates/WIDDX/*.tpl');
            
            foreach ($templateFiles as $file) {
                $content = file_get_contents($file);
                
                // Extract headings
                preg_match_all('/<h([1-6])[^>]*>/', $content, $matches);
                $headingLevels = array_map('intval', $matches[1]);
                
                // Check heading hierarchy
                $previousLevel = 0;
                foreach ($headingLevels as $level) {
                    if ($level > $previousLevel + 1) {
                        $this->issues[] = "Heading hierarchy skip in {$file}: h{$previousLevel} to h{$level}";
                    }
                    $previousLevel = $level;
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test ARIA labels and attributes
     */
    private function testARIALabels() {
        echo "🏷️ Testing ARIA Labels and Attributes\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('ARIA Labels on Interactive Elements', function() {
            $templateFiles = [
                'templates/WIDDX/includes/navbar.tpl',
                'templates/orderforms/widdx_modern/products.tpl'
            ];
            
            foreach ($templateFiles as $file) {
                $fullPath = __DIR__ . '/../' . $file;
                if (!file_exists($fullPath)) continue;
                
                $content = file_get_contents($fullPath);
                
                // Check buttons without labels
                preg_match_all('/<button[^>]*>/', $content, $buttons);
                foreach ($buttons[0] as $button) {
                    if (strpos($button, 'aria-label') === false && 
                        strpos($button, 'aria-labelledby') === false) {
                        // Check if button has text content (would need DOM parsing for full check)
                        if (strpos($button, '>') === strlen($button) - 1) {
                            $this->issues[] = "Button without accessible label in {$file}";
                        }
                    }
                }
                
                // Check form inputs without labels
                preg_match_all('/<input[^>]*>/', $content, $inputs);
                foreach ($inputs[0] as $input) {
                    if (strpos($input, 'type="hidden"') !== false) continue;
                    
                    if (strpos($input, 'aria-label') === false && 
                        strpos($input, 'aria-labelledby') === false &&
                        !preg_match('/id=["\']([^"\']+)["\']/', $input, $idMatch)) {
                        $this->issues[] = "Input without accessible label in {$file}";
                    }
                }
            }
            
            return true;
        });
        
        $this->accessibilityTest('ARIA Roles', function() {
            $navbarFile = __DIR__ . '/../templates/WIDDX/includes/navbar.tpl';
            if (file_exists($navbarFile)) {
                $content = file_get_contents($navbarFile);
                
                // Check for navigation roles
                if (strpos($content, 'role="navigation"') === false && 
                    strpos($content, 'role="menubar"') === false) {
                    $this->issues[] = "Navigation missing ARIA role";
                }
                
                // Check for menu item roles
                if (strpos($content, 'role="menuitem"') === false) {
                    $this->issues[] = "Menu items missing ARIA roles";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test keyboard navigation
     */
    private function testKeyboardNavigation() {
        echo "⌨️ Testing Keyboard Navigation\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Focusable Elements', function() {
            $jsFile = __DIR__ . '/../templates/WIDDX/js/widdx-modern.js';
            if (file_exists($jsFile)) {
                $content = file_get_contents($jsFile);
                
                // Check for keyboard event handlers
                if (strpos($content, 'keydown') === false && strpos($content, 'keypress') === false) {
                    $this->issues[] = "No keyboard event handlers found in main JS file";
                }
                
                // Check for tabindex management
                if (strpos($content, 'tabindex') === false) {
                    $this->issues[] = "No tabindex management found";
                }
            }
            
            return true;
        });
        
        $this->accessibilityTest('Skip Links', function() {
            $headerFile = __DIR__ . '/../templates/WIDDX/header.tpl';
            if (file_exists($headerFile)) {
                $content = file_get_contents($headerFile);
                
                if (strpos($content, 'widdx-skip-link') === false) {
                    $this->issues[] = "Skip links not found in header";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test color contrast
     */
    private function testColorContrast() {
        echo "🎨 Testing Color Contrast\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('CSS Color Contrast', function() {
            $cssFile = __DIR__ . '/../templates/WIDDX/css/accessibility.css';
            if (file_exists($cssFile)) {
                $content = file_get_contents($cssFile);
                
                // Check for high contrast mode support
                if (strpos($content, 'prefers-contrast: high') === false) {
                    $this->issues[] = "High contrast mode not supported";
                }
                
                // Check for color contrast ratios in comments or variables
                if (strpos($content, '4.5:1') === false && strpos($content, 'contrast') === false) {
                    $this->issues[] = "No color contrast documentation found";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test focus management
     */
    private function testFocusManagement() {
        echo "🎯 Testing Focus Management\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Focus Indicators', function() {
            $accessibilityCSS = __DIR__ . '/../templates/WIDDX/css/accessibility.css';
            if (file_exists($accessibilityCSS)) {
                $content = file_get_contents($accessibilityCSS);
                
                // Check for focus styles
                if (strpos($content, ':focus') === false) {
                    $this->issues[] = "No focus styles defined";
                }
                
                // Check for focus-visible support
                if (strpos($content, 'focus-visible') === false) {
                    $this->issues[] = "Focus-visible not implemented";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test screen reader support
     */
    private function testScreenReaderSupport() {
        echo "🔊 Testing Screen Reader Support\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Screen Reader Only Content', function() {
            $accessibilityCSS = __DIR__ . '/../templates/WIDDX/css/accessibility.css';
            if (file_exists($accessibilityCSS)) {
                $content = file_get_contents($accessibilityCSS);
                
                // Check for screen reader only class
                if (strpos($content, 'widdx-sr-only') === false) {
                    $this->issues[] = "Screen reader only class not defined";
                }
            }
            
            return true;
        });
        
        $this->accessibilityTest('Live Regions', function() {
            $headerFile = __DIR__ . '/../templates/WIDDX/header.tpl';
            if (file_exists($headerFile)) {
                $content = file_get_contents($headerFile);
                
                // Check for ARIA live regions
                if (strpos($content, 'aria-live') === false) {
                    $this->issues[] = "No ARIA live regions found";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test responsive design
     */
    private function testResponsiveDesign() {
        echo "📱 Testing Responsive Design\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Viewport Meta Tag', function() {
            $headerFile = __DIR__ . '/../templates/WIDDX/header.tpl';
            if (file_exists($headerFile)) {
                $content = file_get_contents($headerFile);
                
                if (strpos($content, 'viewport') === false) {
                    $this->issues[] = "Viewport meta tag not found";
                }
            }
            
            return true;
        });
        
        $this->accessibilityTest('Media Queries', function() {
            $cssFiles = [
                'templates/WIDDX/css/theme.css',
                'templates/orderforms/widdx_modern/css/order-form.css'
            ];
            
            foreach ($cssFiles as $file) {
                $fullPath = __DIR__ . '/../' . $file;
                if (!file_exists($fullPath)) continue;
                
                $content = file_get_contents($fullPath);
                
                // Check for responsive breakpoints
                $breakpoints = ['768px', '992px', '1200px'];
                $foundBreakpoints = 0;
                
                foreach ($breakpoints as $breakpoint) {
                    if (strpos($content, $breakpoint) !== false) {
                        $foundBreakpoints++;
                    }
                }
                
                if ($foundBreakpoints < 2) {
                    $this->issues[] = "Insufficient responsive breakpoints in {$file}";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test touch targets
     */
    private function testTouchTargets() {
        echo "👆 Testing Touch Targets\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Touch Target Sizes', function() {
            $accessibilityCSS = __DIR__ . '/../templates/WIDDX/css/accessibility.css';
            if (file_exists($accessibilityCSS)) {
                $content = file_get_contents($accessibilityCSS);
                
                // Check for touch target sizing
                if (strpos($content, '44px') === false && strpos($content, 'pointer: coarse') === false) {
                    $this->issues[] = "Touch target sizing not implemented";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test form accessibility
     */
    private function testFormAccessibility() {
        echo "📝 Testing Form Accessibility\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Form Labels and Descriptions', function() {
            $orderFormJS = __DIR__ . '/../templates/orderforms/widdx_modern/js/order-form.js';
            if (file_exists($orderFormJS)) {
                $content = file_get_contents($orderFormJS);
                
                // Check for form validation with accessibility
                if (strpos($content, 'aria-invalid') === false) {
                    $this->issues[] = "Form validation doesn't use aria-invalid";
                }
                
                // Check for error announcements
                if (strpos($content, 'announceToScreenReader') === false) {
                    $this->issues[] = "Form errors not announced to screen readers";
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Test media accessibility
     */
    private function testMediaAccessibility() {
        echo "🎬 Testing Media Accessibility\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $this->accessibilityTest('Image Alt Text', function() {
            $templateFiles = glob(__DIR__ . '/../templates/WIDDX/*.tpl');
            $templateFiles = array_merge($templateFiles, glob(__DIR__ . '/../templates/orderforms/widdx_modern/*.tpl'));
            
            foreach ($templateFiles as $file) {
                $content = file_get_contents($file);
                
                // Check for images without alt text
                preg_match_all('/<img[^>]*>/', $content, $images);
                foreach ($images[0] as $img) {
                    if (strpos($img, 'alt=') === false) {
                        $this->issues[] = "Image without alt text in " . basename($file);
                    }
                }
            }
            
            return true;
        });
        
        echo "\n";
    }
    
    /**
     * Run individual accessibility test
     */
    private function accessibilityTest($name, $callback) {
        $this->testCount++;
        
        try {
            $result = $callback();
            if ($result) {
                echo "✅ {$name}\n";
                $this->passCount++;
            } else {
                echo "❌ {$name} - Test returned false\n";
                $this->failCount++;
            }
        } catch (Exception $e) {
            echo "❌ {$name} - {$e->getMessage()}\n";
            $this->failCount++;
            $this->issues[] = "{$name}: {$e->getMessage()}";
        }
    }
    
    /**
     * Generate accessibility report
     */
    private function generateAccessibilityReport() {
        echo "\n" . "=" . str_repeat("=", 60) . "\n";
        echo "♿ Accessibility Assessment Report\n";
        echo "=" . str_repeat("=", 60) . "\n";
        
        echo "Accessibility Tests: {$this->testCount}\n";
        echo "Passed: {$this->passCount}\n";
        echo "Failed: {$this->failCount}\n";
        echo "Issues Found: " . count($this->issues) . "\n\n";
        
        if (!empty($this->issues)) {
            echo "⚠️ Accessibility Issues:\n";
            foreach ($this->issues as $issue) {
                echo "  - {$issue}\n";
            }
            echo "\n";
        }
        
        // WCAG compliance score
        $complianceScore = ($this->passCount / $this->testCount) * 100;
        $issuePenalty = count($this->issues) * 3;
        $finalScore = max(0, $complianceScore - $issuePenalty);
        
        echo "WCAG 2.1 Compliance Score: " . round($finalScore, 1) . "/100\n";
        
        if ($finalScore >= 95) {
            echo "🟢 Accessibility Status: AAA COMPLIANT\n";
        } elseif ($finalScore >= 85) {
            echo "🟢 Accessibility Status: AA COMPLIANT\n";
        } elseif ($finalScore >= 70) {
            echo "🟡 Accessibility Status: A COMPLIANT\n";
        } else {
            echo "🔴 Accessibility Status: NON-COMPLIANT - Critical issues\n";
        }
        
        // Save accessibility report
        $reportData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'wcag_score' => round($finalScore, 1),
            'tests_passed' => $this->passCount,
            'tests_failed' => $this->failCount,
            'issues' => $this->issues,
            'compliance_level' => $finalScore >= 85 ? 'AA' : ($finalScore >= 70 ? 'A' : 'Non-compliant')
        ];
        
        $reportFile = __DIR__ . '/reports/accessibility_report_' . date('Y-m-d_H-i-s') . '.json';
        @mkdir(dirname($reportFile), 0755, true);
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT));
        
        echo "\n📄 Accessibility report saved to: {$reportFile}\n";
    }
}

// Run accessibility tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $accessibilitySuite = new AccessibilityTestSuite();
    $accessibilitySuite->runAccessibilityTests();
}
