<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/AIService.php';

header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['question']) || empty(trim($input['question']))) {
        throw new Exception('Question is required');
    }
    
    $question = trim($input['question']);
    $aiService = new AIService();
    $response = $aiService->processQuestion($question);
    
    echo json_encode([
        'status' => 'success',
        'data' => $response
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
