<?php

/**
 * 3D Secure Initialization Endpoint for WIDDX
 * 
 * Handles enhanced 3D Secure 2.2.0 initialization with:
 * - Advanced browser fingerprinting
 * - Risk assessment
 * - Challenge flow determination
 * - Mobile optimization
 * 
 * @package WIDDX
 * @subpackage PaymentGateway
 * @version 1.0.0
 */

// Security headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: SAMEORIGIN');
header('X-XSS-Protection: 1; mode=block');

// CORS headers for AJAX requests
header('Access-Control-Allow-Origin: ' . ($_SERVER['HTTP_ORIGIN'] ?? '*'));
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Initialize WHMCS environment
$init_path = __DIR__ . '/../../init.php';
if (file_exists($init_path)) {
    define('CLIENTAREA', true);
    require_once $init_path;
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'WHMCS environment not available']);
    exit;
}

require_once __DIR__ . '/Enhanced3DSecure.php';
require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/EnhancedTransactionManager.php';

class ThreeDSInitializer {
    
    private $enhanced3DS;
    private $logger;
    private $transactionManager;
    private $gatewayParams;
    
    public function __construct() {
        // Load gateway parameters
        $this->gatewayParams = getGatewayVariables('lahza');
        
        $this->logger = new LahzaLogger();
        $this->transactionManager = new EnhancedTransactionManager($this->gatewayParams, $this->logger);
        $this->enhanced3DS = new Enhanced3DSecure($this->gatewayParams, $this->logger, $this->transactionManager);
    }
    
    /**
     * Handle 3D Secure initialization request
     */
    public function handleRequest() {
        try {
            // Validate request method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->sendError('Method not allowed', 405);
                return;
            }
            
            // Validate content type
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (strpos($contentType, 'application/json') === false) {
                $this->sendError('Invalid content type', 400);
                return;
            }
            
            // Get and validate input data
            $input = $this->getInputData();
            if (!$input) {
                $this->sendError('Invalid JSON data', 400);
                return;
            }
            
            // Validate required fields
            $validation = $this->validateInput($input);
            if (!$validation['valid']) {
                $this->sendError($validation['message'], 400);
                return;
            }
            
            // Rate limiting check
            if (!$this->checkRateLimit()) {
                $this->sendError('Rate limit exceeded', 429);
                return;
            }
            
            // Process 3D Secure initialization
            $result = $this->initialize3DSecure($input);
            
            if ($result['success']) {
                $this->sendResponse($result);
            } else {
                $this->sendError($result['message'], $result['code'] ?? 400);
            }
            
        } catch (Exception $e) {
            $this->logger->error('3DS initialization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], '3ds');
            
            $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * Get and validate input data
     */
    private function getInputData() {
        $rawInput = file_get_contents('php://input');
        if (empty($rawInput)) {
            return null;
        }
        
        $input = json_decode($rawInput, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }
        
        return $input;
    }
    
    /**
     * Validate input data
     */
    private function validateInput($input) {
        $required = ['invoiceId', 'amount', 'currency', 'cardData', 'browserInfo'];
        
        foreach ($required as $field) {
            if (!isset($input[$field])) {
                return [
                    'valid' => false,
                    'message' => "Missing required field: {$field}"
                ];
            }
        }
        
        // Validate invoice ID
        if (!is_numeric($input['invoiceId']) || $input['invoiceId'] <= 0) {
            return [
                'valid' => false,
                'message' => 'Invalid invoice ID'
            ];
        }
        
        // Validate amount
        if (!is_numeric($input['amount']) || $input['amount'] <= 0) {
            return [
                'valid' => false,
                'message' => 'Invalid amount'
            ];
        }
        
        // Validate currency
        if (!preg_match('/^[A-Z]{3}$/', $input['currency'])) {
            return [
                'valid' => false,
                'message' => 'Invalid currency code'
            ];
        }
        
        // Validate card data
        $cardData = $input['cardData'];
        $cardRequired = ['number', 'name', 'expiry_month', 'expiry_year'];
        
        foreach ($cardRequired as $field) {
            if (!isset($cardData[$field]) || empty($cardData[$field])) {
                return [
                    'valid' => false,
                    'message' => "Missing card field: {$field}"
                ];
            }
        }
        
        // Validate card number (basic check)
        $cardNumber = preg_replace('/\D/', '', $cardData['number']);
        if (strlen($cardNumber) < 13 || strlen($cardNumber) > 19) {
            return [
                'valid' => false,
                'message' => 'Invalid card number'
            ];
        }
        
        // Validate expiry
        $month = intval($cardData['expiry_month']);
        $year = intval($cardData['expiry_year']);
        
        if ($month < 1 || $month > 12) {
            return [
                'valid' => false,
                'message' => 'Invalid expiry month'
            ];
        }
        
        $currentYear = intval(date('Y'));
        if ($year < $currentYear || $year > ($currentYear + 20)) {
            return [
                'valid' => false,
                'message' => 'Invalid expiry year'
            ];
        }
        
        // Validate browser info
        $browserInfo = $input['browserInfo'];
        $browserRequired = ['userAgent', 'acceptHeader', 'browserLanguage'];
        
        foreach ($browserRequired as $field) {
            if (!isset($browserInfo[$field])) {
                return [
                    'valid' => false,
                    'message' => "Missing browser field: {$field}"
                ];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * Initialize 3D Secure authentication
     */
    private function initialize3DSecure($input) {
        $this->logger->info('Initializing 3D Secure authentication', [
            'invoice_id' => $input['invoiceId'],
            'amount' => $input['amount'],
            'currency' => $input['currency'],
            'browser_fingerprint' => $input['browserInfo']['fingerprint'] ?? 'not_provided'
        ], '3ds');
        
        try {
            // Prepare payment parameters
            $params = $this->preparePaymentParams($input);
            
            // Initialize 3D Secure
            $threeDSData = $this->enhanced3DS->initializeAuthentication($params, $input['cardData']);
            
            // Send authentication request to gateway
            $gatewayResponse = $this->sendGatewayRequest($threeDSData);
            
            if ($gatewayResponse['success']) {
                return $this->processGatewayResponse($gatewayResponse, $threeDSData);
            } else {
                return [
                    'success' => false,
                    'message' => $gatewayResponse['message'] ?? '3D Secure initialization failed',
                    'code' => 400
                ];
            }
            
        } catch (Exception $e) {
            $this->logger->error('3DS initialization exception', [
                'invoice_id' => $input['invoiceId'],
                'error' => $e->getMessage()
            ], '3ds');
            
            return [
                'success' => false,
                'message' => 'Failed to initialize 3D Secure authentication',
                'code' => 500
            ];
        }
    }
    
    /**
     * Prepare payment parameters
     */
    private function preparePaymentParams($input) {
        // Get invoice details
        $invoice = $this->getInvoiceDetails($input['invoiceId']);
        if (!$invoice) {
            throw new Exception('Invoice not found');
        }
        
        // Get client details
        $client = $this->getClientDetails($invoice['userid']);
        if (!$client) {
            throw new Exception('Client not found');
        }
        
        return [
            'invoiceid' => $input['invoiceId'],
            'amount' => $input['amount'],
            'currency' => $input['currency'],
            'companyname' => $this->gatewayParams['companyname'] ?? 'WIDDX Merchant',
            'systemurl' => $this->gatewayParams['systemurl'] ?? '',
            'returnurl' => $this->gatewayParams['returnurl'] ?? '',
            'publicKey' => $this->gatewayParams['publicKey'],
            'secretKey' => $this->gatewayParams['secretKey'],
            'clientdetails' => [
                'userid' => $client['id'],
                'email' => $client['email'],
                'firstname' => $client['firstname'],
                'lastname' => $client['lastname'],
                'country' => $client['country']
            ]
        ];
    }
    
    /**
     * Send request to payment gateway
     */
    private function sendGatewayRequest($threeDSData) {
        $apiUrl = $this->gatewayParams['testMode'] === 'on' 
            ? 'https://api-sandbox.lahza.io/3ds/initialize'
            : 'https://api.lahza.io/3ds/initialize';
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->gatewayParams['publicKey'],
            'X-Signature: ' . $this->generateSignature($threeDSData),
            'User-Agent: WIDDX-3DS/1.0.0'
        ];
        
        $ch = curl_init($apiUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($threeDSData),
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->logger->error('Gateway request failed', [
                'error' => $error,
                'transaction_id' => $threeDSData['transaction_id']
            ], '3ds');
            
            return ['success' => false, 'message' => 'Gateway communication error'];
        }
        
        if ($httpCode !== 200) {
            $this->logger->error('Gateway returned error', [
                'http_code' => $httpCode,
                'response' => $response,
                'transaction_id' => $threeDSData['transaction_id']
            ], '3ds');
            
            return ['success' => false, 'message' => 'Gateway error'];
        }
        
        $responseData = json_decode($response, true);
        if (!$responseData) {
            return ['success' => false, 'message' => 'Invalid gateway response'];
        }
        
        return ['success' => true, 'data' => $responseData];
    }
    
    /**
     * Process gateway response
     */
    private function processGatewayResponse($gatewayResponse, $threeDSData) {
        $data = $gatewayResponse['data'];
        $transactionId = $threeDSData['transaction_id'];
        
        $this->logger->info('Processing gateway response', [
            'transaction_id' => $transactionId,
            'status' => $data['status'] ?? 'unknown',
            'challenge_required' => $data['challenge_required'] ?? false
        ], '3ds');
        
        if (isset($data['challenge_required']) && $data['challenge_required']) {
            // Challenge flow required
            return [
                'success' => true,
                'transactionId' => $transactionId,
                'challengeRequired' => true,
                'challengeUrl' => $data['challenge_url'],
                'challengeWindowSize' => $data['challenge_window_size'] ?? '05',
                'challengeTimeout' => $data['challenge_timeout'] ?? 300
            ];
        } else {
            // Frictionless flow
            return [
                'success' => true,
                'transactionId' => $transactionId,
                'challengeRequired' => false,
                'authenticationStatus' => $data['authentication_status'] ?? 'Y',
                'authenticationData' => [
                    'eci' => $data['eci'] ?? '',
                    'cavv' => $data['cavv'] ?? '',
                    'xid' => $data['xid'] ?? ''
                ]
            ];
        }
    }
    
    /**
     * Generate request signature
     */
    private function generateSignature($data) {
        return hash_hmac('sha256', json_encode($data), $this->gatewayParams['secretKey']);
    }
    
    /**
     * Get invoice details
     */
    private function getInvoiceDetails($invoiceId) {
        try {
            return Capsule::table('tblinvoices')
                ->where('id', $invoiceId)
                ->first();
        } catch (Exception $e) {
            $this->logger->error('Failed to get invoice details', [
                'invoice_id' => $invoiceId,
                'error' => $e->getMessage()
            ], '3ds');
            return null;
        }
    }
    
    /**
     * Get client details
     */
    private function getClientDetails($userId) {
        try {
            return Capsule::table('tblclients')
                ->where('id', $userId)
                ->first();
        } catch (Exception $e) {
            $this->logger->error('Failed to get client details', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ], '3ds');
            return null;
        }
    }
    
    /**
     * Rate limiting check
     */
    private function checkRateLimit() {
        $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cacheKey = "3ds_rate_limit_{$clientIP}";
        $cacheFile = __DIR__ . '/cache/' . md5($cacheKey) . '.cache';
        
        $maxRequests = 10; // per minute
        $timeWindow = 60; // seconds
        
        if (!is_dir(dirname($cacheFile))) {
            mkdir(dirname($cacheFile), 0755, true);
        }
        
        $now = time();
        $requests = [];
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data && isset($data['requests'])) {
                $requests = array_filter($data['requests'], function($timestamp) use ($now, $timeWindow) {
                    return ($now - $timestamp) < $timeWindow;
                });
            }
        }
        
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        $requests[] = $now;
        file_put_contents($cacheFile, json_encode(['requests' => $requests]), LOCK_EX);
        
        return true;
    }
    
    /**
     * Send JSON response
     */
    private function sendResponse($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('c')
        ], JSON_PRETTY_PRINT);
    }
    
    /**
     * Send error response
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('c')
        ], JSON_PRETTY_PRINT);
    }
}

// Handle the request
$initializer = new ThreeDSInitializer();
$initializer->handleRequest();
