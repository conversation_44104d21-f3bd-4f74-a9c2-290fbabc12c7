<?php

/**
 * WIDDX Go-Live Authorization Script
 * 
 * Comprehensive production readiness validation and authorization
 * 
 * @package WIDDX
 * @version 1.0.0
 */

// Set execution time limit
set_time_limit(300); // 5 minutes

// Initialize WHMCS environment
$init_path = __DIR__ . '/../init.php';
if (file_exists($init_path)) {
    define('CLIENTAREA', true);
    require_once $init_path;
} else {
    die("❌ WHMCS environment not found. Please run from WHMCS root directory.\n");
}

require_once __DIR__ . '/../modules/gateways/lahza/GoLiveAuthorization.php';

/**
 * Go-Live Authorization Runner
 */
class GoLiveAuthorizationRunner {
    
    private $authorization;
    private $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->authorization = new GoLiveAuthorization();
    }
    
    /**
     * Run the complete go-live authorization process
     */
    public function run() {
        $this->showBanner();
        $this->checkPrerequisites();
        
        echo "🔍 Starting comprehensive production readiness validation...\n\n";
        
        // Run authorization
        $authorized = $this->authorization->runAuthorization();
        
        // Show final results
        $this->showFinalResults($authorized);
        
        // Generate deployment instructions
        if ($authorized) {
            $this->generateDeploymentInstructions();
        } else {
            $this->generateFixInstructions();
        }
        
        $this->showExecutionTime();
        
        return $authorized;
    }
    
    /**
     * Show authorization banner
     */
    private function showBanner() {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                                                              ║\n";
        echo "║              🚀 WIDDX GO-LIVE AUTHORIZATION 🚀               ║\n";
        echo "║                                                              ║\n";
        echo "║           Production Readiness Validation System            ║\n";
        echo "║                        Version 1.0.0                        ║\n";
        echo "║                                                              ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
        echo "📅 Authorization Date: " . date('Y-m-d H:i:s') . "\n";
        echo "🏢 System: WIDDX Theme + Lahza Payment Gateway\n";
        echo "🎯 Target: Production Go-Live Authorization\n\n";
    }
    
    /**
     * Check prerequisites
     */
    private function checkPrerequisites() {
        echo "🔧 Checking Prerequisites...\n";
        
        // Check PHP version
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            die("❌ PHP 7.4 or higher is required. Current version: " . PHP_VERSION . "\n");
        }
        echo "   ✅ PHP Version: " . PHP_VERSION . "\n";
        
        // Check required extensions
        $requiredExtensions = ['curl', 'json', 'openssl', 'mbstring', 'pdo'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                die("❌ Required PHP extension not loaded: {$ext}\n");
            }
        }
        echo "   ✅ Required PHP Extensions: Available\n";
        
        // Check WHMCS
        if (!defined('WHMCS')) {
            die("❌ WHMCS environment not properly initialized\n");
        }
        echo "   ✅ WHMCS Environment: Initialized\n";
        
        // Check gateway files
        $gatewayFile = __DIR__ . '/../modules/gateways/lahza.php';
        if (!file_exists($gatewayFile)) {
            die("❌ Lahza gateway file not found: {$gatewayFile}\n");
        }
        echo "   ✅ Gateway Files: Present\n";
        
        echo "   Prerequisites check completed\n\n";
    }
    
    /**
     * Show final authorization results
     */
    private function showFinalResults($authorized) {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                    AUTHORIZATION RESULTS                    ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $score = $this->authorization->getAuthorizationScore();
        $criticalIssues = count($this->authorization->getCriticalIssues());
        $warnings = count($this->authorization->getWarnings());
        $validationResults = $this->authorization->getValidationResults();
        $passedTests = count(array_filter($validationResults, function($r) { return $r['status'] === 'PASS'; }));
        $totalTests = count($validationResults);
        
        echo "📊 VALIDATION SUMMARY:\n";
        echo "   • Authorization Score: " . round($score, 2) . "/100\n";
        echo "   • Tests Passed: {$passedTests}/{$totalTests}\n";
        echo "   • Critical Issues: {$criticalIssues}\n";
        echo "   • Warnings: {$warnings}\n\n";
        
        // Show score interpretation
        if ($score >= 95) {
            echo "🏆 EXCELLENT: System exceeds production standards\n";
        } elseif ($score >= 90) {
            echo "✅ GOOD: System meets production standards\n";
        } elseif ($score >= 80) {
            echo "⚠️  FAIR: System needs minor improvements\n";
        } else {
            echo "❌ POOR: System requires significant improvements\n";
        }
        
        echo "\n";
        
        // Authorization decision
        if ($authorized) {
            echo "🎉 AUTHORIZATION STATUS: ✅ GRANTED\n";
            echo "\n";
            echo "┌─────────────────────────────────────────────────────────────┐\n";
            echo "│                                                             │\n";
            echo "│  🚀 PRODUCTION GO-LIVE APPROVED 🚀                         │\n";
            echo "│                                                             │\n";
            echo "│  Your WIDDX system is authorized for production            │\n";
            echo "│  deployment and live customer traffic.                     │\n";
            echo "│                                                             │\n";
            echo "│  Authorization ID: WIDDX-GOLIVE-" . date('Ymd-His') . "        │\n";
            echo "│                                                             │\n";
            echo "└─────────────────────────────────────────────────────────────┘\n";
        } else {
            echo "❌ AUTHORIZATION STATUS: DENIED\n";
            echo "\n";
            echo "┌─────────────────────────────────────────────────────────────┐\n";
            echo "│                                                             │\n";
            echo "│  ⚠️  PRODUCTION GO-LIVE DENIED ⚠️                           │\n";
            echo "│                                                             │\n";
            echo "│  System requires fixes before production deployment.       │\n";
            echo "│  Please address all critical issues and re-run             │\n";
            echo "│  authorization.                                             │\n";
            echo "│                                                             │\n";
            echo "└─────────────────────────────────────────────────────────────┘\n";
        }
        
        echo "\n";
    }
    
    /**
     * Generate deployment instructions for authorized systems
     */
    private function generateDeploymentInstructions() {
        echo "📋 DEPLOYMENT INSTRUCTIONS:\n";
        echo "\n";
        echo "1. 🔧 FINAL CONFIGURATION:\n";
        echo "   • Switch gateway to live mode (testMode = 'off')\n";
        echo "   • Update API keys to production keys\n";
        echo "   • Verify SSL certificate is valid\n";
        echo "   • Enable 3D Secure for production\n";
        echo "\n";
        echo "2. 🚀 DEPLOYMENT STEPS:\n";
        echo "   • Deploy to production environment\n";
        echo "   • Run final smoke tests\n";
        echo "   • Monitor first transactions closely\n";
        echo "   • Verify webhook endpoints are accessible\n";
        echo "\n";
        echo "3. 📊 POST-LAUNCH MONITORING:\n";
        echo "   • Monitor payment success rates\n";
        echo "   • Watch for error rates and alerts\n";
        echo "   • Review transaction logs regularly\n";
        echo "   • Conduct weekly security reviews\n";
        echo "\n";
        echo "4. 🆘 SUPPORT CONTACTS:\n";
        echo "   • Technical Support: Available 24/7\n";
        echo "   • Emergency Escalation: Immediate response\n";
        echo "   • Documentation: Complete implementation guides\n";
        echo "\n";
        
        $this->generatePostLaunchChecklist();
    }
    
    /**
     * Generate fix instructions for denied authorization
     */
    private function generateFixInstructions() {
        echo "🔧 REQUIRED FIXES:\n";
        echo "\n";
        
        $criticalIssues = $this->authorization->getCriticalIssues();
        if (!empty($criticalIssues)) {
            echo "❌ CRITICAL ISSUES (Must Fix):\n";
            foreach ($criticalIssues as $issue) {
                echo "   • {$issue['name']}: {$issue['error']}\n";
                echo "     Category: {$issue['category']}\n";
                echo "     Priority: HIGH\n\n";
            }
        }
        
        $warnings = $this->authorization->getWarnings();
        if (!empty($warnings)) {
            echo "⚠️  WARNINGS (Recommended):\n";
            foreach ($warnings as $warning) {
                echo "   • {$warning}\n";
            }
            echo "\n";
        }
        
        echo "📋 NEXT STEPS:\n";
        echo "1. Address all critical issues listed above\n";
        echo "2. Review and fix warnings where possible\n";
        echo "3. Re-run authorization: php deployment/go-live-authorization.php\n";
        echo "4. Ensure authorization score reaches 95+\n";
        echo "\n";
        
        echo "💡 TIPS FOR QUICK FIXES:\n";
        echo "• Check file permissions (755 for directories, 644 for files)\n";
        echo "• Verify all required API keys are configured\n";
        echo "• Ensure HTTPS is properly configured\n";
        echo "• Test database connectivity\n";
        echo "\n";
    }
    
    /**
     * Generate post-launch checklist
     */
    private function generatePostLaunchChecklist() {
        $checklist = [
            'immediate' => [
                'Monitor first 10 transactions',
                'Verify webhook delivery',
                'Check error logs',
                'Test 3D Secure flow',
                'Validate payment confirmations'
            ],
            'first_24_hours' => [
                'Review transaction success rates',
                'Monitor system performance',
                'Check security alerts',
                'Verify customer experience',
                'Review support tickets'
            ],
            'first_week' => [
                'Analyze payment analytics',
                'Review security logs',
                'Optimize performance',
                'Update documentation',
                'Plan regular maintenance'
            ]
        ];
        
        echo "✅ POST-LAUNCH CHECKLIST:\n";
        echo "\n";
        
        echo "🚨 IMMEDIATE (First Hour):\n";
        foreach ($checklist['immediate'] as $item) {
            echo "   □ {$item}\n";
        }
        echo "\n";
        
        echo "📅 FIRST 24 HOURS:\n";
        foreach ($checklist['first_24_hours'] as $item) {
            echo "   □ {$item}\n";
        }
        echo "\n";
        
        echo "📊 FIRST WEEK:\n";
        foreach ($checklist['first_week'] as $item) {
            echo "   □ {$item}\n";
        }
        echo "\n";
        
        // Save checklist to file
        $checklistFile = __DIR__ . '/post_launch_checklist.md';
        $this->savePostLaunchChecklist($checklistFile, $checklist);
        echo "📄 Post-launch checklist saved: {$checklistFile}\n\n";
    }
    
    /**
     * Save post-launch checklist to file
     */
    private function savePostLaunchChecklist($filename, $checklist) {
        $content = "# WIDDX Post-Launch Checklist\n\n";
        $content .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        $content .= "## Immediate Actions (First Hour)\n\n";
        foreach ($checklist['immediate'] as $item) {
            $content .= "- [ ] {$item}\n";
        }
        
        $content .= "\n## First 24 Hours\n\n";
        foreach ($checklist['first_24_hours'] as $item) {
            $content .= "- [ ] {$item}\n";
        }
        
        $content .= "\n## First Week\n\n";
        foreach ($checklist['first_week'] as $item) {
            $content .= "- [ ] {$item}\n";
        }
        
        $content .= "\n## Emergency Contacts\n\n";
        $content .= "- **Technical Support**: Available 24/7\n";
        $content .= "- **Emergency Escalation**: Immediate response\n";
        $content .= "- **Documentation**: Complete implementation guides\n";
        
        file_put_contents($filename, $content);
    }
    
    /**
     * Show execution time
     */
    private function showExecutionTime() {
        $endTime = microtime(true);
        $executionTime = round($endTime - $this->startTime, 2);
        
        echo "⏱️  Authorization completed in {$executionTime} seconds\n";
        echo "📅 Completed at: " . date('Y-m-d H:i:s') . "\n\n";
    }
}

// Run authorization if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $runner = new GoLiveAuthorizationRunner();
    $authorized = $runner->run();
    
    // Exit with appropriate code
    exit($authorized ? 0 : 1);
}
