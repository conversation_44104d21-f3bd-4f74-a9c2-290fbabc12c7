# Environment: development | staging | production
APP_ENV=development
APP_DEBUG=true
APP_NAME="WIDDX AI Assistant"
APP_URL=http://localhost:8000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=widdx_ai
DB_USERNAME=widdx
DB_PASSWORD=widdx

# API Keys (keep these secret!)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Security
APP_KEY=base64:generate_a_secure_random_key_here
JWT_SECRET=generate_a_secure_random_secret_here

# Session & Cache
SESSION_DRIVER=file
CACHE_DRIVER=file

# Email Configuration
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=debug

# CORS (Cross-Origin Resource Sharing)
CORS_ALLOWED_ORIGINS=

# Rate Limiting
RATE_LIMIT=60
RATE_LIMIT_WINDOW=60

# File Uploads
UPLOAD_MAX_SIZE=2048
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt

# Timezone
APP_TIMEZONE=Asia/Riyadh

# Locale
APP_LOCALE=ar
APP_FALLBACK_LOCALE=en

# Maintenance Mode
MAINTENANCE_MODE=false
