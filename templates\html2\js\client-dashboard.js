// ===== IMPROVED CLIENT DASHBOARD FUNCTIONALITY =====

// Dashboard State Management
class DashboardState {
    constructor() {
        this.user = null;
        this.stats = {
            services: 0,
            domains: 0,
            tickets: 0,
            invoices: 0
        };
        this.services = [];
        this.domains = [];
        this.tickets = [];
        this.invoices = [];
        this.contacts = [];
        this.isLoading = false;
        this.error = null;
    }

    setUser(userData) {
        this.user = userData;
        this.notifyStateChange('user');
    }

    setStats(statsData) {
        this.stats = { ...this.stats, ...statsData };
        this.notifyStateChange('stats');
    }

    setServices(servicesData) {
        this.services = servicesData;
        this.stats.services = servicesData.length;
        this.notifyStateChange('services');
    }

    setError(error) {
        this.error = error;
        this.notifyStateChange('error');
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.notifyStateChange('loading');
    }

    notifyStateChange(type) {
        window.dispatchEvent(new CustomEvent('dashboardStateChange', {
            detail: { type, state: this }
        }));
    }
}

// Global dashboard state
const dashboardState = new DashboardState();

// API Service for data management
class DashboardAPI {
    constructor() {
        this.baseURL = '/api'; // Replace with actual API endpoint
        this.mockMode = true; // Set to false for production
    }

    async fetchUserData() {
        if (this.mockMode) {
            return this.getMockUserData();
        }

        try {
            const response = await fetch(`${this.baseURL}/user`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching user data:', error);
            throw error;
        }
    }

    async fetchDashboardStats() {
        if (this.mockMode) {
            return this.getMockStats();
        }

        try {
            const response = await fetch(`${this.baseURL}/dashboard/stats`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching dashboard stats:', error);
            throw error;
        }
    }

    async fetchServices() {
        if (this.mockMode) {
            return this.getMockServices();
        }

        try {
            const response = await fetch(`${this.baseURL}/services`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching services:', error);
            throw error;
        }
    }

    // Mock data methods
    getMockUserData() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    username: 'castlekh',
                    fullName: 'nosaiba khateb',
                    email: '<EMAIL>',
                    city: 'kfar kana',
                    address: 'kafr kana, kafr kana, 1693000',
                    country: 'Israel',
                    firstName: 'nosaiba'
                });
            }, 500);
        });
    }

    getMockStats() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    services: 0,
                    domains: 0,
                    tickets: 0,
                    invoices: 0
                });
            }, 300);
        });
    }

    getMockServices() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve([]);
            }, 400);
        });
    }
}

// Dashboard API instance
const dashboardAPI = new DashboardAPI();

// UI Manager for handling UI updates
class UIManager {
    constructor() {
        this.elements = this.cacheElements();
        this.setupEventListeners();
    }

    cacheElements() {
        return {
            loadingOverlay: document.getElementById('loadingOverlay'),
            errorMessage: document.getElementById('errorMessage'),
            userName: document.getElementById('userName'),
            dashboardUserName: document.getElementById('dashboardUserName'),
            sidebarUserName: document.getElementById('sidebarUserName'),
            userFullName: document.getElementById('userFullName'),
            userCity: document.getElementById('userCity'),
            userAddress: document.getElementById('userAddress'),
            userCountry: document.getElementById('userCountry'),
            servicesCount: document.getElementById('servicesCount'),
            domainsCount: document.getElementById('domainsCount'),
            ticketsCount: document.getElementById('ticketsCount'),
            invoicesCount: document.getElementById('invoicesCount'),
            contentSections: document.getElementById('contentSections'),
            knowledgebaseSearch: document.getElementById('knowledgebaseSearch')
        };
    }

    setupEventListeners() {
        // Listen for state changes
        window.addEventListener('dashboardStateChange', (e) => {
            this.handleStateChange(e.detail);
        });

        // Setup user menu
        this.setupUserMenu();
    }

    setupUserMenu() {
        const userMenuToggle = document.getElementById('userMenuToggle');
        const userDropdown = document.getElementById('userDropdown');

        if (userMenuToggle && userDropdown) {
            userMenuToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });

            document.addEventListener('click', (e) => {
                if (!userMenuToggle.contains(e.target) && !userDropdown.contains(e.target)) {
                    userDropdown.classList.remove('show');
                }
            });
        }
    }

    handleStateChange({ type, state }) {
        switch (type) {
            case 'loading':
                this.updateLoadingState(state.isLoading);
                break;
            case 'error':
                this.updateErrorState(state.error);
                break;
            case 'user':
                this.updateUserInfo(state.user);
                break;
            case 'stats':
                this.updateStats(state.stats);
                break;
            case 'services':
                this.updateServices(state.services);
                break;
        }
    }

    updateLoadingState(isLoading) {
        if (this.elements.loadingOverlay) {
            this.elements.loadingOverlay.classList.toggle('show', isLoading);
        }
    }

    updateErrorState(error) {
        if (this.elements.errorMessage) {
            if (error) {
                this.elements.errorMessage.textContent = error;
                this.elements.errorMessage.classList.add('show');
            } else {
                this.elements.errorMessage.classList.remove('show');
            }
        }
    }

    updateUserInfo(user) {
        if (!user) return;

        // Update all user name elements
        if (this.elements.userName) {
            this.elements.userName.textContent = user.firstName || user.username;
        }
        if (this.elements.dashboardUserName) {
            this.elements.dashboardUserName.textContent = user.firstName || user.username;
        }
        if (this.elements.sidebarUserName) {
            this.elements.sidebarUserName.textContent = user.username;
        }

        // Update user details in sidebar
        if (this.elements.userFullName) {
            this.elements.userFullName.textContent = user.fullName;
        }
        if (this.elements.userCity) {
            this.elements.userCity.textContent = user.city;
        }
        if (this.elements.userAddress) {
            this.elements.userAddress.textContent = user.address;
        }
        if (this.elements.userCountry) {
            this.elements.userCountry.textContent = user.country;
        }
    }

    updateStats(stats) {
        if (this.elements.servicesCount) {
            this.animateNumber(this.elements.servicesCount, stats.services);
        }
        if (this.elements.domainsCount) {
            this.animateNumber(this.elements.domainsCount, stats.domains);
        }
        if (this.elements.ticketsCount) {
            this.animateNumber(this.elements.ticketsCount, stats.tickets);
        }
        if (this.elements.invoicesCount) {
            this.animateNumber(this.elements.invoicesCount, stats.invoices);
        }
    }

    updateServices(services) {
        this.renderContentSections(services);
    }

    animateNumber(element, targetValue) {
        if (typeof gsap !== 'undefined') {
            gsap.fromTo(element,
                { textContent: 0 },
                {
                    textContent: targetValue,
                    duration: 1.5,
                    ease: 'power2.out',
                    snap: { textContent: 1 }
                }
            );
        } else {
            element.textContent = targetValue;
        }
    }

    renderContentSections(services) {
        if (!this.elements.contentSections) return;

        const hasServices = services && services.length > 0;

        this.elements.contentSections.innerHTML = `
            <!-- Your Active Products/Services -->
            <div class="content-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-cog"></i>
                        Your Active Products/Services
                    </div>
                    <button class="my-services-btn" onclick="navigateToServices()">My Services</button>
                </div>
                <div class="section-content">
                    ${hasServices ? this.renderServicesList(services) : this.renderNoServicesMessage()}
                    <div class="view-more">
                        <a href="#" onclick="navigateToServices()">View More...</a>
                    </div>
                </div>
            </div>

            <!-- Two Column Layout -->
            <div class="two-column-layout">
                <!-- Recent Support Tickets -->
                <div class="content-section half-width">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-ticket-alt"></i>
                            Recent Support Tickets
                        </div>
                        <button class="open-ticket-btn" onclick="openNewTicketModal()">+ Open New Ticket</button>
                    </div>
                    <div class="section-content">
                        <div class="no-tickets-message">
                            No Recent Tickets Found. If you need any help, please
                            <a href="#" class="ticket-link" onclick="openNewTicketModal()">open a ticket</a>.
                        </div>
                    </div>
                </div>

                <!-- Register a New Domain -->
                <div class="content-section half-width">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-globe"></i>
                            Register a New Domain
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="domain-actions">
                            <button class="register-btn" onclick="navigateToDomainRegistration()">Register</button>
                            <button class="transfer-btn" onclick="navigateToDomainTransfer()">Transfer</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent News -->
            <div class="content-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-newspaper"></i>
                        Recent News
                    </div>
                    <button class="view-all-btn" onclick="navigateToNews()">View All</button>
                </div>
                <div class="section-content">
                    <div class="news-item">
                        <div class="news-title">Thank you for choosing WHMCS!</div>
                        <div class="news-date">Saturday, May 3rd, 2025</div>
                    </div>
                </div>
            </div>
        `;
    }

    renderServicesList(services) {
        return services.map(service => `
            <div class="service-item">
                <div class="service-info">
                    <div class="service-name">${service.name}</div>
                    <div class="service-domain">${service.domain || 'N/A'}</div>
                    <div class="service-status ${service.status}">
                        <i class="fas fa-circle"></i>
                        ${service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                    </div>
                </div>
                <div class="service-actions">
                    <button class="btn-small" onclick="manageService('${service.id}')">
                        <i class="fas fa-cog"></i>
                        Manage
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderNoServicesMessage() {
        return `
            <div class="no-products-message">
                It appears you do not have any products/services with us yet.
                <a href="#" class="order-link" onclick="navigateToServices()">Place an order to get started.</a>
            </div>
        `;
    }
}

// Dashboard Controller
class DashboardController {
    constructor() {
        this.uiManager = new UIManager();
        this.init();
    }

    async init() {
        console.log('🚀 Initializing Improved Client Dashboard...');

        // Check authentication
        if (!this.checkAuthentication()) {
            this.redirectToLogin();
            return;
        }

        try {
            await this.loadDashboardData();
            this.initAnimations();
            console.log('✅ Dashboard initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing dashboard:', error);
            dashboardState.setError('Failed to load dashboard data. Please refresh the page.');
        }
    }

    checkAuthentication() {
        const sessionData = this.getSessionData();
        return !!sessionData;
    }

    getSessionData() {
        let sessionData = localStorage.getItem('widdx_session');
        if (!sessionData) {
            sessionData = sessionStorage.getItem('widdx_session');
        }

        if (sessionData) {
            try {
                return JSON.parse(sessionData);
            } catch (error) {
                console.warn('Invalid session data found, clearing storage');
                sessionStorage.removeItem('widdx_session');
                return null;
            }
        }

        return null;
    }

    redirectToLogin() {
        console.log('🔐 Redirecting to login page...');
        window.location.href = 'client-area.html';
    }

    async loadDashboardData() {
        dashboardState.setLoading(true);
        dashboardState.setError(null);

        try {
            // Load user data
            const userData = await dashboardAPI.fetchUserData();
            dashboardState.setUser(userData);

            // Load dashboard stats
            const statsData = await dashboardAPI.fetchDashboardStats();
            dashboardState.setStats(statsData);

            // Load services
            const servicesData = await dashboardAPI.fetchServices();
            dashboardState.setServices(servicesData);

        } catch (error) {
            dashboardState.setError('Failed to load dashboard data');
            throw error;
        } finally {
            dashboardState.setLoading(false);
        }
    }

    initAnimations() {
        if (typeof gsap === 'undefined') return;

        // Animate elements with improved timing
        const tl = gsap.timeline();

        tl.fromTo('.welcome-header h1',
            { opacity: 0, y: 30 },
            { opacity: 1, y: 0, duration: 0.6, ease: 'power2.out' }
        )
        .fromTo('.breadcrumb',
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.6, ease: 'power2.out' }, '-=0.4'
        )
        .fromTo('.sidebar-section',
            { opacity: 0, x: -50 },
            { opacity: 1, x: 0, duration: 0.8, ease: 'power3.out', stagger: 0.1 }, '-=0.3'
        )
        .fromTo('.stat-card',
            { opacity: 0, y: 50 },
            { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out', stagger: 0.1 }, '-=0.5'
        )
        .fromTo('.search-bar',
            { opacity: 0, scale: 0.9 },
            { opacity: 1, scale: 1, duration: 0.6, ease: 'power2.out' }, '-=0.3'
        )
        .fromTo('.content-section',
            { opacity: 0, y: 30 },
            { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out', stagger: 0.1 }, '-=0.2'
        );
    }
}

// Navigation and Action Handlers
const NavigationManager = {
    navigateToServices() {
        console.log('🔧 Navigating to services...');
        this.showModal('Services Management', 'Here you would see all your active services, hosting packages, and service management options.');
    },

    navigateToDomains() {
        console.log('🌐 Navigating to domains...');
        this.showModal('Domain Management', 'Here you would see all your registered domains, DNS management, and domain renewal options.');
    },

    navigateToTickets() {
        console.log('🎫 Navigating to tickets...');
        this.showModal('Support Tickets', 'Here you would see all your support tickets, create new tickets, and track ticket status.');
    },

    navigateToInvoices() {
        console.log('💰 Navigating to invoices...');
        this.showModal('Billing & Invoices', 'Here you would see all your invoices, payment history, and billing information.');
    },

    navigateToDomainRegistration() {
        console.log('🌐 Opening domain registration...');
        this.showModal('Register New Domain', 'Domain registration form would be displayed here with search functionality and pricing.');
    },

    navigateToDomainTransfer() {
        console.log('🔄 Opening domain transfer...');
        this.showModal('Transfer Domain', 'Domain transfer form would be displayed here with transfer requirements and process.');
    },

    navigateToNews() {
        console.log('📰 Navigating to news...');
        this.showModal('Company News', 'Here you would see all company announcements, service updates, and important notices.');
    },

    showModal(title, content) {
        const modal = this.createModal(title, content);
        document.body.appendChild(modal);

        if (typeof gsap !== 'undefined') {
            gsap.fromTo(modal,
                { opacity: 0, scale: 0.8 },
                { opacity: 1, scale: 1, duration: 0.3, ease: 'power2.out' }
            );
        }
    },

    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(0, 0, 0, 0.8); display: flex; align-items: center;
            justify-content: center; z-index: 10000; backdrop-filter: blur(5px);
        `;

        modal.innerHTML = `
            <div class="modal-content" style="
                background: var(--card-background); border: 1px solid var(--border-color);
                border-radius: var(--border-radius); padding: 30px; max-width: 500px;
                width: 90%; backdrop-filter: blur(10px);
            ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="color: var(--text-primary); margin: 0;">${title}</h3>
                    <button onclick="this.closest('.modal-overlay').remove()" style="
                        background: none; border: none; color: var(--text-muted);
                        font-size: 1.5rem; cursor: pointer;
                    ">&times;</button>
                </div>
                <div style="color: var(--text-secondary);">${content}</div>
            </div>
        `;

        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });

        return modal;
    }
};

// Action Handlers
function navigateToServices() { NavigationManager.navigateToServices(); }
function navigateToDomains() { NavigationManager.navigateToDomains(); }
function navigateToTickets() { NavigationManager.navigateToTickets(); }
function navigateToInvoices() { NavigationManager.navigateToInvoices(); }
function navigateToDomainRegistration() { NavigationManager.navigateToDomainRegistration(); }
function navigateToDomainTransfer() { NavigationManager.navigateToDomainTransfer(); }
function navigateToNews() { NavigationManager.navigateToNews(); }

function showProfileSettings() {
    NavigationManager.showModal('Profile Settings', 'Here you would be able to update your personal information, contact details, and account preferences.');
}

function showAccountSettings() {
    NavigationManager.showModal('Account Settings', 'Here you would be able to manage your account security, password, two-factor authentication, and notification preferences.');
}

function showNewContactModal() {
    NavigationManager.showModal('Add New Contact', 'Here you would be able to add additional contacts to your account for billing, technical, or administrative purposes.');
}

function openNewTicketModal() {
    NavigationManager.showModal('Create Support Ticket', 'Here you would be able to create a new support ticket with priority selection, department routing, and file attachments.');
}

function manageService(serviceId) {
    NavigationManager.showModal('Manage Service', `Here you would be able to manage service ${serviceId} with options for upgrades, configuration, backups, and monitoring.`);
}

function handleSearchKeypress(event) {
    if (event.key === 'Enter') {
        const query = event.target.value.trim();
        if (query) {
            console.log('🔍 Searching knowledgebase for:', query);
            NavigationManager.showModal('Search Results', `Search results for "${query}" would be displayed here with relevant articles and solutions.`);
        }
    }
}

function handleLogout() {
    console.log('🔐 Logging out...');

    // Clear session data
    localStorage.removeItem('widdx_session');
    sessionStorage.removeItem('widdx_session');

    // Show logout animation
    if (typeof gsap !== 'undefined') {
        const logoutMessage = document.createElement('div');
        logoutMessage.textContent = 'Logging out...';
        logoutMessage.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: var(--card-background); color: var(--text-primary);
            padding: 20px 40px; border-radius: var(--border-radius);
            border: 1px solid var(--border-color); z-index: 10000;
            backdrop-filter: blur(10px);
        `;

        document.body.appendChild(logoutMessage);

        gsap.fromTo(logoutMessage,
            { opacity: 0, scale: 0.8 },
            { opacity: 1, scale: 1, duration: 0.3, ease: 'power2.out' }
        );

        setTimeout(() => {
            window.location.href = 'client-area.html';
        }, 1000);
    } else {
        setTimeout(() => {
            window.location.href = 'client-area.html';
        }, 500);
    }
}

// Initialize dashboard when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new DashboardController();
    });
} else {
    new DashboardController();
}
