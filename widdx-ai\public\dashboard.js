document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    let usageChart = null;
    let sourceChart = null;

    // WebSocket connection
    let ws = new WebSocket('ws://' + window.location.host + '/widdx-ai/ws/analytics');

    // Chart configuration
    const usageChartConfig = {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'استخدام النظام (%)',
                data: [],
                borderColor: '#4a6cf7',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    };

    const sourceChartConfig = {
        type: 'doughnut',
        data: {
            labels: ['DEEPSEEK', 'GEMINI', 'التخزين المؤقت'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: ['#4a6cf7', '#28a745', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    };

    // Initialize charts
    const usageCtx = document.getElementById('usageChart').getContext('2d');
    usageChart = new Chart(usageCtx, usageChartConfig);

    const sourceCtx = document.getElementById('sourceChart').getContext('2d');
    sourceChart = new Chart(sourceCtx, sourceChartConfig);

    // WebSocket event handlers
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        // Update metrics
        document.getElementById('memoryUsage').textContent = 
            (data.memory_usage / 1024 / 1024).toFixed(2) + ' MB';
        document.getElementById('totalQuestions').textContent = data.total_questions;
        document.getElementById('cachedResponses').textContent = data.cached_responses;

        // Update system status
        const statusCircle = document.querySelector('.status-circle');
        statusCircle.className = 'status-circle ' + 
            (data.status === 'healthy' ? 'status-ok' : 
            data.status === 'warning' ? 'status-warning' : 'status-error');

        // Update charts
        if (data.usage) {
            updateUsageChart(data.usage);
        }
        if (data.sources) {
            updateSourceChart(data.sources);
        }

        // Update activity table
        updateActivityTable(data.activity);
    };

    ws.onclose = function() {
        console.log('WebSocket connection closed');
        setTimeout(() => {
            ws = new WebSocket('ws://' + window.location.host + '/widdx-ai/ws/analytics');
        }, 5000);
    };

    // Helper functions
    function updateUsageChart(usage) {
        usageChart.data.labels = usage.labels;
        usageChart.data.datasets[0].data = usage.values;
        usageChart.update();
    }

    function updateSourceChart(sources) {
        sourceChart.data.datasets[0].data = sources.values;
        sourceChart.update();
    }

    function updateActivityTable(activity) {
        const tbody = document.getElementById('activityTable');
        tbody.innerHTML = '';
        
        activity.forEach(item => {
            const row = tbody.insertRow();
            const timeCell = row.insertCell(0);
            const typeCell = row.insertCell(1);
            const detailsCell = row.insertCell(2);
            
            timeCell.textContent = new Date(item.timestamp).toLocaleString();
            typeCell.textContent = item.type;
            detailsCell.textContent = item.details;
        });
    }

    // Initial data fetch
    fetch('/widdx-ai/api/analytics.php')
        .then(response => response.json())
        .then(data => {
            updateUsageChart(data.usage);
            updateSourceChart(data.sources);
            updateActivityTable(data.activity);
        });
});
