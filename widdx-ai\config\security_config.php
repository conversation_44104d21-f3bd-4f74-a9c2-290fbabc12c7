<?php
// Security configuration

// Rate limiting
$rate_limit = [
    'enabled' => true,
    'window' => 60, // seconds
    'max_requests' => 100,
    'storage' => 'redis', // or 'file'
    'key_prefix' => 'rate_limit_'
];

// CSRF protection
$csrf = [
    'enabled' => true,
    'token_name' => '_csrf_token',
    'header_name' => 'X-CSRF-Token',
    'cookie_name' => '_csrf_cookie',
    'cookie_secure' => true,
    'cookie_httponly' => true,
    'cookie_samesite' => 'Strict',
    'token_ttl' => 3600,
    'regenerate_on_login' => true
];

// Authentication
$auth = [
    'enabled' => true,
    'method' => 'session', // or 'jwt'
    'session_name' => 'widdx_session',
    'session_lifetime' => 3600,
    'session_cookie_secure' => true,
    'session_cookie_httponly' => true,
    'session_cookie_samesite' => 'Strict',
    'jwt_secret' => 'your-secret-key-here',
    'jwt_ttl' => 3600,
    'jwt_refresh_ttl' => 86400,
    'jwt_algorithm' => 'HS256'
];

// Input validation
$input_validation = [
    'enabled' => true,
    'max_length' => 1000,
    'allowed_chars' => 'a-zA-Z0-9\s\p{Arabic}\.\,\!\?\(\)\[\]\{\}\-\_\:\;\'\"\\\/@#$%^&*+=<>|~',
    'regex' => '/^[a-zA-Z0-9\s\p{Arabic}\.\,\!\?\(\)\[\]\{\}\-\_\:\;\'\"\\\/@#$%^&*+=<>|~]*$/u'
];

// IP restrictions
$ip_restrictions = [
    'enabled' => true,
    'whitelist' => [
        '127.0.0.1',
        '::1'
    ],
    'blacklist' => []
];

// Content security
$content_security = [
    'enabled' => true,
    'allowed_types' => [
        'text/plain',
        'application/json',
        'application/x-www-form-urlencoded'
    ],
    'max_size' => 1048576, // 1MB
    'encoding' => 'UTF-8'
];

// Headers
$headers = [
    'enabled' => true,
    'csp' => [
        'default-src' => [
            'self',
            'https://widdx.ai'
        ],
        'script-src' => [
            'self',
            'https://cdn.jsdelivr.net'
        ],
        'style-src' => [
            'self',
            'https://cdn.jsdelivr.net'
        ],
        'img-src' => [
            'self',
            'data:'
        ]
    ],
    'hsts' => [
        'max_age' => 31536000, // 1 year
        'include_subdomains' => true,
        'preload' => true
    ],
    'cors' => [
        'enabled' => true,
        'allowed_origins' => [
            'https://widdx.ai'
        ],
        'allowed_methods' => [
            'GET',
            'POST',
            'PUT',
            'DELETE',
            'OPTIONS'
        ],
        'allowed_headers' => [
            'Content-Type',
            'Authorization',
            'X-Requested-With'
        ],
        'allow_credentials' => true,
        'max_age' => 3600
    ]
];

// Error handling
$error_handling = [
    'enabled' => true,
    'display_errors' => false,
    'log_errors' => true,
    'log_file' => __DIR__ . '/../logs/security_errors.log',
    'error_level' => E_ALL
];

// Logging
$logging = [
    'enabled' => true,
    'level' => 'INFO',
    'file' => __DIR__ . '/../logs/security.log',
    'format' => '{timestamp} [{level}] {message} {context}',
    'max_size' => 10485760, // 10MB
    'rotate' => true,
    'rotate_count' => 10
];

// Return configuration as array
return [
    'rate_limit' => $rate_limit,
    'csrf' => $csrf,
    'auth' => $auth,
    'input_validation' => $input_validation,
    'ip_restrictions' => $ip_restrictions,
    'content_security' => $content_security,
    'headers' => $headers,
    'error_handling' => $error_handling,
    'logging' => $logging,
    'error_messages' => [
        'generic' => 'حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
        'timeout' => 'تمت معالجة الطلب لفترة طويلة. يرجى المحاولة مرة أخرى.',
        'rate_limit' => 'تم تجاوز حد معدل الطلبات. يرجى الانتظار قليلاً.',
        'invalid_input' => 'المدخلات غير صالحة. يرجى التأكد من صحة المدخلات.',
        'authentication' => 'فشل التحقق من الهوية. يرجى التأكد من بيانات تسجيل الدخول.',
        'permission' => 'ليس لديك صلاحيات كافية للوصول إلى هذه الميزة.',
        'not_found' => 'لم يتم العثور على المورد المطلوب.',
        'server_error' => 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.'
    ]
];

define('SECURITY_ENABLED', true);
define('SECURITY_ENFORCE_SSL', true);
define('SECURITY_SSL_REDIRECT', true);
define('SECURITY_SSL_PORT', 443);

define('SECURITY_XSS_PROTECTION', true);
define('SECURITY_XSS_PROTECTION_MODE', 'block');
define('SECURITY_CONTENT_TYPE_NOSNIFF', true);
define('SECURITY_HSTS_ENABLED', true);
define('SECURITY_HSTS_MAX_AGE', 31536000); // 1 year
define('SECURITY_HSTS_INCLUDE_SUBDOMAINS', true);
define('SECURITY_HSTS_PRELOAD', true);

define('SECURITY_CSP_ENABLED', true);
define('SECURITY_CSP_DEFAULT_SRC', "'self'");
define('SECURITY_CSP_SCRIPT_SRC', "'self' 'unsafe-inline' 'unsafe-eval'");
define('SECURITY_CSP_STYLE_SRC', "'self' 'unsafe-inline'");
define('SECURITY_CSP_IMG_SRC', "'self' data:");
define('SECURITY_CSP_CONNECT_SRC', "'self'");
define('SECURITY_CSP_FONT_SRC', "'self'");
define('SECURITY_CSP_OBJECT_SRC', "'none'");
define('SECURITY_CSP_MEDIA_SRC', "'self'");
define('SECURITY_CSP_FRAME_SRC', "'self'");
define('SECURITY_CSP_FORM_ACTION', "'self'");
define('SECURITY_CSP_BASE_URI', "'self'");
define('SECURITY_CSP_SANDBOX', true);
define('SECURITY_CSP_SANDBOX_FLAGS', "allow-forms allow-scripts allow-same-origin");

define('SECURITY_REFERRER_POLICY', 'strict-origin-when-cross-origin');
define('SECURITY_PERMISSIONS_POLICY', 'camera=() microphone=() geolocation=()');
define('SECURITY_CORS_ENABLED', true);
define('SECURITY_CORS_ALLOW_ORIGIN', ['https://widdx.ai']);
define('SECURITY_CORS_ALLOW_METHODS', ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']);
define('SECURITY_CORS_ALLOW_HEADERS', ['Content-Type', 'Authorization', 'X-Requested-With']);
define('SECURITY_CORS_ALLOW_CREDENTIALS', true);
define('SECURITY_CORS_EXPOSE_HEADERS', ['Content-Length', 'Content-Type']);
define('SECURITY_CORS_MAX_AGE', 3600);

define('SECURITY_RATE_LIMIT_ENABLED', true);
define('SECURITY_RATE_LIMIT_WINDOW', 60); // seconds
define('SECURITY_RATE_LIMIT_MAX', 100);
define('SECURITY_RATE_LIMIT_BURST', 200);
define('SECURITY_RATE_LIMIT_RECOVER', 300);
define('SECURITY_RATE_LIMIT_STORAGE', 'redis');
define('SECURITY_RATE_LIMIT_KEY_PREFIX', 'rate_limit_');

define('SECURITY_CSRF_ENABLED', true);
define('SECURITY_CSRF_TOKEN_NAME', '_csrf_token');
define('SECURITY_CSRF_TOKEN_LENGTH', 32);
define('SECURITY_CSRF_TOKEN_TTL', 3600);
define('SECURITY_CSRF_TOKEN_STORAGE', 'session');
define('SECURITY_CSRF_TOKEN_HEADER', 'X-CSRF-Token');
define('SECURITY_CSRF_TOKEN_FIELD', '_csrf_token');

define('SECURITY_AUTH_ENABLED', true);
define('SECURITY_AUTH_METHOD', 'session'); // session, jwt, cookie
define('SECURITY_AUTH_TTL', 3600);
define('SECURITY_AUTH_REFRESH_TTL', 86400);
define('SECURITY_AUTH_TOKEN_NAME', 'auth_token');
define('SECURITY_AUTH_COOKIE_NAME', 'widdx_auth');
define('SECURITY_AUTH_COOKIE_SECURE', true);
define('SECURITY_AUTH_COOKIE_HTTPONLY', true);
define('SECURITY_AUTH_COOKIE_SAMESITE', 'Strict');
define('SECURITY_AUTH_PASSWORD_MIN_LENGTH', 8);
define('SECURITY_AUTH_PASSWORD_MAX_LENGTH', 128);
define('SECURITY_AUTH_PASSWORD_REQUIRE_UPPERCASE', true);
define('SECURITY_AUTH_PASSWORD_REQUIRE_LOWERCASE', true);
define('SECURITY_AUTH_PASSWORD_REQUIRE_NUMBER', true);
define('SECURITY_AUTH_PASSWORD_REQUIRE_SPECIAL', true);
define('SECURITY_AUTH_PASSWORD_SPECIAL_CHARS', '!@#$%^&*()');

define('SECURITY_INPUT_VALIDATION_ENABLED', true);
define('SECURITY_INPUT_MAX_LENGTH', 1048576);
define('SECURITY_INPUT_SANITIZE', true);
define('SECURITY_INPUT_ALLOW_TAGS', false);
define('SECURITY_INPUT_ALLOWED_TAGS', ['<p>', '<br>', '<strong>', '<em>']);
define('SECURITY_INPUT_ALLOWED_ATTRIBUTES', ['class', 'id', 'style']);
define('SECURITY_INPUT_ALLOWED_PROTOCOLS', ['http', 'https']);

define('SECURITY_FILE_UPLOAD_ENABLED', true);
define('SECURITY_FILE_UPLOAD_MAX_SIZE', 10485760);
define('SECURITY_FILE_UPLOAD_ALLOWED_TYPES', ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']);
define('SECURITY_FILE_UPLOAD_TEMP_DIR', __DIR__ . '/../uploads/tmp');
define('SECURITY_FILE_UPLOAD_PERMANENT_DIR', __DIR__ . '/../uploads');
define('SECURITY_FILE_UPLOAD_PERMISSIONS', 0644);
define('SECURITY_FILE_UPLOAD_DIR_PERMISSIONS', 0755);
define('SECURITY_FILE_UPLOAD_SCAN_VIRUS', true);
define('SECURITY_FILE_UPLOAD_SCAN_MALWARE', true);

define('SECURITY_API_KEY_ENABLED', true);
define('SECURITY_API_KEY_HEADER', 'X-API-Key');
define('SECURITY_API_KEY_QUERY_PARAM', 'api_key');
define('SECURITY_API_KEY_STORAGE', 'database');
define('SECURITY_API_KEY_TTL', 86400);
define('SECURITY_API_KEY_ROTATE', true);
define('SECURITY_API_KEY_ROTATE_INTERVAL', 604800);

define('SECURITY_IP_WHITELIST_ENABLED', true);
define('SECURITY_IP_WHITELIST', [
    '127.0.0.1',
    '::1'
]);
define('SECURITY_IP_BLACKLIST_ENABLED', true);
define('SECURITY_IP_BLACKLIST', []);
define('SECURITY_IP_LOGGING_ENABLED', true);
define('SECURITY_IP_LOGGING_FILE', __DIR__ . '/../logs/ip.log');

define('SECURITY_REQUEST_VALIDATION_ENABLED', true);
define('SECURITY_REQUEST_MAX_HEADERS', 100);
define('SECURITY_REQUEST_MAX_BODY_SIZE', 10485760);
define('SECURITY_REQUEST_TIMEOUT', 30);
define('SECURITY_REQUEST_LOGGING_ENABLED', true);
define('SECURITY_REQUEST_LOGGING_FILE', __DIR__ . '/../logs/requests.log');

define('SECURITY_SESSION_ENABLED', true);
define('SECURITY_SESSION_NAME', 'widdx_session');
define('SECURITY_SESSION_LIFETIME', 3600);
define('SECURITY_SESSION_COOKIE_SECURE', true);
define('SECURITY_SESSION_COOKIE_HTTPONLY', true);
define('SECURITY_SESSION_COOKIE_SAMESITE', 'Strict');
define('SECURITY_SESSION_REGENERATE', true);
define('SECURITY_SESSION_REGENERATE_INTERVAL', 300);

define('SECURITY_LOGGING_ENABLED', true);
define('SECURITY_LOGGING_FILE', __DIR__ . '/../logs/security.log');
define('SECURITY_LOGGING_LEVEL', 'INFO');
define('SECURITY_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');

define('SECURITY_AUDIT_ENABLED', true);
define('SECURITY_AUDIT_LOGGING_ENABLED', true);
define('SECURITY_AUDIT_LOGGING_FILE', __DIR__ . '/../logs/audit.log');
define('SECURITY_AUDIT_LOGGING_LEVEL', 'INFO');
define('SECURITY_AUDIT_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');

define('SECURITY_RATE_LIMIT_ENABLED', true);
define('SECURITY_RATE_LIMIT_WINDOW', 60);
define('SECURITY_RATE_LIMIT_MAX', 100);
define('SECURITY_RATE_LIMIT_BURST', 200);
define('SECURITY_RATE_LIMIT_RECOVER', 300);
define('SECURITY_RATE_LIMIT_STORAGE', 'redis');
define('SECURITY_RATE_LIMIT_KEY_PREFIX', 'rate_limit_');

define('SECURITY_INPUT_VALIDATION_ENABLED', true);
define('SECURITY_INPUT_MAX_LENGTH', 1048576);
define('SECURITY_INPUT_SANITIZE', true);
define('SECURITY_INPUT_ALLOW_TAGS', false);
define('SECURITY_INPUT_ALLOWED_TAGS', ['<p>', '<br>', '<strong>', '<em>']);
define('SECURITY_INPUT_ALLOWED_ATTRIBUTES', ['class', 'id', 'style']);
define('SECURITY_INPUT_ALLOWED_PROTOCOLS', ['http', 'https']);

define('SECURITY_FILE_UPLOAD_ENABLED', true);
define('SECURITY_FILE_UPLOAD_MAX_SIZE', 10485760);
define('SECURITY_FILE_UPLOAD_ALLOWED_TYPES', ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']);
define('SECURITY_FILE_UPLOAD_TEMP_DIR', __DIR__ . '/../uploads/tmp');
define('SECURITY_FILE_UPLOAD_PERMANENT_DIR', __DIR__ . '/../uploads');
define('SECURITY_FILE_UPLOAD_PERMISSIONS', 0644);
define('SECURITY_FILE_UPLOAD_DIR_PERMISSIONS', 0755);
define('SECURITY_FILE_UPLOAD_SCAN_VIRUS', true);
define('SECURITY_FILE_UPLOAD_SCAN_MALWARE', true);

define('SECURITY_API_KEY_ENABLED', true);
define('SECURITY_API_KEY_HEADER', 'X-API-Key');
define('SECURITY_API_KEY_QUERY_PARAM', 'api_key');
define('SECURITY_API_KEY_STORAGE', 'database');
define('SECURITY_API_KEY_TTL', 86400);
define('SECURITY_API_KEY_ROTATE', true);
define('SECURITY_API_KEY_ROTATE_INTERVAL', 604800);

define('SECURITY_IP_WHITELIST_ENABLED', true);
define('SECURITY_IP_WHITELIST', [
    '127.0.0.1',
    '::1'
]);
define('SECURITY_IP_BLACKLIST_ENABLED', true);
define('SECURITY_IP_BLACKLIST', []);
define('SECURITY_IP_LOGGING_ENABLED', true);
define('SECURITY_IP_LOGGING_FILE', __DIR__ . '/../logs/ip.log');

define('SECURITY_REQUEST_VALIDATION_ENABLED', true);
define('SECURITY_REQUEST_MAX_HEADERS', 100);
define('SECURITY_REQUEST_MAX_BODY_SIZE', 10485760);
define('SECURITY_REQUEST_TIMEOUT', 30);
define('SECURITY_REQUEST_LOGGING_ENABLED', true);
define('SECURITY_REQUEST_LOGGING_FILE', __DIR__ . '/../logs/requests.log');

define('SECURITY_SESSION_ENABLED', true);
define('SECURITY_SESSION_NAME', 'widdx_session');
define('SECURITY_SESSION_LIFETIME', 3600);
define('SECURITY_SESSION_COOKIE_SECURE', true);
define('SECURITY_SESSION_COOKIE_HTTPONLY', true);
define('SECURITY_SESSION_COOKIE_SAMESITE', 'Strict');
define('SECURITY_SESSION_REGENERATE', true);
define('SECURITY_SESSION_REGENERATE_INTERVAL', 300);

define('SECURITY_LOGGING_ENABLED', true);
define('SECURITY_LOGGING_FILE', __DIR__ . '/../logs/security.log');
define('SECURITY_LOGGING_LEVEL', 'INFO');
define('SECURITY_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');

define('SECURITY_AUDIT_ENABLED', true);
define('SECURITY_AUDIT_LOGGING_ENABLED', true);
define('SECURITY_AUDIT_LOGGING_FILE', __DIR__ . '/../logs/audit.log');
define('SECURITY_AUDIT_LOGGING_LEVEL', 'INFO');
define('SECURITY_AUDIT_LOGGING_FORMAT', '{timestamp} [{level}] {message} {context}');
