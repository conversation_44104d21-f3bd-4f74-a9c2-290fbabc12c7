<?php

/**
 * 3D Secure Status Checking Endpoint for WIDDX
 * 
 * Provides real-time status updates for 3D Secure authentication
 * 
 * @package WIDDX
 * @subpackage PaymentGateway
 * @version 1.0.0
 */

// Security headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: SAMEORIGIN');
header('X-XSS-Protection: 1; mode=block');

// CORS headers for AJAX requests
header('Access-Control-Allow-Origin: ' . ($_SERVER['HTTP_ORIGIN'] ?? '*'));
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Initialize WHMCS environment
$init_path = __DIR__ . '/../../init.php';
if (file_exists($init_path)) {
    define('CLIENTAREA', true);
    require_once $init_path;
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'WHMCS environment not available']);
    exit;
}

require_once __DIR__ . '/Enhanced3DSecure.php';
require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/EnhancedTransactionManager.php';

class ThreeDSStatusChecker {
    
    private $enhanced3DS;
    private $logger;
    private $transactionManager;
    private $gatewayParams;
    
    public function __construct() {
        // Load gateway parameters
        $this->gatewayParams = getGatewayVariables('lahza');
        
        $this->logger = new LahzaLogger();
        $this->transactionManager = new EnhancedTransactionManager($this->gatewayParams, $this->logger);
        $this->enhanced3DS = new Enhanced3DSecure($this->gatewayParams, $this->logger, $this->transactionManager);
    }
    
    /**
     * Handle status check request
     */
    public function handleRequest() {
        try {
            // Validate request method
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                $this->sendError('Method not allowed', 405);
                return;
            }
            
            // Get transaction ID
            $transactionId = $_GET['transaction_id'] ?? '';
            if (empty($transactionId)) {
                $this->sendError('Transaction ID required', 400);
                return;
            }
            
            // Validate transaction ID format
            if (!$this->isValidTransactionId($transactionId)) {
                $this->sendError('Invalid transaction ID format', 400);
                return;
            }
            
            // Rate limiting check
            if (!$this->checkRateLimit()) {
                $this->sendError('Rate limit exceeded', 429);
                return;
            }
            
            // Get authentication status
            $status = $this->getAuthenticationStatus($transactionId);
            
            if ($status) {
                $this->sendResponse($status);
            } else {
                $this->sendError('Transaction not found', 404);
            }
            
        } catch (Exception $e) {
            $this->logger->error('3DS status check failed', [
                'transaction_id' => $_GET['transaction_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], '3ds');
            
            $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * Get authentication status
     */
    private function getAuthenticationStatus($transactionId) {
        $this->logger->info('Checking 3DS authentication status', [
            'transaction_id' => $transactionId
        ], '3ds');
        
        try {
            // Get transaction from database
            $transaction = $this->transactionManager->getTransaction($transactionId);
            if (!$transaction) {
                return null;
            }
            
            // Get 3DS specific data
            $threeDSData = $this->get3DSData($transactionId);
            
            // Check current status
            $currentStatus = $transaction['status'];
            $threeDSStatus = $transaction['three_ds_status'] ?? '';
            
            $this->logger->info('Current transaction status', [
                'transaction_id' => $transactionId,
                'status' => $currentStatus,
                '3ds_status' => $threeDSStatus
            ], '3ds');
            
            // Determine response based on status
            switch ($threeDSStatus) {
                case EnhancedTransactionManager::TDS_STATUS_AUTHENTICATED:
                    return $this->buildAuthenticatedResponse($transaction, $threeDSData);
                    
                case EnhancedTransactionManager::TDS_STATUS_FAILED:
                    return $this->buildFailedResponse($transaction, $threeDSData);
                    
                case EnhancedTransactionManager::TDS_STATUS_CHALLENGE:
                    return $this->buildChallengeResponse($transaction, $threeDSData);
                    
                case EnhancedTransactionManager::TDS_STATUS_FRICTIONLESS:
                    return $this->buildFrictionlessResponse($transaction, $threeDSData);
                    
                case EnhancedTransactionManager::TDS_STATUS_ATTEMPTED:
                    return $this->buildAttemptedResponse($transaction, $threeDSData);
                    
                case EnhancedTransactionManager::TDS_STATUS_REQUIRED:
                    return $this->buildPendingResponse($transaction, $threeDSData);
                    
                default:
                    // Check if challenge has expired
                    if ($this->isChallengeExpired($threeDSData)) {
                        return $this->buildExpiredResponse($transaction, $threeDSData);
                    }
                    
                    return $this->buildPendingResponse($transaction, $threeDSData);
            }
            
        } catch (Exception $e) {
            $this->logger->error('Failed to get authentication status', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], '3ds');
            
            return null;
        }
    }
    
    /**
     * Build authenticated response
     */
    private function buildAuthenticatedResponse($transaction, $threeDSData) {
        $metadata = json_decode($transaction['metadata'] ?? '{}', true);
        
        return [
            'completed' => true,
            'success' => true,
            'status' => 'authenticated',
            'message' => 'Authentication successful',
            'authenticationData' => [
                'eci' => $metadata['eci'] ?? '',
                'cavv' => $metadata['cavv'] ?? '',
                'xid' => $metadata['xid'] ?? '',
                'authentication_value' => $metadata['authentication_value'] ?? '',
                'ds_transaction_id' => $metadata['ds_transaction_id'] ?? ''
            ],
            'timestamp' => $transaction['updated_at']
        ];
    }
    
    /**
     * Build failed response
     */
    private function buildFailedResponse($transaction, $threeDSData) {
        $metadata = json_decode($transaction['metadata'] ?? '{}', true);
        
        return [
            'completed' => true,
            'success' => false,
            'failed' => true,
            'status' => 'failed',
            'message' => 'Authentication failed',
            'errorCode' => $metadata['error_code'] ?? 'unknown',
            'errorMessage' => $metadata['error_message'] ?? 'Authentication failed',
            'timestamp' => $transaction['updated_at']
        ];
    }
    
    /**
     * Build challenge response
     */
    private function buildChallengeResponse($transaction, $threeDSData) {
        $metadata = json_decode($transaction['metadata'] ?? '{}', true);
        $data = json_decode($threeDSData['data'] ?? '{}', true);
        
        $challengeExpiresAt = $data['challenge_expires_at'] ?? null;
        $timeRemaining = null;
        
        if ($challengeExpiresAt) {
            $expiryTime = strtotime($challengeExpiresAt);
            $timeRemaining = max(0, $expiryTime - time());
        }
        
        return [
            'completed' => false,
            'success' => false,
            'status' => 'challenge_in_progress',
            'message' => 'Challenge authentication in progress',
            'challengeUrl' => $metadata['challenge_url'] ?? '',
            'challengeWindowSize' => $metadata['challenge_window_size'] ?? '05',
            'challengeTimeout' => $metadata['challenge_timeout'] ?? 300,
            'timeRemaining' => $timeRemaining,
            'timestamp' => $transaction['updated_at']
        ];
    }
    
    /**
     * Build frictionless response
     */
    private function buildFrictionlessResponse($transaction, $threeDSData) {
        return [
            'completed' => false,
            'success' => false,
            'status' => 'frictionless_in_progress',
            'message' => 'Frictionless authentication in progress',
            'timestamp' => $transaction['updated_at']
        ];
    }
    
    /**
     * Build attempted response
     */
    private function buildAttemptedResponse($transaction, $threeDSData) {
        $metadata = json_decode($transaction['metadata'] ?? '{}', true);
        
        return [
            'completed' => true,
            'success' => true,
            'status' => 'attempted',
            'message' => 'Authentication attempted',
            'authenticationData' => [
                'eci' => $metadata['eci'] ?? '',
                'cavv' => $metadata['cavv'] ?? '',
                'xid' => $metadata['xid'] ?? ''
            ],
            'timestamp' => $transaction['updated_at']
        ];
    }
    
    /**
     * Build pending response
     */
    private function buildPendingResponse($transaction, $threeDSData) {
        return [
            'completed' => false,
            'success' => false,
            'status' => 'pending',
            'message' => 'Authentication pending',
            'timestamp' => $transaction['updated_at']
        ];
    }
    
    /**
     * Build expired response
     */
    private function buildExpiredResponse($transaction, $threeDSData) {
        return [
            'completed' => true,
            'success' => false,
            'failed' => true,
            'status' => 'expired',
            'message' => 'Authentication challenge expired',
            'timestamp' => $transaction['updated_at']
        ];
    }
    
    /**
     * Get 3DS specific data
     */
    private function get3DSData($transactionId) {
        try {
            return Capsule::table('lahza_3ds_data')
                ->where('transaction_id', $transactionId)
                ->first();
        } catch (Exception $e) {
            $this->logger->error('Failed to get 3DS data', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ], '3ds');
            return null;
        }
    }
    
    /**
     * Check if challenge has expired
     */
    private function isChallengeExpired($threeDSData) {
        if (!$threeDSData) return false;
        
        $data = json_decode($threeDSData['data'] ?? '{}', true);
        $challengeExpiresAt = $data['challenge_expires_at'] ?? null;
        
        if (!$challengeExpiresAt) return false;
        
        return strtotime($challengeExpiresAt) < time();
    }
    
    /**
     * Validate transaction ID format
     */
    private function isValidTransactionId($transactionId) {
        // WIDDX transaction ID format: WIDDX_3DS_[invoice_id]_[timestamp]_[random]
        return preg_match('/^WIDDX_3DS_\d+_\d+_\d+$/', $transactionId);
    }
    
    /**
     * Rate limiting check
     */
    private function checkRateLimit() {
        $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cacheKey = "3ds_status_rate_limit_{$clientIP}";
        $cacheFile = __DIR__ . '/cache/' . md5($cacheKey) . '.cache';
        
        $maxRequests = 30; // per minute
        $timeWindow = 60; // seconds
        
        if (!is_dir(dirname($cacheFile))) {
            mkdir(dirname($cacheFile), 0755, true);
        }
        
        $now = time();
        $requests = [];
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data && isset($data['requests'])) {
                $requests = array_filter($data['requests'], function($timestamp) use ($now, $timeWindow) {
                    return ($now - $timestamp) < $timeWindow;
                });
            }
        }
        
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        $requests[] = $now;
        file_put_contents($cacheFile, json_encode(['requests' => $requests]), LOCK_EX);
        
        return true;
    }
    
    /**
     * Send JSON response
     */
    private function sendResponse($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('c')
        ], JSON_PRETTY_PRINT);
    }
    
    /**
     * Send error response
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('c')
        ], JSON_PRETTY_PRINT);
    }
}

// Handle the request
$statusChecker = new ThreeDSStatusChecker();
$statusChecker->handleRequest();
