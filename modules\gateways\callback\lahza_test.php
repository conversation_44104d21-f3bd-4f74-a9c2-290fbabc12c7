<?php
/**
 * Test endpoint for Lahza callback handler
 */

// Log the request
error_log('Lahza test endpoint accessed at ' . date('Y-m-d H:i:s'));

// Log request details
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI',
    'headers' => getallheaders(),
    'get' => $_GET,
    'post' => $_POST,
    'input' => file_get_contents('php://input')
];

// Write to a test log file
$logFile = __DIR__ . '/test_access.log';
file_put_contents($logFile, json_encode($logData, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);

// Return success response
header('Content-Type: application/json');
http_response_code(200);
echo json_encode([
    'status' => 'success',
    'message' => 'Test endpoint is working',
    'timestamp' => date('c')
]);
