<?php
/**
 * Mock WHMCS Database Class
 * Simulates WHMCS database operations for testing
 */
class WHMCS_DB {
    private static $invoices = [];
    
    public static function getInvoice($invoiceid) {
        return isset(self::$invoices[$invoiceid]) ? self::$invoices[$invoiceid] : null;
    }
    
    public static function updateInvoice($invoiceid, $data) {
        if (!isset(self::$invoices[$invoiceid])) {
            self::$invoices[$invoiceid] = [
                'id' => $invoiceid,
                'status' => 'Unpaid',
                'paymentmethod' => 'lahza',
                'total' => '100.00',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }
        
        foreach ($data as $key => $value) {
            self::$invoices[$invoiceid][$key] = $value;
        }
        
        self::$invoices[$invoiceid]['updated_at'] = date('Y-m-d H:i:s');
        
        // Log the update
        $log = date('[Y-m-d H:i:s] ') . "Invoice #$invoiceid updated. Status: " . 
              (isset($data['status']) ? $data['status'] : 'no change') . "\n";
        file_put_contents(__DIR__ . '/whmcs_simulation.log', $log, FILE_APPEND);
        
        return true;
    }
    
    public static function logTransaction($data) {
        $log = date('[Y-m-d H:i:s] ') . "Transaction Log: " . json_encode($data) . "\n";
        file_put_contents(__DIR__ . '/whmcs_simulation.log', $log, FILE_APPEND);
        return true;
    }
}

/**
 * Mock WHMCS Functions
 */
function logTransaction($module, $data, $status) {
    return WHMCS_DB::logTransaction([
        'module' => $module,
        'data' => $data,
        'status' => $status
    ]);
}

function checkCbInvoiceID($invoiceid, $gateway) {
    return true; // Always return true for testing
}

function checkCbTransID($transid) {
    return true; // Always return true for testing
}

function addInvoicePayment($invoiceid, $transid, $amount, $fee = 0, $gateway = 'lahza') {
    return WHMCS_DB::updateInvoice($invoiceid, [
        'status' => 'Paid',
        'paymentmethod' => $gateway,
        'transid' => $transid,
        'amount' => $amount,
        'paid_at' => date('Y-m-d H:i:s')
    ]);
}
