/*!
 * WIDDX Modern Header & Footer Styles
 * Modern, responsive design with accessibility features
 * Copyright (c) 2025 WIDDX Development Team
 */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Header Variables */
  --widdx-header-height: 80px;
  --widdx-topbar-height: 40px;
  --widdx-header-bg: #ffffff;
  --widdx-header-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  --widdx-topbar-bg: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
  
  /* Footer Variables */
  --widdx-footer-bg: #1a1a1a;
  --widdx-footer-text: #e0e0e0;
  --widdx-footer-link: #b0b0b0;
  --widdx-footer-link-hover: #ffffff;
  --widdx-newsletter-bg: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
  
  /* Animation Variables */
  --widdx-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --widdx-hover-transform: translateY(-2px);
}

/* ===== MODERN HEADER STYLES ===== */

/* Top Bar */
.widdx-topbar {
  background: var(--widdx-topbar-bg);
  color: white;
  padding: 8px 0;
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
  /* إضافة تأثير مرئي واضح للاختبار */
  border-bottom: 3px solid #4a90e2;
  box-shadow: 0 2px 10px rgba(44, 90, 160, 0.3);
}

.widdx-topbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: widdx-shimmer 3s infinite;
}

@keyframes widdx-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.widdx-contact-info .contact-item {
  display: inline-flex;
  align-items: center;
  margin-right: 1.5rem;
  transition: var(--widdx-transition);
}

.widdx-contact-info .contact-item:hover {
  transform: var(--widdx-hover-transform);
}

.widdx-contact-info .contact-item i {
  margin-right: 0.5rem;
  width: 16px;
  text-align: center;
}

.widdx-contact-info .contact-item a {
  color: white;
  text-decoration: none;
  transition: var(--widdx-transition);
}

.widdx-contact-info .contact-item a:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* Header Actions */
.widdx-header-actions {
  display: flex;
  align-items: center;
}

.widdx-lang-btn {
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  transition: var(--widdx-transition);
}

.widdx-lang-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.widdx-social-links .list-inline-item {
  margin-right: 0.5rem;
}

.widdx-social-links .btn {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: var(--widdx-transition);
}

.widdx-social-links .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: var(--widdx-hover-transform);
}

/* Main Navigation */
.widdx-main-nav {
  background: var(--widdx-header-bg);
  box-shadow: var(--widdx-header-shadow);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1030;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  /* إضافة تأثير مرئي واضح للاختبار */
  border-top: 4px solid var(--widdx-primary);
  transition: all 0.3s ease;
}

.widdx-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: var(--widdx-transition);
}

.widdx-brand:hover {
  transform: scale(1.05);
  text-decoration: none;
}

.widdx-logo-img {
  max-height: 50px;
  width: auto;
  transition: var(--widdx-transition);
}

.widdx-brand-text {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--widdx-primary);
  margin: 0;
}

/* Mobile Toggle */
.widdx-mobile-toggle {
  border: none;
  background: transparent;
  padding: 0.5rem;
  position: relative;
  z-index: 1031;
}

.widdx-hamburger {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  justify-content: space-between;
}

.widdx-hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: var(--widdx-primary);
  border-radius: 1px;
  transition: var(--widdx-transition);
}

.widdx-mobile-toggle[aria-expanded="true"] .widdx-hamburger span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.widdx-mobile-toggle[aria-expanded="true"] .widdx-hamburger span:nth-child(2) {
  opacity: 0;
}

.widdx-mobile-toggle[aria-expanded="true"] .widdx-hamburger span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Navigation Menu */
.widdx-primary-nav .nav-link {
  font-weight: 500;
  color: var(--widdx-text-primary);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  transition: var(--widdx-transition);
  position: relative;
}

.widdx-primary-nav .nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--widdx-primary);
  transition: var(--widdx-transition);
  transform: translateX(-50%);
}

.widdx-primary-nav .nav-link:hover::before,
.widdx-primary-nav .nav-link.active::before {
  width: 80%;
}

.widdx-primary-nav .nav-link:hover {
  color: var(--widdx-primary);
  background: rgba(44, 90, 160, 0.05);
}

/* Search Widget */
.widdx-search-widget {
  position: relative;
}

.widdx-search-widget .form-control {
  border-radius: 25px;
  padding-left: 2.5rem;
  border: 2px solid #e9ecef;
  transition: var(--widdx-transition);
}

.widdx-search-widget .form-control:focus {
  border-color: var(--widdx-primary);
  box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

/* Cart Button */
.widdx-cart-btn {
  border-radius: 25px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: var(--widdx-transition);
  position: relative;
}

.widdx-cart-btn:hover {
  transform: var(--widdx-hover-transform);
  box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.widdx-cart-count {
  top: -8px;
  right: -8px;
  min-width: 20px;
  height: 20px;
  font-size: 0.75rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: widdx-pulse 2s infinite;
}

@keyframes widdx-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Mobile Language */
.widdx-mobile-lang {
  background: #f8f9fa;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

/* Mobile Overlay */
.widdx-mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: var(--widdx-transition);
}

.widdx-mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

.widdx-mobile-menu {
  position: absolute;
  top: 0;
  right: 0;
  width: 320px;
  height: 100%;
  background: white;
  transform: translateX(100%);
  transition: var(--widdx-transition);
  overflow-y: auto;
}

.widdx-mobile-overlay.active .widdx-mobile-menu {
  transform: translateX(0);
}

.widdx-mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.widdx-mobile-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--widdx-text-primary);
  cursor: pointer;
}

.widdx-mobile-content {
  padding: 1rem;
}

.widdx-mobile-search .widdx-search-input {
  padding-right: 3rem;
}

.widdx-search-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--widdx-primary);
}

/* Progress Bar */
.widdx-progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(44, 90, 160, 0.1);
  z-index: 1050;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.widdx-progress-bar.active {
  opacity: 1;
}

.widdx-progress-fill {
  height: 100%;
  background: var(--widdx-primary);
  width: 0%;
  transition: width 0.3s ease;
}

/* ===== MODERN FOOTER STYLES ===== */

/* Newsletter Section */
.widdx-newsletter-section {
  background: var(--widdx-newsletter-bg);
  color: white;
  padding: 3rem 0;
  position: relative;
  overflow: hidden;
}

.widdx-newsletter-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.widdx-newsletter-content {
  position: relative;
  z-index: 1;
}

.widdx-newsletter-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
}

.widdx-newsletter-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

.widdx-newsletter-form {
  position: relative;
  z-index: 1;
}

.widdx-newsletter-input {
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 25px 0 0 25px;
  padding: 0.75rem 1rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.widdx-newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.widdx-newsletter-input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: none;
  color: white;
}

.widdx-newsletter-btn {
  border-radius: 0 25px 25px 0;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: var(--widdx-transition);
}

.widdx-newsletter-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  transform: var(--widdx-hover-transform);
}

/* Main Footer */
.widdx-footer-main {
  background: var(--widdx-footer-bg);
  color: var(--widdx-footer-text);
  padding: 4rem 0 2rem;
}

.widdx-footer-widget {
  margin-bottom: 2rem;
}

.widdx-footer-brand {
  margin-bottom: 1.5rem;
}

.widdx-footer-logo {
  max-height: 60px;
  width: auto;
  filter: brightness(0) invert(1);
}

.widdx-footer-brand-text {
  color: white;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.widdx-footer-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--widdx-footer-text);
  margin-bottom: 1.5rem;
}

.widdx-footer-title {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.widdx-footer-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--widdx-primary);
}

.widdx-footer-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.widdx-footer-menu li {
  margin-bottom: 0.75rem;
}

.widdx-footer-menu a {
  color: var(--widdx-footer-link);
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--widdx-transition);
  position: relative;
  padding-left: 1rem;
}

.widdx-footer-menu a::before {
  content: '→';
  position: absolute;
  left: 0;
  opacity: 0;
  transform: translateX(-10px);
  transition: var(--widdx-transition);
}

.widdx-footer-menu a:hover {
  color: var(--widdx-footer-link-hover);
  text-decoration: none;
  padding-left: 1.5rem;
}

.widdx-footer-menu a:hover::before {
  opacity: 1;
  transform: translateX(0);
}

/* Contact Info */
.widdx-contact-info {
  margin-top: 1.5rem;
}

.widdx-contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.widdx-contact-item i {
  width: 20px;
  margin-right: 0.75rem;
  color: var(--widdx-primary);
  flex-shrink: 0;
}

.widdx-contact-item a {
  color: var(--widdx-footer-link);
  text-decoration: none;
  transition: var(--widdx-transition);
}

.widdx-contact-item a:hover {
  color: var(--widdx-footer-link-hover);
}

/* Social Links */
.widdx-social-icons {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.widdx-social-icons .btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: var(--widdx-transition);
}

.widdx-social-icons .btn:hover {
  background: var(--widdx-primary);
  border-color: var(--widdx-primary);
  color: white;
  transform: var(--widdx-hover-transform) scale(1.1);
}

/* Payment Methods */
.widdx-payment-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

.widdx-payment-icon {
  height: 32px;
  width: auto;
  background: white;
  padding: 0.25rem;
  border-radius: 4px;
  transition: var(--widdx-transition);
}

.widdx-payment-icon:hover {
  transform: var(--widdx-hover-transform);
}

/* Trust Badges */
.widdx-trust-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.widdx-trust-badge {
  height: 28px;
  width: auto;
  opacity: 0.8;
  transition: var(--widdx-transition);
}

.widdx-trust-badge:hover {
  opacity: 1;
  transform: var(--widdx-hover-transform);
}

/* Footer Bottom */
.widdx-footer-bottom {
  background: #0f0f0f;
  padding: 1.5rem 0;
  border-top: 1px solid #333;
}

.widdx-copyright {
  font-size: 0.875rem;
  color: var(--widdx-footer-link);
  margin: 0;
}

.widdx-powered-by {
  font-size: 0.8rem;
  color: #888;
  margin-top: 0.25rem;
}

.widdx-footer-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
}

.widdx-lang-currency-btn {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
}

.widdx-back-to-top {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--widdx-transition);
}

.widdx-back-to-top:hover {
  transform: var(--widdx-hover-transform) scale(1.1);
}

/* Quick Contact Widget */
.widdx-quick-contact {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1030;
}

.widdx-quick-contact-toggle {
  width: 60px;
  height: 60px;
  background: var(--widdx-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(44, 90, 160, 0.3);
  transition: var(--widdx-transition);
  animation: widdx-float 3s ease-in-out infinite;
}

@keyframes widdx-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.widdx-quick-contact-toggle:hover {
  background: var(--widdx-secondary);
  transform: scale(1.1);
}

.widdx-quick-contact-panel {
  position: absolute;
  bottom: 70px;
  right: 0;
  width: 280px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: var(--widdx-transition);
}

.widdx-quick-contact.active .widdx-quick-contact-panel {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.widdx-quick-contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.widdx-quick-contact-header h6 {
  margin: 0;
  color: var(--widdx-primary);
  font-weight: 600;
}

.widdx-quick-contact-close {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.widdx-quick-contact-content {
  padding: 1rem;
}

.widdx-quick-contact-item {
  margin-bottom: 0.75rem;
}

.widdx-quick-contact-item:last-child {
  margin-bottom: 0;
}

/* PWA Install Prompt */
.widdx-pwa-install-prompt {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  max-width: 400px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  z-index: 1025;
  opacity: 0;
  visibility: hidden;
  transform: translateY(100px);
  transition: var(--widdx-transition);
}

.widdx-pwa-install-prompt.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.widdx-pwa-content {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  position: relative;
}

.widdx-pwa-icon {
  flex-shrink: 0;
  margin-right: 1rem;
}

.widdx-pwa-app-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
}

.widdx-pwa-text {
  flex: 1;
  margin-right: 1rem;
}

.widdx-pwa-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--widdx-primary);
}

.widdx-pwa-description {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.widdx-pwa-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.widdx-pwa-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 991.98px) {
  .widdx-newsletter-title {
    font-size: 1.75rem;
  }

  .widdx-newsletter-subtitle {
    font-size: 1rem;
  }

  .widdx-footer-main {
    padding: 3rem 0 1.5rem;
  }

  .widdx-quick-contact {
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .widdx-quick-contact-toggle {
    width: 50px;
    height: 50px;
  }

  .widdx-quick-contact-panel {
    width: 260px;
    bottom: 60px;
  }
}

/* Mobile Styles */
@media (max-width: 767.98px) {
  .widdx-topbar {
    display: none;
  }

  .widdx-main-nav {
    padding: 0.75rem 0;
  }

  .widdx-newsletter-section {
    padding: 2rem 0;
    text-align: center;
  }

  .widdx-newsletter-title {
    font-size: 1.5rem;
  }

  .widdx-newsletter-form {
    margin-top: 1.5rem;
  }

  .widdx-newsletter-input,
  .widdx-newsletter-btn {
    border-radius: 6px;
  }

  .widdx-footer-main {
    padding: 2.5rem 0 1rem;
  }

  .widdx-footer-widget {
    margin-bottom: 2.5rem;
  }

  .widdx-footer-bottom {
    text-align: center;
  }

  .widdx-footer-actions {
    justify-content: center;
    margin-top: 1rem;
  }

  .widdx-payment-methods {
    justify-content: center;
  }

  .widdx-social-icons {
    justify-content: center;
  }

  .widdx-quick-contact {
    bottom: 1rem;
    right: 1rem;
  }

  .widdx-quick-contact-panel {
    width: 240px;
    bottom: 50px;
    right: -100px;
  }

  .widdx-pwa-install-prompt {
    left: 1rem;
    right: 1rem;
    bottom: 1rem;
  }

  .widdx-pwa-content {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }

  .widdx-pwa-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .widdx-pwa-text {
    margin-right: 0;
  }
}

/* Small Mobile Styles */
@media (max-width: 575.98px) {
  .widdx-newsletter-input {
    margin-bottom: 0.75rem;
    border-radius: 25px;
  }

  .widdx-newsletter-btn {
    width: 100%;
    border-radius: 25px;
  }

  .widdx-footer-widget {
    text-align: center;
  }

  .widdx-footer-menu a {
    padding-left: 0;
  }

  .widdx-footer-menu a::before {
    display: none;
  }

  .widdx-contact-item {
    justify-content: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --widdx-header-bg: #1a1a1a;
    --widdx-header-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }

  .widdx-main-nav {
    background: var(--widdx-header-bg);
    border-bottom: 1px solid #333;
  }

  .widdx-brand-text {
    color: white;
  }

  .widdx-primary-nav .nav-link {
    color: #e0e0e0;
  }

  .widdx-primary-nav .nav-link:hover {
    color: var(--widdx-primary);
    background: rgba(44, 90, 160, 0.1);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .widdx-footer-menu a::before {
    content: '▶';
  }

  .widdx-social-icons .btn,
  .widdx-quick-contact-toggle {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .widdx-quick-contact-toggle {
    animation: none;
  }
}

/* ===== NOTIFICATION SYSTEM ===== */
.widdx-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  z-index: 1050;
  opacity: 0;
  transform: translateX(100px);
  transition: var(--widdx-transition);
  max-width: 400px;
}

.widdx-notification.show {
  opacity: 1;
  transform: translateX(0);
}

.widdx-notification-content {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
}

.widdx-notification-content i {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.widdx-notification-success {
  border-left: 4px solid var(--widdx-success);
}

.widdx-notification-success i {
  color: var(--widdx-success);
}

.widdx-notification-error {
  border-left: 4px solid var(--widdx-danger);
}

.widdx-notification-error i {
  color: var(--widdx-danger);
}

.widdx-notification-info {
  border-left: 4px solid var(--widdx-info);
}

.widdx-notification-info i {
  color: var(--widdx-info);
}

/* ===== ADDITIONAL HEADER STATES ===== */
.widdx-main-nav.scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.widdx-main-nav.header-hidden {
  transform: translateY(-100%);
}

/* ===== LOADING STATES ===== */
.widdx-lang-btn.loading,
.widdx-lang-currency-btn.loading {
  position: relative;
  color: transparent;
}

.widdx-lang-btn.loading::after,
.widdx-lang-currency-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: widdx-spin 1s linear infinite;
}

@keyframes widdx-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== MOBILE MENU BODY LOCK ===== */
body.mobile-menu-open {
  overflow: hidden;
}

/* ===== SEARCH FOCUS STATE ===== */
.widdx-search-widget.search-focused .form-control {
  box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
  border-color: var(--widdx-primary);
}
