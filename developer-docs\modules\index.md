+++
chapter = true
icon = "<i class='fa fa-cube fa-fw'></i>"
next = "/modules/getting-started/"
title = "Modules"
weight = 0

+++

## Introduction

A module is a collection of functions that provide additional functionality to the WHMCS platform, most commonly used to integrate with third party services and APIs.

#### Choose a module type to begin...

<div class="row text-center link-blocks">
    <div class="col-sm-6">
        <a href="/provisioning-modules/">
            <i class="fa fa-cog"></i>
            <h3>Provisioning Modules</h3>
            <p>For modules that enable provisioning and management of products and services via API.</p>
        </a>
    </div>
    <div class="col-sm-6">
        <a href="/addon-modules/">
            <i class="fa fa-cube"></i>
            <h3>Addon Modules</h3>
            <p>Create a module that provides additional functionality within WHMCS.</p>
        </a>
    </div>
    <div class="col-sm-6">
        <a href="/domain-registrars/">
            <i class="fa fa-globe"></i>
            <h3>Registrar Modules</h3>
            <p>Connect with domain registrars to allow for availability checking, domain registration and management.</p>
        </a>
    </div>
    <div class="col-sm-6">
        <a href="/payment-gateways/">
            <i class="fa fa-money"></i>
            <h3>Gateway Modules</h3>
            <p>Integrate with payment gateway providers to collect and process payments.</p>
        </a>
    </div>
    <div class="col-sm-6">
        <a href="/mail-providers/">
            <i class="fa fa-envelope"></i>
            <h3>Mail Provider Modules</h3>
            <p>Add custom mail providers to WHMCS.</p>
        </a>
    </div>
    <div class="col-sm-6">
        <a href="/notification-providers/">
            <i class="fa fa-bell-o"></i>
            <h3>Notification Providers</h3>
            <p>Integrate additional notification services for use with the Notifications system.</p>
        </a>
    </div>
</div>
