+++
chapter = true
icon = "<i class='fa fa-code fa-fw'></i>"
next = "/advanced/creating-pages/"
title = "Advanced"
weight = 0

+++

## Introduction

The following are some advanced customisation options.

* **[Upgrading to WHMCS 8.0](/advanced/upgrade-to-whmcs-8/)**<br>Everything you need to know to ensure a smooth transition to Version 8.
* **[Creating Pages](/advanced/creating-pages/)**<br>Learn how to create additional pages within the WHMCS client area.
* **[Authentication via CurrentUser](/advanced/authentication/)**<br>Learn how to assess the current authentication state with `CurrentUser`.
* **[Interacting with the Database](/advanced/db-interaction/)**<br>Learn how to interact with the WHMCS database
* **[Date Functions](/advanced/date-functions/)**<br>Learn how to use the date helper functions in WHMCS
* **[Currency Formatting](/advanced/currency-formatting/)**<br>Learn how to use the currency formatting helper functions in WHMCS
* **[Logging](/advanced/logging/)**<br>Learn how to use the logging helper functions in WHMCS
* **[Widgets](/advanced/widgets/)**<br>Learn how to create widgets using AbstractWidget
* **[JSON File](/advanced/json-file/)**<br>Provide meta information for modules
