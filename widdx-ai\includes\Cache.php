<?php
require_once 'Database.php';

class Cache {
    private $db;
    private $pdo;
    private $cacheProvider;
    private $ttl;
    private $enabled;

    public function __construct() {
        $this->db = new Database();
        $this->pdo = $this->db->getConnection();
        $this->cacheProvider = CACHE_PROVIDER;
        $this->ttl = CACHE_TTL;
        $this->enabled = CACHE_ENABLED;

        // Initialize cache provider
        $this->initCacheProvider();
    }

    private function initCacheProvider() {
        if ($this->cacheProvider === 'redis') {
            $this->redis = new Redis();
            try {
                $this->redis->connect(REDIS_HOST, REDIS_PORT);
                if (!empty(REDIS_PASSWORD)) {
                    $this->redis->auth(REDIS_PASSWORD);
                }
            } catch (Exception $e) {
                error_log("Redis connection failed: " . $e->getMessage());
                $this->enabled = false;
            }
        }
    }

    public function get($key) {
        if (!$this->enabled) {
            return null;
        }

        try {
            if ($this->cacheProvider === 'redis') {
                return $this->redis->get($key);
            } else {
                // Fallback to file cache
                $cacheFile = __DIR__ . '/../cache/' . $key;
                if (file_exists($cacheFile)) {
                    $data = file_get_contents($cacheFile);
                    $cache = json_decode($data, true);
                    if ($cache && isset($cache['ttl']) && $cache['ttl'] > time()) {
                        return $cache['data'];
                    }
                    // Remove expired cache
                    unlink($cacheFile);
                }
            }
        } catch (Exception $e) {
            error_log("Cache get error: " . $e->getMessage());
        }

        return null;
    }

    public function set($key, $value, $ttl = null) {
        if (!$this->enabled) {
            return false;
        }

        try {
            $ttl = $ttl ?: $this->ttl;
            if ($this->cacheProvider === 'redis') {
                return $this->redis->setex($key, $ttl, json_encode($value));
            } else {
                // Create cache directory if it doesn't exist
                if (!file_exists(__DIR__ . '/../cache')) {
                    mkdir(__DIR__ . '/../cache', 0777, true);
                }

                $cacheFile = __DIR__ . '/../cache/' . $key;
                $cacheData = [
                    'data' => $value,
                    'ttl' => time() + $ttl
                ];
                return file_put_contents($cacheFile, json_encode($cacheData));
            }
        } catch (Exception $e) {
            error_log("Cache set error: " . $e->getMessage());
            return false;
        }
    }

    public function delete($key) {
        if (!$this->enabled) {
            return false;
        }

        try {
            if ($this->cacheProvider === 'redis') {
                return $this->redis->del($key);
            } else {
                $cacheFile = __DIR__ . '/../cache/' . $key;
                return file_exists($cacheFile) ? unlink($cacheFile) : true;
            }
        } catch (Exception $e) {
            error_log("Cache delete error: " . $e->getMessage());
            return false;
        }
    }

    public function clear() {
        if (!$this->enabled) {
            return false;
        }

        try {
            if ($this->cacheProvider === 'redis') {
                return $this->redis->flushDb();
            } else {
                $cacheDir = __DIR__ . '/../cache';
                if (file_exists($cacheDir)) {
                    $files = glob($cacheDir . '/*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                        }
                    }
                }
                return true;
            }
        } catch (Exception $e) {
            error_log("Cache clear error: " . $e->getMessage());
            return false;
        }
    }

    public function isCached($key) {
        return $this->get($key) !== null;
    }

    public function getCacheStats() {
        if (!$this->enabled) {
            return [
                'enabled' => false,
                'provider' => $this->cacheProvider,
                'stats' => []
            ];
        }

        try {
            if ($this->cacheProvider === 'redis') {
                return [
                    'enabled' => true,
                    'provider' => $this->cacheProvider,
                    'stats' => [
                        'info' => $this->redis->info('memory'),
                        'size' => $this->redis->dbsize()
                    ]
                ];
            } else {
                $cacheDir = __DIR__ . '/../cache';
                $files = glob($cacheDir . '/*');
                return [
                    'enabled' => true,
                    'provider' => $this->cacheProvider,
                    'stats' => [
                        'count' => count($files),
                        'size' => $this->getCacheDirectorySize($cacheDir)
                    ]
                ];
            }
        } catch (Exception $e) {
            error_log("Cache stats error: " . $e->getMessage());
            return [
                'enabled' => true,
                'provider' => $this->cacheProvider,
                'stats' => []
            ];
        }
    }

    private function getCacheDirectorySize($dir) {
        $size = 0;
        foreach (glob(rtrim($dir, '/').'/*', GLOB_NOSORT) as $each) {
            $size += is_file($each) ? filesize($each) : $this->getCacheDirectorySize($each);
        }
        return $size;
    }

    public function isEnabled() {
        return $this->enabled;
    }

    public function setEnabled($enabled) {
        $this->enabled = $enabled;
    }

    public function getProvider() {
        return $this->cacheProvider;
    }

    public function setProvider($provider) {
        $this->cacheProvider = $provider;
        $this->initCacheProvider();
    }

    public function getTTL() {
        return $this->ttl;
    }

    public function setTTL($ttl) {
        $this->ttl = $ttl;
    }
}
