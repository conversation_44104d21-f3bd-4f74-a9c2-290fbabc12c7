/*!
 * WIDDX Test Modern Styles - للاختبار السريع
 * إضافة أنماط واضحة ومرئية للتأكد من تطبيق التغييرات
 */

/* تأثيرات واضحة للاختبار */
.widdx-header {
    border: 3px solid #ff0000 !important;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important;
    animation: testPulse 2s infinite;
}

@keyframes testPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.widdx-topbar {
    background: #ff4757 !important;
    color: white !important;
    font-weight: bold !important;
    text-align: center !important;
    padding: 15px 0 !important;
}

.widdx-topbar::before {
    content: "🎉 الهيدر الحديث يعمل بنجاح! 🎉" !important;
    display: block !important;
    font-size: 16px !important;
    margin-bottom: 5px !important;
}

.widdx-main-nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: 2px solid #ffa502 !important;
    box-shadow: 0 5px 20px rgba(255, 165, 2, 0.5) !important;
}

.widdx-brand-text {
    color: #ffffff !important;
    font-size: 2rem !important;
    font-weight: bold !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5) !important;
}

.widdx-primary-nav .nav-link {
    color: #ffffff !important;
    font-weight: bold !important;
    background: rgba(255,255,255,0.1) !important;
    margin: 0 5px !important;
    border-radius: 20px !important;
    padding: 10px 20px !important;
    transition: all 0.3s ease !important;
}

.widdx-primary-nav .nav-link:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
}

.widdx-cart-btn {
    background: #ff3838 !important;
    color: white !important;
    border: 2px solid #ffffff !important;
    border-radius: 25px !important;
    font-weight: bold !important;
    animation: cartBounce 1s infinite;
}

@keyframes cartBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.widdx-cart-count {
    background: #ffa502 !important;
    color: #000 !important;
    font-weight: bold !important;
    animation: countPulse 1.5s infinite;
}

@keyframes countPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

/* فوتر الاختبار */
.widdx-footer {
    border: 3px solid #00ff00 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.widdx-newsletter-section {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important;
    position: relative !important;
}

.widdx-newsletter-section::before {
    content: "📧 قسم النشرة الإخبارية الحديث 📧" !important;
    display: block !important;
    text-align: center !important;
    font-size: 18px !important;
    font-weight: bold !important;
    color: white !important;
    padding: 10px !important;
    background: rgba(0,0,0,0.3) !important;
}

.widdx-newsletter-title {
    color: #ffffff !important;
    font-size: 2.5rem !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5) !important;
    animation: titleGlow 2s infinite alternate;
}

@keyframes titleGlow {
    0% { text-shadow: 2px 2px 4px rgba(0,0,0,0.5); }
    100% { text-shadow: 2px 2px 20px rgba(255,255,255,0.8); }
}

.widdx-newsletter-btn {
    background: #ff4757 !important;
    border: 2px solid #ffffff !important;
    color: white !important;
    font-weight: bold !important;
    animation: btnGlow 1.5s infinite;
}

@keyframes btnGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 71, 87, 0.5); }
    50% { box-shadow: 0 0 20px rgba(255, 71, 87, 1); }
}

.widdx-footer-main {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    border: 2px solid #f39c12 !important;
}

.widdx-footer-title {
    color: #f39c12 !important;
    font-size: 1.3rem !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
}

.widdx-footer-title::after {
    background: #e74c3c !important;
    height: 3px !important;
    width: 50px !important;
}

.widdx-footer-menu a {
    color: #ecf0f1 !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.widdx-footer-menu a:hover {
    color: #f39c12 !important;
    padding-left: 20px !important;
    transform: translateX(10px) !important;
}

.widdx-footer-bottom {
    background: #1a252f !important;
    border-top: 3px solid #e74c3c !important;
}

.widdx-back-to-top {
    background: #e74c3c !important;
    border: 2px solid #ffffff !important;
    animation: backToTopFloat 2s infinite;
}

@keyframes backToTopFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* ويدجت الاتصال السريع */
.widdx-quick-contact-toggle {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important;
    animation: quickContactPulse 2s infinite;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.7) !important;
}

@keyframes quickContactPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* شريط التقدم */
.widdx-progress-bar {
    background: rgba(255, 165, 2, 0.3) !important;
    height: 5px !important;
}

.widdx-progress-fill {
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #ffa502) !important;
    animation: progressGlow 1s infinite;
}

@keyframes progressGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* رسالة تأكيد */
body::before {
    content: "✅ تم تحميل الأنماط الحديثة بنجاح! ✅" !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: #27ae60 !important;
    color: white !important;
    text-align: center !important;
    padding: 10px !important;
    font-weight: bold !important;
    font-size: 16px !important;
    z-index: 9999 !important;
    animation: successMessage 3s ease-in-out;
}

@keyframes successMessage {
    0% { transform: translateY(-100%); }
    10%, 90% { transform: translateY(0); }
    100% { transform: translateY(-100%); }
}

/* تأثيرات إضافية للموبايل */
@media (max-width: 768px) {
    .widdx-header {
        border-width: 2px !important;
    }
    
    .widdx-topbar::before {
        content: "📱 الهيدر الحديث - موبايل 📱" !important;
        font-size: 14px !important;
    }
    
    .widdx-newsletter-section::before {
        font-size: 16px !important;
    }
}
