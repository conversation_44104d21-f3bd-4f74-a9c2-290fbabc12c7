/*!
 * Enhanced 3D Secure JavaScript for WIDDX
 * 
 * Comprehensive 3D Secure 2.2.0 frontend implementation with:
 * - Advanced browser fingerprinting
 * - Challenge window management
 * - Mobile optimization
 * - Real-time status updates
 * - Accessibility support
 * 
 * @package WIDDX
 * @version 1.0.0
 */

(function() {
    'use strict';

    class Enhanced3DSecure {
        constructor(options = {}) {
            this.options = {
                challengeTimeout: 300000, // 5 minutes
                challengeWindowSize: '05', // Full screen
                statusUpdateInterval: 2000, // 2 seconds
                maxRetries: 3,
                debug: false,
                ...options
            };

            this.transactionId = null;
            this.challengeWindow = null;
            this.statusTimer = null;
            this.timeoutTimer = null;
            this.retryCount = 0;
            this.browserInfo = null;

            this.init();
        }

        init() {
            this.collectBrowserInformation();
            this.setupEventListeners();
            this.log('Enhanced 3D Secure initialized');
        }

        /**
         * Collect comprehensive browser information for 3DS 2.2.0
         */
        collectBrowserInformation() {
            const screen = window.screen || {};
            const navigator = window.navigator || {};
            const location = window.location || {};

            this.browserInfo = {
                // Required 3DS 2.2.0 fields
                acceptHeader: this.getAcceptHeader(),
                userAgent: navigator.userAgent || '',
                browserLanguage: navigator.language || 'en',
                browserColorDepth: screen.colorDepth || 24,
                browserScreenHeight: screen.height || 1080,
                browserScreenWidth: screen.width || 1920,
                browserTimezone: this.getTimezone(),
                browserJavaEnabled: navigator.javaEnabled ? navigator.javaEnabled() : false,
                browserJavascriptEnabled: true,

                // Enhanced fingerprinting
                platform: navigator.platform || '',
                cookieEnabled: navigator.cookieEnabled || false,
                doNotTrack: navigator.doNotTrack || '',
                hardwareConcurrency: navigator.hardwareConcurrency || 1,
                maxTouchPoints: navigator.maxTouchPoints || 0,
                deviceMemory: navigator.deviceMemory || 0,
                
                // Screen information
                screenAvailHeight: screen.availHeight || screen.height || 1080,
                screenAvailWidth: screen.availWidth || screen.width || 1920,
                screenPixelDepth: screen.pixelDepth || screen.colorDepth || 24,
                
                // Window information
                windowInnerHeight: window.innerHeight || 1080,
                windowInnerWidth: window.innerWidth || 1920,
                windowOuterHeight: window.outerHeight || 1080,
                windowOuterWidth: window.outerWidth || 1920,
                
                // Device orientation
                deviceOrientation: this.getDeviceOrientation(),
                
                // Connection information
                connectionType: this.getConnectionType(),
                connectionEffectiveType: this.getConnectionEffectiveType(),
                
                // Performance information
                performanceTiming: this.getPerformanceTiming(),
                
                // Canvas fingerprint
                canvasFingerprint: this.generateCanvasFingerprint(),
                
                // WebGL fingerprint
                webglFingerprint: this.generateWebGLFingerprint(),
                
                // Audio fingerprint
                audioFingerprint: this.generateAudioFingerprint(),
                
                // Fonts detection
                availableFonts: this.detectAvailableFonts(),
                
                // Plugins information
                plugins: this.getPluginsInfo(),
                
                // Local storage support
                localStorageEnabled: this.isLocalStorageEnabled(),
                sessionStorageEnabled: this.isSessionStorageEnabled(),
                indexedDBEnabled: this.isIndexedDBEnabled(),
                
                // Timestamp
                collectionTimestamp: Date.now(),
                collectionDate: new Date().toISOString(),
                
                // Browser fingerprint hash
                fingerprint: null // Will be calculated after all data is collected
            };

            // Calculate comprehensive fingerprint
            this.browserInfo.fingerprint = this.calculateFingerprint();

            this.log('Browser information collected', this.browserInfo);
        }

        /**
         * Initialize 3D Secure authentication
         */
        async initializeAuthentication(paymentData) {
            try {
                this.log('Initializing 3D Secure authentication', paymentData);

                // Update browser info with latest data
                this.updateBrowserInfo();

                // Prepare authentication request
                const authRequest = {
                    ...paymentData,
                    browserInfo: this.browserInfo,
                    challengeWindowSize: this.options.challengeWindowSize,
                    challengeTimeout: this.options.challengeTimeout
                };

                // Send authentication request
                const response = await this.sendAuthRequest(authRequest);

                if (response.success) {
                    this.transactionId = response.transactionId;
                    
                    if (response.challengeRequired) {
                        return this.handleChallengeFlow(response);
                    } else {
                        return this.handleFrictionlessFlow(response);
                    }
                } else {
                    throw new Error(response.message || '3D Secure initialization failed');
                }

            } catch (error) {
                this.log('3D Secure initialization failed', error);
                return this.handleError(error);
            }
        }

        /**
         * Handle challenge flow
         */
        async handleChallengeFlow(response) {
            this.log('Handling 3D Secure challenge flow', response);

            try {
                // Create challenge window
                this.challengeWindow = this.createChallengeWindow(response.challengeUrl);
                
                // Start timeout timer
                this.startTimeoutTimer();
                
                // Start status monitoring
                this.startStatusMonitoring();
                
                // Show challenge UI
                this.showChallengeUI();
                
                // Wait for challenge completion
                return new Promise((resolve, reject) => {
                    this.challengeResolve = resolve;
                    this.challengeReject = reject;
                });

            } catch (error) {
                this.log('Challenge flow error', error);
                return this.handleError(error);
            }
        }

        /**
         * Handle frictionless flow
         */
        async handleFrictionlessFlow(response) {
            this.log('Handling frictionless 3D Secure flow', response);

            // Update UI to show frictionless authentication
            this.showFrictionlessUI();

            // Wait for authentication result
            const result = await this.waitForAuthenticationResult();

            if (result.success) {
                this.showSuccessUI();
                return {
                    success: true,
                    authenticated: true,
                    authenticationData: result.authenticationData
                };
            } else {
                return this.handleError(new Error(result.message));
            }
        }

        /**
         * Create challenge window with optimal sizing
         */
        createChallengeWindow(challengeUrl) {
            const windowFeatures = this.getChallengeWindowFeatures();
            
            this.log('Creating challenge window', { url: challengeUrl, features: windowFeatures });

            // For mobile devices, use full screen
            if (this.isMobileDevice()) {
                return this.createMobileChallengeWindow(challengeUrl);
            }

            // For desktop, use popup window
            const challengeWindow = window.open(
                challengeUrl,
                '3ds_challenge',
                windowFeatures
            );

            if (!challengeWindow) {
                throw new Error('Challenge window blocked by popup blocker');
            }

            // Monitor window close
            this.monitorChallengeWindow(challengeWindow);

            return challengeWindow;
        }

        /**
         * Create mobile-optimized challenge window
         */
        createMobileChallengeWindow(challengeUrl) {
            // Create overlay iframe for mobile
            const overlay = document.createElement('div');
            overlay.className = 'widdx-3ds-challenge-overlay';
            overlay.innerHTML = `
                <div class="widdx-3ds-challenge-container">
                    <div class="widdx-3ds-challenge-header">
                        <h3>Secure Authentication</h3>
                        <button class="widdx-3ds-challenge-close" aria-label="Close">×</button>
                    </div>
                    <iframe 
                        src="${challengeUrl}" 
                        class="widdx-3ds-challenge-iframe"
                        allow="payment"
                        sandbox="allow-scripts allow-same-origin allow-forms allow-top-navigation"
                    ></iframe>
                    <div class="widdx-3ds-challenge-footer">
                        <div class="widdx-3ds-challenge-timer">
                            <span>Time remaining: <span id="widdx-3ds-timer">5:00</span></span>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(overlay);

            // Add event listeners
            overlay.querySelector('.widdx-3ds-challenge-close').addEventListener('click', () => {
                this.cancelChallenge();
            });

            // Start countdown timer
            this.startCountdownTimer();

            return overlay;
        }

        /**
         * Monitor challenge window for completion
         */
        monitorChallengeWindow(challengeWindow) {
            const checkClosed = () => {
                if (challengeWindow.closed) {
                    this.log('Challenge window closed');
                    this.handleChallengeComplete();
                } else {
                    setTimeout(checkClosed, 1000);
                }
            };

            setTimeout(checkClosed, 1000);
        }

        /**
         * Start status monitoring
         */
        startStatusMonitoring() {
            this.statusTimer = setInterval(async () => {
                try {
                    const status = await this.checkAuthenticationStatus();
                    
                    if (status.completed) {
                        this.handleChallengeComplete(status);
                    } else if (status.failed) {
                        this.handleChallengeFailed(status);
                    }
                } catch (error) {
                    this.log('Status check failed', error);
                }
            }, this.options.statusUpdateInterval);
        }

        /**
         * Start timeout timer
         */
        startTimeoutTimer() {
            this.timeoutTimer = setTimeout(() => {
                this.log('3D Secure challenge timeout');
                this.handleChallengeTimeout();
            }, this.options.challengeTimeout);
        }

        /**
         * Start countdown timer for mobile UI
         */
        startCountdownTimer() {
            const timerElement = document.getElementById('widdx-3ds-timer');
            if (!timerElement) return;

            let timeLeft = this.options.challengeTimeout / 1000; // Convert to seconds

            const updateTimer = () => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                if (timeLeft <= 0) {
                    this.handleChallengeTimeout();
                } else {
                    timeLeft--;
                    setTimeout(updateTimer, 1000);
                }
            };

            updateTimer();
        }

        /**
         * Handle challenge completion
         */
        async handleChallengeComplete(status = null) {
            this.log('Challenge completed', status);

            this.cleanup();

            if (!status) {
                status = await this.checkAuthenticationStatus();
            }

            if (status.success) {
                this.showSuccessUI();
                if (this.challengeResolve) {
                    this.challengeResolve({
                        success: true,
                        authenticated: true,
                        authenticationData: status.authenticationData
                    });
                }
            } else {
                this.handleError(new Error(status.message || 'Authentication failed'));
            }
        }

        /**
         * Handle challenge failure
         */
        handleChallengeFailed(status) {
            this.log('Challenge failed', status);
            this.cleanup();
            this.handleError(new Error(status.message || 'Authentication failed'));
        }

        /**
         * Handle challenge timeout
         */
        handleChallengeTimeout() {
            this.log('Challenge timeout');
            this.cleanup();
            this.handleError(new Error('Authentication timeout. Please try again.'));
        }

        /**
         * Cancel challenge
         */
        cancelChallenge() {
            this.log('Challenge cancelled by user');
            this.cleanup();
            this.handleError(new Error('Authentication cancelled by user'));
        }

        /**
         * Cleanup resources
         */
        cleanup() {
            if (this.statusTimer) {
                clearInterval(this.statusTimer);
                this.statusTimer = null;
            }

            if (this.timeoutTimer) {
                clearTimeout(this.timeoutTimer);
                this.timeoutTimer = null;
            }

            if (this.challengeWindow) {
                if (this.challengeWindow.close) {
                    this.challengeWindow.close();
                } else if (this.challengeWindow.remove) {
                    this.challengeWindow.remove();
                }
                this.challengeWindow = null;
            }

            // Remove mobile overlay if exists
            const overlay = document.querySelector('.widdx-3ds-challenge-overlay');
            if (overlay) {
                overlay.remove();
            }
        }

        /**
         * Handle errors
         */
        handleError(error) {
            this.log('3D Secure error', error);
            this.cleanup();
            this.showErrorUI(error.message);

            if (this.challengeReject) {
                this.challengeReject(error);
            }

            return {
                success: false,
                error: error.message,
                retry: this.retryCount < this.options.maxRetries
            };
        }

        /**
         * Utility methods for browser fingerprinting
         */
        getAcceptHeader() {
            // Simulate typical browser accept header
            return 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
        }

        getTimezone() {
            try {
                return Intl.DateTimeFormat().resolvedOptions().timeZone;
            } catch (e) {
                return new Date().getTimezoneOffset().toString();
            }
        }

        getDeviceOrientation() {
            if (screen.orientation) {
                return screen.orientation.angle;
            }
            return window.orientation || 0;
        }

        getConnectionType() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            return connection ? connection.type : 'unknown';
        }

        getConnectionEffectiveType() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            return connection ? connection.effectiveType : 'unknown';
        }

        getPerformanceTiming() {
            if (performance && performance.timing) {
                return {
                    navigationStart: performance.timing.navigationStart,
                    loadEventEnd: performance.timing.loadEventEnd,
                    domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd
                };
            }
            return null;
        }

        generateCanvasFingerprint() {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('3D Secure fingerprint', 2, 2);
                return canvas.toDataURL().slice(-50);
            } catch (e) {
                return 'unavailable';
            }
        }

        generateWebGLFingerprint() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (!gl) return 'unavailable';
                
                const renderer = gl.getParameter(gl.RENDERER);
                const vendor = gl.getParameter(gl.VENDOR);
                return `${vendor}~${renderer}`.slice(-50);
            } catch (e) {
                return 'unavailable';
            }
        }

        generateAudioFingerprint() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const analyser = audioContext.createAnalyser();
                oscillator.connect(analyser);
                oscillator.frequency.value = 1000;
                oscillator.start();
                
                const dataArray = new Uint8Array(analyser.frequencyBinCount);
                analyser.getByteFrequencyData(dataArray);
                
                oscillator.stop();
                audioContext.close();
                
                return Array.from(dataArray.slice(0, 10)).join('');
            } catch (e) {
                return 'unavailable';
            }
        }

        detectAvailableFonts() {
            const testFonts = ['Arial', 'Times', 'Courier', 'Helvetica', 'Georgia', 'Verdana'];
            const availableFonts = [];
            
            testFonts.forEach(font => {
                if (this.isFontAvailable(font)) {
                    availableFonts.push(font);
                }
            });
            
            return availableFonts;
        }

        isFontAvailable(fontName) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            ctx.font = `12px ${fontName}`;
            const testWidth = ctx.measureText('test').width;
            
            ctx.font = '12px monospace';
            const defaultWidth = ctx.measureText('test').width;
            
            return testWidth !== defaultWidth;
        }

        getPluginsInfo() {
            const plugins = [];
            for (let i = 0; i < navigator.plugins.length; i++) {
                plugins.push(navigator.plugins[i].name);
            }
            return plugins.slice(0, 5); // Limit to first 5 plugins
        }

        isLocalStorageEnabled() {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        }

        isSessionStorageEnabled() {
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        }

        isIndexedDBEnabled() {
            return 'indexedDB' in window;
        }

        calculateFingerprint() {
            const data = JSON.stringify(this.browserInfo);
            return this.simpleHash(data);
        }

        simpleHash(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            return Math.abs(hash).toString(16);
        }

        updateBrowserInfo() {
            // Update dynamic values
            this.browserInfo.windowInnerHeight = window.innerHeight;
            this.browserInfo.windowInnerWidth = window.innerWidth;
            this.browserInfo.deviceOrientation = this.getDeviceOrientation();
            this.browserInfo.collectionTimestamp = Date.now();
            this.browserInfo.collectionDate = new Date().toISOString();
        }

        isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        getChallengeWindowFeatures() {
            const width = 600;
            const height = 400;
            const left = (screen.width - width) / 2;
            const top = (screen.height - height) / 2;

            return `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no`;
        }

        /**
         * API communication methods
         */
        async sendAuthRequest(data) {
            const response = await fetch('/modules/gateways/lahza/3ds_init.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        }

        async checkAuthenticationStatus() {
            const response = await fetch(`/modules/gateways/lahza/3ds_status.php?transaction_id=${this.transactionId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        }

        async waitForAuthenticationResult() {
            return new Promise((resolve) => {
                const checkStatus = async () => {
                    try {
                        const status = await this.checkAuthenticationStatus();
                        if (status.completed || status.failed) {
                            resolve(status);
                        } else {
                            setTimeout(checkStatus, 1000);
                        }
                    } catch (error) {
                        resolve({ success: false, message: error.message });
                    }
                };
                checkStatus();
            });
        }

        /**
         * UI methods
         */
        showChallengeUI() {
            this.updateUI('challenge', 'Please complete the authentication in the popup window.');
        }

        showFrictionlessUI() {
            this.updateUI('processing', 'Authenticating your payment...');
        }

        showSuccessUI() {
            this.updateUI('success', 'Authentication successful! Processing payment...');
        }

        showErrorUI(message) {
            this.updateUI('error', message);
        }

        updateUI(status, message) {
            const statusElement = document.getElementById('3ds-status');
            if (statusElement) {
                statusElement.className = `3ds-status 3ds-status-${status}`;
                statusElement.textContent = message;
            }

            this.log(`UI updated: ${status} - ${message}`);
        }

        setupEventListeners() {
            // Listen for messages from challenge window
            window.addEventListener('message', (event) => {
                if (event.data && event.data.type === '3ds_challenge_complete') {
                    this.handleChallengeComplete(event.data);
                }
            });

            // Handle page unload
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });
        }

        log(message, data = null) {
            if (this.options.debug) {
                console.log(`[3DS] ${message}`, data);
            }
        }
    }

    // Export to global scope
    window.Enhanced3DSecure = Enhanced3DSecure;

    // Auto-initialize if container exists
    document.addEventListener('DOMContentLoaded', () => {
        const container = document.getElementById('3ds-container');
        if (container) {
            window.widdx3DS = new Enhanced3DSecure({
                debug: container.dataset.debug === 'true'
            });
        }
    });

})();
