<?php
/**
 * WIDDX AI Assistant - Installation Script
 * 
 * This script handles the initial setup and configuration of the WIDDX AI Assistant.
 */

// Set error reporting for development
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Define base path
define('BASE_PATH', __DIR__);

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die('Error: PHP 7.4 or higher is required. Current version: ' . PHP_VERSION);
}

// Check if .env file exists
if (!file_exists(__DIR__ . '/.env')) {
    if (!copy(__DIR__ . '/.env.example', __DIR__ . '/.env')) {
        die('Error: Please create a .env file. You can use .env.example as a template.');
    }
    echo "Created .env file from .env.example\n";
}

// Load environment variables
require_once __DIR__ . '/includes/Env.php';

// Load required classes
require_once __DIR__ . '/includes/ErrorHandler.php';
require_once __DIR__ . '/includes/Database.php';
require_once __DIR__ . '/includes/Cache.php';
require_once __DIR__ . '/includes/Logger.php';
require_once __DIR__ . '/includes/Security.php';

// Initialize error handling
$errorHandler = new ErrorHandler();

// Initialize database
try {
    $db = Database::getInstance();
    echo "✓ Database connection successful\n";
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage() . "\n");
}

// Initialize other components
try {
    $cache = new Cache();
    $logger = new Logger();
    $security = new Security();
    
    echo "✓ Core components initialized successfully\n";
} catch (Exception $e) {
    die("Error initializing components: " . $e->getMessage() . "\n");
}

// Check if database tables exist
try {
    $tables = [
        'questions' => "SHOW TABLES LIKE 'questions'",
        'logs' => "SHOW TABLES LIKE 'logs'"
    ];
    
    $missingTables = [];
    $connection = $db->getConnection();
    
    foreach ($tables as $table => $query) {
        $stmt = $connection->query($query);
        if ($stmt->rowCount() === 0) {
            $missingTables[] = $table;
        }
    }
    
    if (!empty($missingTables)) {
        echo "\n⚠️  The following database tables are missing: " . implode(', ', $missingTables) . "\n";
        echo "Would you like to create them now? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = trim(fgets($handle));
        
        if (strtolower($line) === 'y') {
            // Execute schema.sql
            $sql = file_get_contents(__DIR__ . '/database/setup.sql');
            try {
                $connection->exec($sql);
                echo "✓ Database tables created successfully\n";
            } catch (PDOException $e) {
                die("Error creating tables: " . $e->getMessage() . "\n");
            }
        } else {
            echo "Skipping table creation. Some features may not work.\n";
        }
    } else {
        echo "✓ All database tables exist\n";
    }
    
} catch (Exception $e) {
    die("Error checking database tables: " . $e->getMessage() . "\n");
}

echo "\n🎉 WIDDX AI Assistant installation completed successfully!\n";
echo "You can now access the application at: " . (Env::get('APP_URL', 'http://localhost:8000')) . "\n\n";

// Check required extensions
$requiredExtensions = ['mysqli', 'curl', 'json', 'redis'];
$missingExtensions = array_filter($requiredExtensions, function($ext) {
    return !extension_loaded($ext);
});

if (!empty($missingExtensions)) {
    die('Missing required PHP extensions: ' . implode(', ', $missingExtensions));
}

// Check database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
    echo "✓ Database connection successful\n";
} catch (Exception $e) {
    die("✗ Database connection failed: " . $e->getMessage());
}

// Check if database exists
try {
    $pdo->query("USE widdx_ai");
    echo "✓ Database exists\n";
} catch (Exception $e) {
    // Database doesn't exist, create it
    try {
        $pdo->query("CREATE DATABASE IF NOT EXISTS widdx_ai");
        $pdo->query("USE widdx_ai");
        echo "✓ Database created\n";
    } catch (Exception $e) {
        die("✗ Failed to create database: " . $e->getMessage());
    }
}

// Create required directories
$requiredDirs = [
    __DIR__ . '/logs',
    __DIR__ . '/cache',
    __DIR__ . '/uploads'
];

foreach ($requiredDirs as $dir) {
    if (!file_exists($dir)) {
        if (!mkdir($dir, 0777, true)) {
            die("✗ Failed to create directory: " . $dir);
        }
    }
    if (!is_writable($dir)) {
        die("✗ Directory not writable: " . $dir);
    }
}

echo "✓ Required directories created and writable\n";

// Import database schema
try {
    $sql = file_get_contents(__DIR__ . '/database/setup.sql');
    $pdo->exec($sql);
    echo "✓ Database schema imported successfully\n";
} catch (Exception $e) {
    die("✗ Failed to import database schema: " . $e->getMessage());
}

// Check Redis connection
try {
    $redis = new Redis();
    $redis->connect(REDIS_HOST, REDIS_PORT);
    if (!empty(REDIS_PASSWORD)) {
        $redis->auth(REDIS_PASSWORD);
    }
    echo "✓ Redis connection successful\n";
} catch (Exception $e) {
    echo "✗ Redis connection failed: " . $e->getMessage() . "\n";
    echo "Warning: Redis is not available. Caching will be disabled.\n";
}

// Create configuration file if it doesn't exist
if (!file_exists(__DIR__ . '/config/config_production.php')) {
    $config = [
        'APP_ENV' => 'production',
        'APP_DEBUG' => false,
        'API_KEY_DEEPSEEK' => '',
        'API_KEY_GEMINI' => '',
        'REDIS_HOST' => '127.0.0.1',
        'REDIS_PORT' => 6379,
        'REDIS_PASSWORD' => 'widdx'
    ];

    $configContent = "<?php\n";
    foreach ($config as $key => $value) {
        $configContent .= "define('{$key}', '" . addslashes($value) . "');\n";
    }

    file_put_contents(__DIR__ . '/config/config_production.php', $configContent);
    echo "✓ Created production configuration file\n";
}

// Create logs directory if it doesn't exist
if (!file_exists(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0777, true);
    echo "✓ Created logs directory\n";
}

// Create cache directory if it doesn't exist
if (!file_exists(__DIR__ . '/cache')) {
    mkdir(__DIR__ . '/cache', 0777, true);
    echo "✓ Created cache directory\n";
}

// Set permissions
chmod(__DIR__ . '/logs', 0777);
chmod(__DIR__ . '/cache', 0777);

// Display completion message
echo "\n✓ Installation completed successfully!\n";
echo "\nNext steps:\n";
echo "1. Configure your API keys in config/config_production.php\n";
echo "2. Set up Redis server (optional for caching)\n";
echo "3. Point your web server to the public/ directory\n";
echo "4. Access the application through your web browser\n";
