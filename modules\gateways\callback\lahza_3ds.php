<?php
/**
 * <PERSON><PERSON>za 3D Secure Callback Handler
 * Handles 3D Secure authentication responses
 *
 * @package    WHMCS
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @version    1.0.0
 */

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');
$gatewayModuleName = str_replace('_3ds', '', $gatewayModuleName);

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    die("Module Not Activated");
}

// Load enhanced logger
require_once __DIR__ . '/../lahza/Logger.php';
$lahza_logger = new LahzaLogger();

/**
 * Validate 3D Secure callback request
 */
function validate3DSCallback($gatewayParams) {
    global $lahza_logger;
    
    // Log the callback attempt
    $lahza_logger->info('3D Secure callback received', [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
    ], '3ds');
    
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $lahza_logger->warning('Invalid request method for 3DS callback', [
            'method' => $_SERVER['REQUEST_METHOD']
        ], '3ds');
        return false;
    }
    
    // Get raw POST data
    $rawData = file_get_contents('php://input');
    if (empty($rawData)) {
        $lahza_logger->warning('Empty POST data in 3DS callback', [], '3ds');
        return false;
    }
    
    // Parse JSON data
    $data = json_decode($rawData, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $lahza_logger->error('Invalid JSON in 3DS callback', [
            'json_error' => json_last_error_msg(),
            'raw_data' => substr($rawData, 0, 200)
        ], '3ds');
        return false;
    }
    
    // Validate required fields
    $requiredFields = ['transaction_id', 'authentication_status', 'signature'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field])) {
            $lahza_logger->error('Missing required field in 3DS callback', [
                'missing_field' => $field,
                'received_fields' => array_keys($data)
            ], '3ds');
            return false;
        }
    }
    
    // Verify signature
    $signature = $data['signature'];
    unset($data['signature']);
    
    $expectedSignature = hash_hmac('sha256', json_encode($data), $gatewayParams['webhookSecret']);
    
    if (!hash_equals($expectedSignature, $signature)) {
        $lahza_logger->error('Invalid signature in 3DS callback', [
            'transaction_id' => $data['transaction_id']
        ], '3ds');
        return false;
    }
    
    return $data;
}

/**
 * Process 3D Secure authentication result
 */
function process3DSResult($data, $gatewayParams) {
    global $lahza_logger;
    
    $transactionId = $data['transaction_id'];
    $authStatus = $data['authentication_status'];
    
    $lahza_logger->info('Processing 3DS authentication result', [
        'transaction_id' => $transactionId,
        'auth_status' => $authStatus,
        'eci' => $data['eci'] ?? 'not_provided',
        'cavv' => isset($data['cavv']) ? 'present' : 'absent'
    ], '3ds');
    
    // Extract invoice ID from transaction ID
    $invoiceId = null;
    if (preg_match('/^(\d+)_/', $transactionId, $matches)) {
        $invoiceId = $matches[1];
    }
    
    if (!$invoiceId) {
        $lahza_logger->error('Could not extract invoice ID from transaction ID', [
            'transaction_id' => $transactionId
        ], '3ds');
        return false;
    }
    
    // Check if invoice exists
    $invoiceId = checkCbInvoiceID($invoiceId, $gatewayParams['name']);
    if (!$invoiceId) {
        $lahza_logger->error('Invalid invoice ID in 3DS callback', [
            'transaction_id' => $transactionId,
            'invoice_id' => $invoiceId
        ], '3ds');
        return false;
    }
    
    // Process based on authentication status
    switch ($authStatus) {
        case 'Y': // Authentication successful
            $lahza_logger->info('3DS authentication successful', [
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId
            ], '3ds');
            
            // Store 3DS data for main payment processing
            store3DSData($invoiceId, [
                'status' => 'authenticated',
                'eci' => $data['eci'],
                'cavv' => $data['cavv'],
                'xid' => $data['xid'] ?? '',
                'transaction_id' => $transactionId
            ]);
            
            // Continue with payment processing
            return continuePaymentProcessing($invoiceId, $data, $gatewayParams);
            
        case 'A': // Authentication attempted
            $lahza_logger->info('3DS authentication attempted', [
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId
            ], '3ds');
            
            // Store 3DS data and continue
            store3DSData($invoiceId, [
                'status' => 'attempted',
                'eci' => $data['eci'],
                'cavv' => $data['cavv'] ?? '',
                'xid' => $data['xid'] ?? '',
                'transaction_id' => $transactionId
            ]);
            
            return continuePaymentProcessing($invoiceId, $data, $gatewayParams);
            
        case 'N': // Authentication failed
            $lahza_logger->warning('3DS authentication failed', [
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'error_code' => $data['error_code'] ?? 'unknown'
            ], '3ds');
            
            // Log transaction as failed
            logTransaction($gatewayParams['name'], [
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'error' => '3D Secure authentication failed'
            ], 'Failed');
            
            return false;
            
        case 'U': // Authentication unavailable
            $lahza_logger->info('3DS authentication unavailable, proceeding without 3DS', [
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId
            ], '3ds');
            
            // Continue without 3DS
            return continuePaymentProcessing($invoiceId, $data, $gatewayParams);
            
        default:
            $lahza_logger->error('Unknown 3DS authentication status', [
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'auth_status' => $authStatus
            ], '3ds');
            
            return false;
    }
}

/**
 * Store 3D Secure data for later use
 */
function store3DSData($invoiceId, $threeDSData) {
    global $lahza_logger;
    
    $cacheFile = __DIR__ . '/../logs/3ds_' . $invoiceId . '.json';
    
    $data = [
        'timestamp' => time(),
        'invoice_id' => $invoiceId,
        'three_ds_data' => $threeDSData
    ];
    
    if (file_put_contents($cacheFile, json_encode($data), LOCK_EX) === false) {
        $lahza_logger->error('Failed to store 3DS data', [
            'invoice_id' => $invoiceId,
            'cache_file' => $cacheFile
        ], '3ds');
        return false;
    }
    
    $lahza_logger->info('3DS data stored successfully', [
        'invoice_id' => $invoiceId,
        'status' => $threeDSData['status']
    ], '3ds');
    
    return true;
}

/**
 * Continue payment processing after 3DS
 */
function continuePaymentProcessing($invoiceId, $threeDSData, $gatewayParams) {
    global $lahza_logger;
    
    $lahza_logger->info('Continuing payment processing after 3DS', [
        'invoice_id' => $invoiceId,
        'transaction_id' => $threeDSData['transaction_id']
    ], '3ds');
    
    // This would typically trigger the main payment processing
    // For now, we'll just log the success and return true
    // The actual payment completion will be handled by the main callback
    
    logTransaction($gatewayParams['name'], [
        'invoice_id' => $invoiceId,
        'transaction_id' => $threeDSData['transaction_id'],
        'status' => '3DS authentication completed, awaiting payment confirmation'
    ], 'Success');
    
    return true;
}

// Main execution
try {
    // Validate the callback
    $callbackData = validate3DSCallback($gatewayParams);
    
    if (!$callbackData) {
        http_response_code(400);
        die('Invalid callback data');
    }
    
    // Process the 3DS result
    $result = process3DSResult($callbackData, $gatewayParams);
    
    if ($result) {
        $lahza_logger->info('3DS callback processed successfully', [
            'transaction_id' => $callbackData['transaction_id']
        ], '3ds');
        
        http_response_code(200);
        echo 'OK';
    } else {
        $lahza_logger->error('Failed to process 3DS callback', [
            'transaction_id' => $callbackData['transaction_id']
        ], '3ds');
        
        http_response_code(400);
        echo 'Processing failed';
    }
    
} catch (Exception $e) {
    $lahza_logger->error('Exception in 3DS callback processing', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], '3ds');
    
    http_response_code(500);
    echo 'Internal server error';
}
