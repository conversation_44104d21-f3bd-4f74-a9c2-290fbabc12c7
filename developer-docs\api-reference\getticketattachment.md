+++
title = "GetTicketAttachment"
toc = true
+++

Retrieve a single attachment.

Retrieves a single attachment from a ticket, reply or note with filename and base64 encoded file contents.

### Request Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| action | string | "GetTicketAttachment" | Required |
| relatedid | int | The unique id for the `type` | Required |
| type | string | One of `ticket`, `reply`, `note` | Required |
| index | int | The numerical index of the attachment to get | Required |

### Response Parameters

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| result | string | The result of the operation: success or error |
| filename | string |  |
| data | string | The base64 encoded file data |


### Example Request (CURL)

```
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.example.com/includes/api.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS,
    http_build_query(
        array(
            'action' => 'GetTicketAttachment',
            // See https://developers.whmcs.com/api/authentication
            'username' => 'IDENTIFIER_OR_ADMIN_USERNAME',
            'password' => 'SECRET_OR_HASHED_PASSWORD',
            'relatedid' => '1',
            'type' => 'ticket',
            'index' => '0',
            'responsetype' => 'json',
        )
    )
);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
$response = curl_exec($ch);
curl_close($ch);
```


### Example Request (Local API)

```
$command = 'GetTicketAttachment';
$postData = array(
    'relatedid' => '1',
    'type' => 'ticket',
    'index' => '0',
);
$adminUsername = 'ADMIN_USERNAME'; // Optional for WHMCS 7.2 and later

$results = localAPI($command, $postData, $adminUsername);
print_r($results);
```


### Example Response JSON

```
{
    "result": "success",
    "filename": "whmcs_logo.png",
    "data": "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"
}
```


### Error Responses

Possible error condition responses include:

* Related ID Required
* Invalid Type. Must be one of ticket, reply, note
* Related ID Not Found
* Attachment Index Required
* No Attachments Found
* Attachments Deleted
* Invalid Attachment Index


### Version History

| Version | Changelog |
| ------- | --------- |
| 7.10.0 | Initial Version |
