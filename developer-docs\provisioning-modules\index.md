+++
chapter = true
icon = "<i class='fa fa-cog fa-fw'></i>"
next = "/provisioning-modules/getting-started"
title = "Provisioning Modules"
weight = 0

+++

## Introduction

Provisioning Modules, enable provisioning and management of services in WHMCS.

Provisioning Modules are also referred to as Product or Server Modules.

The core function of a module is creating, suspending, unsuspending, and terminating of products.
This happens as various events occur.
These events include:

* New order payment
* Items becoming overdue
* Overdue invoice payment
* Cancellation requests.

A WHMCS module can do much more than just that including:

* Automated password resets.
* Upgrades/downgrades.
* Renewals.
* Admin based links.
* Client area output.
* Metric data for Usage Billing
* And more via custom functions.
