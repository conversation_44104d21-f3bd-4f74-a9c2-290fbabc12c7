/**
 * WIDDX Accessibility Enhancements
 * WCAG 2.1 AA Compliance Features
 * 
 * @package    WIDDX Theme
 * <AUTHOR> Development Team
 * @version    1.0.0
 */

/* Skip Links */
.widdx-skip-links {
    position: absolute;
    top: -40px;
    left: 6px;
    z-index: 9999;
}

.widdx-skip-link {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: var(--widdx-primary);
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.widdx-skip-link:focus {
    position: static;
    width: auto;
    height: auto;
    left: auto;
    top: auto;
    overflow: visible;
    clip: auto;
    outline: 2px solid #fff;
    outline-offset: 2px;
}

/* Focus Indicators */
.widdx-theme *:focus {
    outline: 2px solid var(--widdx-primary);
    outline-offset: 2px;
}

.widdx-theme .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.5);
    outline: none;
}

.widdx-theme .form-control:focus {
    border-color: var(--widdx-primary);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
    outline: none;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .widdx-theme {
        --widdx-primary: #000080;
        --widdx-secondary: #000000;
        --widdx-accent: #0000ff;
        --widdx-success: #008000;
        --widdx-warning: #ff8c00;
        --widdx-danger: #dc143c;
        --widdx-info: #000080;
    }
    
    .widdx-theme .btn {
        border: 2px solid currentColor;
    }
    
    .widdx-theme .card {
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .widdx-theme *,
    .widdx-theme *::before,
    .widdx-theme *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Screen Reader Only Content */
.widdx-sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.widdx-sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
}

/* Improved Color Contrast */
.widdx-theme .text-muted {
    color: #6c757d !important; /* Ensures 4.5:1 contrast ratio */
}

.widdx-theme .btn-outline-primary {
    color: var(--widdx-primary);
    border-color: var(--widdx-primary);
}

.widdx-theme .btn-outline-primary:hover {
    background-color: var(--widdx-primary);
    border-color: var(--widdx-primary);
    color: white;
}

/* Form Labels and Descriptions */
.widdx-form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.widdx-form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.widdx-form-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.widdx-form-error {
    color: var(--widdx-danger);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.widdx-form-control[aria-invalid="true"] {
    border-color: var(--widdx-danger);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Keyboard Navigation Enhancements */
.widdx-theme .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

.widdx-theme .dropdown-item:focus {
    background-color: var(--widdx-primary);
    color: white;
    outline: none;
}

/* Table Accessibility */
.widdx-table th {
    text-align: left;
    font-weight: 600;
}

.widdx-table caption {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #6c757d;
    text-align: left;
    caption-side: top;
    font-weight: 600;
}

/* Modal Accessibility */
.modal[aria-hidden="true"] {
    display: none;
}

.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Loading States Accessibility */
.widdx-loading[aria-busy="true"] {
    position: relative;
}

.widdx-loading[aria-busy="true"]::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Error States */
.widdx-error-state {
    border: 2px solid var(--widdx-danger);
    background-color: #fff5f5;
    padding: 1rem;
    border-radius: var(--widdx-border-radius);
}

.widdx-error-icon {
    color: var(--widdx-danger);
    margin-right: 0.5rem;
}

/* Success States */
.widdx-success-state {
    border: 2px solid var(--widdx-success);
    background-color: #f0fff4;
    padding: 1rem;
    border-radius: var(--widdx-border-radius);
}

.widdx-success-icon {
    color: var(--widdx-success);
    margin-right: 0.5rem;
}

/* Landmark Roles Styling */
main[role="main"] {
    min-height: 400px;
}

nav[role="navigation"] {
    position: relative;
}

/* Print Styles for Accessibility */
@media print {
    .widdx-theme {
        color: black !important;
        background: white !important;
    }
    
    .widdx-theme .btn {
        border: 1px solid black !important;
        background: white !important;
        color: black !important;
    }
    
    .widdx-theme a {
        color: black !important;
        text-decoration: underline !important;
    }
    
    .widdx-theme .card {
        border: 1px solid black !important;
        box-shadow: none !important;
    }
    
    /* Hide non-essential elements when printing */
    .navbar,
    .footer,
    .btn,
    .widdx-skip-links {
        display: none !important;
    }
}

/* Touch Target Sizes */
@media (pointer: coarse) {
    .widdx-theme .btn,
    .widdx-theme .nav-link,
    .widdx-theme .dropdown-item {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
    }
}

/* Language Direction Support */
[dir="rtl"] .widdx-theme {
    text-align: right;
}

[dir="rtl"] .widdx-theme .dropdown-menu {
    left: auto;
    right: 0;
}

[dir="rtl"] .widdx-theme .mr-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

[dir="rtl"] .widdx-theme .ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

/* Focus Management for Dynamic Content */
.widdx-focus-trap {
    position: relative;
}

.widdx-focus-trap:focus {
    outline: none;
}

/* Announcement Region for Screen Readers */
.widdx-announcements {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

.widdx-announcements[aria-live="polite"],
.widdx-announcements[aria-live="assertive"] {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
