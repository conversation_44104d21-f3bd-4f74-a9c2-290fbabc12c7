<?php
require_once __DIR__ . '/Logger.php';
require_once __DIR__ . '/Cache.php';
require_once __DIR__ . '/Security.php';
require_once __DIR__ . '/../config/database_config.php';

class DatabaseHandler {
    private $connection;
    private $logger;
    private $cache;
    private $security;
    private $retryCount = 0;
    private $maxRetries = DB_MAX_RETRIES;

    public function __construct() {
        $this->logger = new Logger();
        $this->cache = new Cache();
        $this->security = new Security();
        $this->connect();
    }

    private function connect() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => DB_CONNECTION_TIMEOUT
            ];

            if (DB_SSL_ENABLED) {
                $options[PDO::MYSQL_ATTR_SSL_CA] = DB_SSL_CA;
                $options[PDO::MYSQL_ATTR_SSL_CERT] = DB_SSL_CERT;
                $options[PDO::MYSQL_ATTR_SSL_KEY] = DB_SSL_KEY;
                $options[PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT] = DB_SSL_VERIFY;
            }

            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            $this->logger->log('INFO', 'Database connection established', [
                'host' => DB_HOST,
                'port' => DB_PORT,
                'database' => DB_NAME
            ]);

        } catch (PDOException $e) {
            $this->handleError($e);
            throw new Exception(DB_ERROR_MESSAGES['connection']);
        }
    }

    public function query($sql, $params = []) {
        try {
            $this->security->validateSQL($sql);
            $stmt = $this->connection->prepare($sql);
            $this->bindParameters($stmt, $params);
            $stmt->execute();
            
            $this->logger->log('INFO', 'Query executed successfully', [
                'sql' => $sql,
                'params' => $params,
                'rows' => $stmt->rowCount()
            ]);
            
            return $stmt;
            
        } catch (Exception $e) {
            $this->handleError($e);
            throw new Exception(DB_ERROR_MESSAGES['query']);
        }
    }

    public function beginTransaction() {
        try {
            $this->connection->beginTransaction();
            $this->logger->log('INFO', 'Transaction started');
            
        } catch (Exception $e) {
            $this->handleError($e);
            throw new Exception(DB_ERROR_MESSAGES['transaction']);
        }
    }

    public function commit() {
        try {
            $this->connection->commit();
            $this->logger->log('INFO', 'Transaction committed');
            
        } catch (Exception $e) {
            $this->handleError($e);
            throw new Exception(DB_ERROR_MESSAGES['transaction']);
        }
    }

    public function rollback() {
        try {
            $this->connection->rollBack();
            $this->logger->log('INFO', 'Transaction rolled back');
            
        } catch (Exception $e) {
            $this->handleError($e);
            throw new Exception(DB_ERROR_MESSAGES['transaction']);
        }
    }

    public function insert($table, $data) {
        $columns = array_keys($data);
        $values = array_fill(0, count($columns), '?');
        
        $sql = "INSERT INTO " . DB_PREFIX . $table . " (" . implode(", ", $columns) . ") 
                VALUES (" . implode(", ", $values) . ")";
        
        $stmt = $this->query($sql, array_values($data));
        return $this->connection->lastInsertId();
    }

    public function update($table, $data, $where, $whereParams = []) {
        $columns = array_keys($data);
        $set = array_map(function($col) { return $col . " = ?"; }, $columns);
        
        $sql = "UPDATE " . DB_PREFIX . $table . " 
                SET " . implode(", ", $set) . " 
                WHERE " . $where;
        
        $params = array_merge(array_values($data), $whereParams);
        return $this->query($sql, $params)->rowCount();
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM " . DB_PREFIX . $table . " WHERE " . $where;
        return $this->query($sql, $params)->rowCount();
    }

    public function select($table, $columns = '*', $where = '1', $params = [], $orderBy = null, $limit = null) {
        $sql = "SELECT " . $columns . " FROM " . DB_PREFIX . $table . " 
                WHERE " . $where;
        
        if ($orderBy) {
            $sql .= " ORDER BY " . $orderBy;
        }
        
        if ($limit) {
            $sql .= " LIMIT " . $limit;
        }
        
        return $this->query($sql, $params)->fetchAll();
    }

    public function getQuestionsCount() {
        return $this->cache->get('questions_count') ?? $this->select('questions', 'COUNT(*) as count')[0]['count'];
    }

    public function getAverageResponseTime() {
        return $this->cache->get('avg_response_time') ?? $this->select('questions', 'AVG(response_time) as avg')[0]['avg'];
    }

    public function getErrorRate() {
        $total = $this->getQuestionsCount();
        $errors = $this->select('questions', 'COUNT(*) as count', 'error IS NOT NULL')[0]['count'];
        return ($total > 0) ? ($errors / $total * 100) : 0;
    }

    public function getRecentActivity($limit = 10) {
        return $this->select('questions', '*', '1', [], 'created_at DESC', $limit);
    }

    public function getRecentErrors($limit = 10) {
        return $this->select('questions', '*', 'error IS NOT NULL', [], 'created_at DESC', $limit);
    }

    public function getActiveUsers($hours = 24) {
        $cutoff = date('Y-m-d H:i:s', time() - ($hours * 3600));
        return $this->select('questions', 'COUNT(DISTINCT user_id) as count', 'created_at >= ?', [$cutoff])[0]['count'];
    }

    private function bindParameters($stmt, $params) {
        foreach ($params as $key => $value) {
            $stmt->bindValue($key + 1, $value);
        }
    }

    private function handleError($e) {
        $this->logger->log('ERROR', 'Database error', [
            'error' => $e->getMessage(),
            'code' => $e->getCode(),
            'trace' => $e->getTraceAsString()
        ]);
        
        if ($this->retryCount < $this->maxRetries) {
            $this->retryCount++;
            sleep(DB_ERROR_RETRY_DELAY * $this->retryCount);
            $this->connect();
        } else {
            throw $e;
        }
    }

    private function validateSQL($sql) {
        // Implement SQL validation logic
        if (preg_match('/\b(SELECT|UPDATE|DELETE|INSERT)\s+.*\bFROM\s+.*\bWHERE\s+1=1\b/i', $sql)) {
            throw new Exception('SQL injection attempt detected');
        }
    }

    public function __destruct() {
        if ($this->connection) {
            $this->connection = null;
            $this->logger->log('INFO', 'Database connection closed');
        }
    }
}
