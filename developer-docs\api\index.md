+++
chapter = true
icon = "<i class='fa fa-code fa-fw'></i>"
next = "/api/getting-started"
title = "API"
weight = 0

+++

## Introduction

The WHMCS API allows you to perform operations and actions within WHMCS from external third party and custom code.

* **[Getting Started](/api/getting-started/)**<br>Learn about how to get started with the API
* **[Authentication](/api/authentication/)**<br>Learn how authentication works for the WHMCS API
* **[Access Control](/api/access-control/)**<br>Learn how IP and Access Key control works for the WHMCS API
* **[Response Types](/api/response-types/)**<br>Learn about the different response types
* **[Sample Code](/api/sample-code/)**<br>See sample code for remotely connecting to the WHMCS API
* **[Internal API](/api/internal-api/)**<br>See sample code for using the Local WHMCS API
* **[API Index](/api/api-index/)**<br>A complete listing of all available API commands
* **[API Reference](/api-reference/)**<br>Visit the API Reference Manual
