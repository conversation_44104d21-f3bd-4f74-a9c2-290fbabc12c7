#!/bin/bash
#
# WIDDX Final Smoke Test Script
# Comprehensive smoke tests across theme, gateway, and order forms
#
# @package    WIDDX Production Finalization
# <AUTHOR> Development Team
# @version    1.0.0
#

set -euo pipefail

# Configuration
WHMCS_PATH="/var/www/html/whmcs"
LOG_PATH="/var/log/widdx-deployment"
DOMAIN="yourdomain.com"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
CRITICAL_FAILURES=()
SMOKE_ISSUES=()

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_PATH}/smoke-test.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "${LOG_PATH}/smoke-test.log"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "${LOG_PATH}/smoke-test.log"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "${LOG_PATH}/smoke-test.log"
}

# Test execution function
run_smoke_test() {
    local test_name="$1"
    local test_command="$2"
    local is_critical="${3:-true}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Testing ${test_name}... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log "✅ ${test_name}: PASSED"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        if [[ "$is_critical" == "true" ]]; then
            CRITICAL_FAILURES+=("${test_name}")
            error "❌ ${test_name}: CRITICAL FAILURE"
        else
            SMOKE_ISSUES+=("${test_name}")
            warning "⚠️ ${test_name}: WARNING"
        fi
        return 1
    fi
}

# Create log directory
mkdir -p "${LOG_PATH}"

log "🔥 Starting WIDDX Final Smoke Test"

echo ""
echo -e "${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                                                                              ║${NC}"
echo -e "${CYAN}║                           🔥 FINAL SMOKE TEST 🔥                             ║${NC}"
echo -e "${CYAN}║                                                                              ║${NC}"
echo -e "${CYAN}║  Comprehensive end-to-end testing before go-live authorization              ║${NC}"
echo -e "${CYAN}║                                                                              ║${NC}"
echo -e "${CYAN}║  Testing Components:                                                         ║${NC}"
echo -e "${CYAN}║  • WIDDX Theme & User Experience                                             ║${NC}"
echo -e "${CYAN}║  • Lahza Payment Gateway & 3D Secure                                        ║${NC}"
echo -e "${CYAN}║  • WIDDX Modern Order Forms                                                  ║${NC}"
echo -e "${CYAN}║  • System Integration & Performance                                          ║${NC}"
echo -e "${CYAN}║                                                                              ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""

# 1. Core System Smoke Tests
log "🏗️ Phase 1: Core System Smoke Tests"

run_smoke_test "Website Accessibility" "curl -f -s -o /dev/null https://${DOMAIN}/" true
run_smoke_test "HTTPS Enforcement" "curl -s -o /dev/null -w '%{redirect_url}' http://${DOMAIN}/ | grep -q https" true
run_smoke_test "SSL Certificate Valid" "echo | openssl s_client -servername ${DOMAIN} -connect ${DOMAIN}:443 2>/dev/null | openssl x509 -checkend 3600 -noout" true
run_smoke_test "Admin Area Access" "curl -f -s -o /dev/null https://${DOMAIN}/admin/" true
run_smoke_test "Client Area Access" "curl -f -s -o /dev/null https://${DOMAIN}/clientarea.php" true

# 2. WIDDX Theme Smoke Tests
log "🎨 Phase 2: WIDDX Theme Smoke Tests"

run_smoke_test "WIDDX Theme Active" "curl -s https://${DOMAIN}/ | grep -q 'WIDDX'" true
run_smoke_test "Theme CSS Loading" "curl -f -s -o /dev/null https://${DOMAIN}/templates/WIDDX/css/theme.css" true
run_smoke_test "Theme JavaScript Loading" "curl -f -s -o /dev/null https://${DOMAIN}/templates/WIDDX/js/widdx-modern.js" true
run_smoke_test "Responsive Design Elements" "curl -s https://${DOMAIN}/ | grep -q 'viewport'" true
run_smoke_test "Accessibility Features" "curl -s https://${DOMAIN}/ | grep -q 'skip-link\|aria-label'" false

# Test theme rendering on different pages
run_smoke_test "Homepage Theme Rendering" "curl -s https://${DOMAIN}/ | grep -q 'widdx\|WIDDX'" true
run_smoke_test "Client Area Theme Rendering" "curl -s https://${DOMAIN}/clientarea.php | grep -q 'widdx\|WIDDX'" true

# 3. Lahza Payment Gateway Smoke Tests
log "💳 Phase 3: Lahza Payment Gateway Smoke Tests"

run_smoke_test "Gateway Files Present" "test -f ${WHMCS_PATH}/modules/gateways/lahza.php" true
run_smoke_test "Callback Files Present" "test -f ${WHMCS_PATH}/modules/gateways/callback/lahza.php" true
run_smoke_test "3DS Callback Present" "test -f ${WHMCS_PATH}/modules/gateways/callback/lahza_3ds.php" true
run_smoke_test "Gateway Logs Directory" "test -d ${WHMCS_PATH}/modules/gateways/logs" true

# Test gateway configuration
DB_HOST=$(grep -oP "(?<=\\\$db_host = ')[^']*" "${WHMCS_PATH}/configuration.php" || echo "localhost")
DB_NAME=$(grep -oP "(?<=\\\$db_name = ')[^']*" "${WHMCS_PATH}/configuration.php")
DB_USER=$(grep -oP "(?<=\\\$db_username = ')[^']*" "${WHMCS_PATH}/configuration.php")
DB_PASS=$(grep -oP "(?<=\\\$db_password = ')[^']*" "${WHMCS_PATH}/configuration.php")

run_smoke_test "Gateway Database Config" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT COUNT(*) FROM tblpaymentgateways WHERE gateway=\"lahza\";' | tail -n 1 | grep -q '[1-9]'" true

# Test production mode
run_smoke_test "Production Mode Enabled" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT value FROM tblpaymentgateways WHERE gateway=\"lahza\" AND setting=\"testMode\";' | tail -n 1 | grep -q 'off'" true

# Test 3D Secure enabled
run_smoke_test "3D Secure Enabled" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT value FROM tblpaymentgateways WHERE gateway=\"lahza\" AND setting=\"enable3DS\";' | tail -n 1 | grep -q 'on'" true

# 4. Order Form Smoke Tests
log "🛒 Phase 4: Order Form Smoke Tests"

run_smoke_test "WIDDX Modern Order Form" "test -f ${WHMCS_PATH}/templates/orderforms/widdx_modern/theme.yaml" true
run_smoke_test "Order Form CSS" "test -f ${WHMCS_PATH}/templates/orderforms/widdx_modern/css/order-form.css" true
run_smoke_test "Order Form JavaScript" "test -f ${WHMCS_PATH}/templates/orderforms/widdx_modern/js/order-form.js" true
run_smoke_test "Order Form Access" "curl -f -s -o /dev/null https://${DOMAIN}/cart.php" true
run_smoke_test "Order Form Rendering" "curl -s https://${DOMAIN}/cart.php | grep -q 'widdx'" true

# Test order form configuration
run_smoke_test "Order Form Template Active" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT value FROM tblconfiguration WHERE setting=\"OrderFormTemplate\";' | tail -n 1 | grep -q 'widdx_modern'" true

# 5. Performance Smoke Tests
log "⚡ Phase 5: Performance Smoke Tests"

# Test page load times
HOMEPAGE_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://${DOMAIN}/)
run_smoke_test "Homepage Load Time (<5s)" "echo '${HOMEPAGE_TIME} < 5' | bc -l | grep -q 1" false

CLIENTAREA_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://${DOMAIN}/clientarea.php)
run_smoke_test "Client Area Load Time (<6s)" "echo '${CLIENTAREA_TIME} < 6' | bc -l | grep -q 1" false

CART_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://${DOMAIN}/cart.php)
run_smoke_test "Cart Load Time (<5s)" "echo '${CART_TIME} < 5' | bc -l | grep -q 1" false

# Test database performance
DB_TIME=$(mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e "USE ${DB_NAME}; SET @start_time = NOW(6); SELECT COUNT(*) FROM tblinvoices; SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, NOW(6)) / 1000000;" | tail -n 1)
run_smoke_test "Database Performance (<2s)" "echo '${DB_TIME} < 2' | bc -l | grep -q 1" false

# 6. Security Smoke Tests
log "🔒 Phase 6: Security Smoke Tests"

run_smoke_test "Security Headers Present" "curl -s -I https://${DOMAIN}/ | grep -q 'X-Content-Type-Options\|X-Frame-Options\|X-XSS-Protection'" false
run_smoke_test "HSTS Header" "curl -s -I https://${DOMAIN}/ | grep -q 'Strict-Transport-Security'" false
run_smoke_test "Content Security Policy" "curl -s -I https://${DOMAIN}/ | grep -q 'Content-Security-Policy'" false

# Test file permissions
run_smoke_test "Secure File Permissions" "find ${WHMCS_PATH} -type f -perm /o+w | wc -l | grep -q '^0$'" true
run_smoke_test "Gateway File Permissions" "stat -c '%a' ${WHMCS_PATH}/modules/gateways/lahza.php | grep -q '644'" true

# 7. Integration Smoke Tests
log "🔗 Phase 7: Integration Smoke Tests"

# Test WHMCS core functionality
run_smoke_test "WHMCS Database Connection" "mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e 'USE ${DB_NAME}; SELECT 1;'" true
run_smoke_test "WHMCS Configuration Valid" "php -l ${WHMCS_PATH}/configuration.php" true

# Test gateway integration
run_smoke_test "Gateway PHP Syntax" "php -l ${WHMCS_PATH}/modules/gateways/lahza.php" true
run_smoke_test "Callback PHP Syntax" "php -l ${WHMCS_PATH}/modules/gateways/callback/lahza.php" true

# 8. User Experience Smoke Tests
log "👥 Phase 8: User Experience Smoke Tests"

# Test navigation elements
run_smoke_test "Navigation Menu Present" "curl -s https://${DOMAIN}/ | grep -q 'nav\|menu'" true
run_smoke_test "Footer Present" "curl -s https://${DOMAIN}/ | grep -q 'footer'" true
run_smoke_test "Logo/Branding Present" "curl -s https://${DOMAIN}/ | grep -q 'logo\|brand'" false

# Test responsive design
run_smoke_test "Mobile Viewport Meta" "curl -s https://${DOMAIN}/ | grep -q 'viewport'" true
run_smoke_test "Responsive CSS Classes" "curl -s https://${DOMAIN}/templates/WIDDX/css/theme.css | grep -q '@media'" false

# 9. Monitoring Integration Smoke Tests
log "📊 Phase 9: Monitoring Integration Smoke Tests"

run_smoke_test "Monitoring Scripts Present" "test -d /var/monitoring/widdx" true
run_smoke_test "System Health Monitor" "test -x /var/monitoring/widdx/system-health.sh" true
run_smoke_test "Payment Monitor" "test -x /var/monitoring/widdx/payment-monitor.sh" true
run_smoke_test "Monitoring Cron Jobs" "crontab -l | grep -q monitoring" true

# Test monitoring functionality
run_smoke_test "Health Check Execution" "/var/monitoring/widdx/system-health.sh" false
run_smoke_test "Monitoring Logs Generated" "test -f /var/monitoring/widdx/health.log" false

# 10. End-to-End Workflow Tests
log "🔄 Phase 10: End-to-End Workflow Tests"

# Test complete user journey simulation
run_smoke_test "Homepage to Cart Navigation" "curl -s https://${DOMAIN}/ | grep -q 'cart\|order\|buy'" false
run_smoke_test "Cart to Checkout Flow" "curl -s https://${DOMAIN}/cart.php | grep -q 'checkout\|payment'" false

# Test admin workflow
run_smoke_test "Admin Login Page" "curl -s https://${DOMAIN}/admin/ | grep -q 'login\|username\|password'" true

# 11. API and Webhook Smoke Tests
log "🌐 Phase 11: API and Webhook Smoke Tests"

# Test webhook endpoints
run_smoke_test "Payment Webhook Endpoint" "curl -f -s -o /dev/null -X POST https://${DOMAIN}/modules/gateways/callback/lahza.php" true
run_smoke_test "3DS Webhook Endpoint" "curl -f -s -o /dev/null -X POST https://${DOMAIN}/modules/gateways/callback/lahza_3ds.php" true

# Test API connectivity (if configured)
PUBLIC_KEY=$(mysql --host=${DB_HOST} --user=${DB_USER} --password=${DB_PASS} -e "USE ${DB_NAME}; SELECT value FROM tblpaymentgateways WHERE gateway='lahza' AND setting='publicKey';" | tail -n 1)
if [[ "$PUBLIC_KEY" =~ ^pk_live_ ]]; then
    run_smoke_test "Lahza API Connectivity" "curl -f -s -H 'Authorization: Bearer ${PUBLIC_KEY}' https://api.lahza.io/v1/health" false
fi

# 12. Generate Smoke Test Report
log "📊 Generating final smoke test report..."

cat > "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt" << EOF
WIDDX Final Smoke Test Report
============================
Test ID: ${TIMESTAMP}
Completed: $(date)
Domain: ${DOMAIN}

SMOKE TEST SUMMARY:
Total Tests: ${TOTAL_TESTS}
Passed: ${PASSED_TESTS}
Failed: ${FAILED_TESTS}
Success Rate: $(echo "scale=2; ${PASSED_TESTS} * 100 / ${TOTAL_TESTS}" | bc)%

PERFORMANCE METRICS:
Homepage Load Time: ${HOMEPAGE_TIME}s
Client Area Load Time: ${CLIENTAREA_TIME}s
Cart Load Time: ${CART_TIME}s
Database Query Time: ${DB_TIME}s

COMPONENT STATUS:
✅ WIDDX Theme: $(curl -s https://${DOMAIN}/ | grep -q 'WIDDX' && echo "ACTIVE" || echo "INACTIVE")
✅ Lahza Gateway: $(test -f ${WHMCS_PATH}/modules/gateways/lahza.php && echo "DEPLOYED" || echo "MISSING")
✅ Order Form: $(test -f ${WHMCS_PATH}/templates/orderforms/widdx_modern/theme.yaml && echo "ACTIVE" || echo "INACTIVE")
✅ Monitoring: $(test -d /var/monitoring/widdx && echo "ACTIVE" || echo "INACTIVE")

CRITICAL FAILURES:
EOF

if [[ ${#CRITICAL_FAILURES[@]} -eq 0 ]]; then
    echo "None - All critical tests passed ✅" >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt"
else
    for failure in "${CRITICAL_FAILURES[@]}"; do
        echo "- ${failure}" >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt"
    done
fi

cat >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt" << EOF

WARNINGS:
EOF

if [[ ${#SMOKE_ISSUES[@]} -eq 0 ]]; then
    echo "None - All non-critical tests passed ✅" >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt"
else
    for issue in "${SMOKE_ISSUES[@]}"; do
        echo "- ${issue}" >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt"
    done
fi

cat >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt" << EOF

GO-LIVE READINESS:
EOF

if [[ ${#CRITICAL_FAILURES[@]} -eq 0 ]]; then
    cat >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt" << EOF
✅ READY FOR GO-LIVE
All critical smoke tests passed. System is ready for live traffic.

FINAL CHECKLIST:
✅ WIDDX Theme: Deployed and rendering correctly
✅ Lahza Gateway: Configured with production credentials
✅ Order Forms: WIDDX Modern active and functional
✅ Performance: Load times within acceptable ranges
✅ Security: SSL and security headers configured
✅ Monitoring: All monitoring systems active
✅ Integration: All components working together

AUTHORIZATION FOR LIVE TRAFFIC:
System has passed all critical smoke tests and is ready to serve real customers.

POST-LAUNCH MONITORING:
1. Monitor payment processing success rates
2. Track website performance metrics
3. Watch for security alerts and failed logins
4. Review user experience feedback
5. Schedule 24-hour post-launch review

CONGRATULATIONS! 🎉
Your WIDDX-powered WHMCS system is ready for production traffic!
EOF
else
    cat >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt" << EOF
❌ NOT READY FOR GO-LIVE
Critical failures detected that must be resolved before launch.

CRITICAL ISSUES TO RESOLVE:
EOF
    for failure in "${CRITICAL_FAILURES[@]}"; do
        echo "- ${failure}" >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt"
    done
    
    cat >> "${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt" << EOF

REQUIRED ACTIONS:
1. Address all critical failures listed above
2. Re-run smoke tests to verify fixes
3. Consider rollback if issues cannot be resolved quickly
4. Do not authorize go-live until all critical tests pass
EOF
fi

# Final Summary
echo ""
echo "=" . str_repeat("=", 80)
log "🔥 FINAL SMOKE TEST COMPLETED"
echo "=" . str_repeat("=", 80)

echo ""
echo "📊 SMOKE TEST SUMMARY:"
echo "   Total Tests: ${TOTAL_TESTS}"
echo "   Passed: ${PASSED_TESTS}"
echo "   Failed: ${FAILED_TESTS}"
echo "   Success Rate: $(echo "scale=2; ${PASSED_TESTS} * 100 / ${TOTAL_TESTS}" | bc)%"

echo ""
echo "⚡ PERFORMANCE METRICS:"
echo "   Homepage: ${HOMEPAGE_TIME}s"
echo "   Client Area: ${CLIENTAREA_TIME}s"
echo "   Cart: ${CART_TIME}s"
echo "   Database: ${DB_TIME}s"

if [[ ${#CRITICAL_FAILURES[@]} -eq 0 ]]; then
    echo ""
    echo -e "${GREEN}✅ GO-LIVE STATUS: AUTHORIZED FOR LIVE TRAFFIC${NC}"
    echo ""
    echo "🎉 All critical smoke tests passed!"
    echo "🌐 WIDDX system is production-ready"
    echo "💳 Payment processing validated"
    echo "🔒 Security measures verified"
    echo "📊 Monitoring systems active"
    echo ""
    echo -e "${CYAN}🚀 READY TO OPEN THE FLOODGATES! 🚀${NC}"
    echo ""
    echo "📋 Post-Launch Actions:"
    echo "   1. Begin accepting live traffic"
    echo "   2. Monitor payment processing"
    echo "   3. Track user experience metrics"
    echo "   4. Schedule 24-hour review"
else
    echo ""
    echo -e "${RED}❌ GO-LIVE STATUS: NOT AUTHORIZED${NC}"
    echo ""
    echo "🚨 Critical failures detected:"
    for failure in "${CRITICAL_FAILURES[@]}"; do
        echo "   - ${failure}"
    done
    echo ""
    echo "📋 Required Actions:"
    echo "   1. Address all critical failures"
    echo "   2. Re-run smoke tests"
    echo "   3. Do not go live until all tests pass"
fi

if [[ ${#SMOKE_ISSUES[@]} -gt 0 ]]; then
    echo ""
    echo -e "${YELLOW}⚠️ NON-CRITICAL ISSUES:${NC}"
    for issue in "${SMOKE_ISSUES[@]}"; do
        echo "   - ${issue}"
    done
    echo "   (These can be addressed post-launch)"
fi

echo ""
echo "📄 Detailed report: ${LOG_PATH}/final_smoke_test_${TIMESTAMP}.txt"
echo "📋 Smoke test logs: ${LOG_PATH}/smoke-test.log"

info "Final smoke test completed. Check ${LOG_PATH}/smoke-test.log for detailed logs."
EOF
