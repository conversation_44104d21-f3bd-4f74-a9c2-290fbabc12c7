<?php
// WebSocket Configuration
if (!defined('SECURE_WS_CONFIG_LOADED')) {
    define('SECURE_WS_CONFIG_LOADED', true);
    
    // Prevent direct access
    if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
        die('Direct access not permitted');
    }
}

// Load environment variables
require_once __DIR__ . '/../includes/Env.php';

// WebSocket Settings
const WS_HOST = Env::get('WS_HOST', 'localhost');
const WS_PORT = Env::get('WS_PORT', 8080);
const WS_SSL = Env::get('WS_SSL', false);
const WS_SSL_CERT = Env::get('WS_SSL_CERT', '');
const WS_SSL_KEY = Env::get('WS_SSL_KEY', '');

// Connection Settings
const WS_UPDATE_INTERVAL = Env::get('WS_UPDATE_INTERVAL', 5);
const WS_MAX_CLIENTS = Env::get('WS_MAX_CLIENTS', 100);
const WS_TIMEOUT = Env::get('WS_TIMEOUT', 30);
const WS_LOG_FILE = Env::get('WS_LOG_FILE', __DIR__ . '/../logs/ws.log');

// Security Settings
const WS_ALLOWED_ORIGINS = Env::get('WS_ALLOWED_ORIGINS', ['http://localhost', 'https://localhost']);
const WS_ALLOWED_PROTOCOLS = Env::get('WS_ALLOWED_PROTOCOLS', ['widdx-analytics']);

// Message Settings
const WS_MAX_MESSAGE_SIZE = Env::get('WS_MAX_MESSAGE_SIZE', 1048576);
const WS_COMPRESSION = Env::get('WS_COMPRESSION', true);

// Debug Settings
const WS_DEBUG = Env::get('WS_DEBUG', Env::get('APP_DEBUG', false));

// Authentication
const WS_AUTH_REQUIRED = Env::get('WS_AUTH_REQUIRED', true);
const WS_AUTH_KEY = Env::get('WS_AUTH_KEY', '');

// Security and Validation
if (WS_AUTH_REQUIRED && empty(WS_AUTH_KEY)) {
    trigger_error('WebSocket authentication key is required in production', E_USER_WARNING);
}

if (WS_MAX_CLIENTS < 1) {
    trigger_error('Invalid maximum clients value', E_USER_WARNING);
}

if (WS_TIMEOUT < 5) {
    trigger_error('WebSocket timeout is too low', E_USER_WARNING);
}

// Create log directory with proper permissions
$logDir = dirname(WS_LOG_FILE);
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

chmod($logDir, 0755);

// Validate SSL configuration if enabled
if (WS_SSL && (!WS_SSL_CERT || !WS_SSL_KEY)) {
    trigger_error('SSL is enabled but SSL certificates are not configured', E_USER_WARNING);
}

// Validate message size
if (WS_MAX_MESSAGE_SIZE < 1024) {
    trigger_error('WebSocket maximum message size is too small', E_USER_WARNING);
}

// Validate origins
if (!is_array(WS_ALLOWED_ORIGINS)) {
    trigger_error('WebSocket allowed origins must be an array', E_USER_WARNING);
}

// Validate protocols
if (!is_array(WS_ALLOWED_PROTOCOLS)) {
    trigger_error('WebSocket allowed protocols must be an array', E_USER_WARNING);
}
