<?php
require_once __DIR__ . '/../includes/Monitor.php';
require_once __DIR__ . '/../includes/Database.php';

use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use Ratchet\ConnectionInterface;
use Ratchet\MessageComponentInterface;

class AnalyticsServer implements MessageComponentInterface {
    protected $clients;
    private $monitor;
    private $interval;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->monitor = new Monitor();
        $this->interval = 5; // Update interval in seconds
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "New connection! ({$conn->resourceId})\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        // Handle incoming messages
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        echo "Connection {$conn->resourceId} has disconnected\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }

    public function start() {
        // Start background process to send updates
        $this->startUpdateProcess();
        
        // Start WebSocket server
        $server = IoServer::factory(
            new HttpServer(
                new WsServer(
                    new AnalyticsServer()
                )
            ),
            8080
        );
        
        $server->run();
    }

    private function startUpdateProcess() {
        // Create background process
        $pid = pcntl_fork();
        
        if ($pid == -1) {
            die('Could not fork');
        } else if ($pid) {
            // Parent process
            return;
        }

        // Child process
        while (true) {
            $this->sendUpdates();
            sleep($this->interval);
        }
    }

    private function sendUpdates() {
        $data = $this->getAnalyticsData();
        
        foreach ($this->clients as $client) {
            $client->send(json_encode($data));
        }
    }

    private function getAnalyticsData() {
        return [
            'timestamp' => time(),
            'status' => $this->monitor->getSystemStatus(),
            'memory_usage' => memory_get_usage(true),
            'total_questions' => $this->getTotalQuestions(),
            'cached_responses' => $this->getCachedResponses(),
            'usage' => $this->getUsageData(),
            'sources' => $this->getSourceData(),
            'activity' => $this->getRecentActivity()
        ];
    }

    private function getTotalQuestions() {
        $db = new Database();
        $stmt = $db->getConnection()->query("SELECT COUNT(*) FROM questions");
        return $stmt->fetchColumn();
    }

    private function getCachedResponses() {
        $db = new Database();
        $stmt = $db->getConnection()->query("SELECT COUNT(*) FROM questions WHERE source_model = 'CACHED'");
        return $stmt->fetchColumn();
    }

    private function getUsageData() {
        $db = new Database();
        $stmt = $db->getConnection()->query("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:00') as time,
                COUNT(*) as count
            FROM questions
            WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            GROUP BY time
            ORDER BY time
        ");
        
        $data = $stmt->fetchAll();
        return [
            'labels' => array_column($data, 'time'),
            'values' => array_column($data, 'count')
        ];
    }

    private function getSourceData() {
        $db = new Database();
        $stmt = $db->getConnection()->query("
            SELECT 
                source_model,
                COUNT(*) as count
            FROM questions
            WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            GROUP BY source_model
        ");
        
        $data = $stmt->fetchAll();
        return [
            'labels' => array_column($data, 'source_model'),
            'values' => array_column($data, 'count')
        ];
    }

    private function getRecentActivity() {
        $db = new Database();
        $stmt = $db->getConnection()->query("
            SELECT 
                created_at,
                'question' as type,
                question_text as details
            FROM questions
            ORDER BY created_at DESC
            LIMIT 10
        ");
        
        return $stmt->fetchAll();
    }
}

// Start server
$server = new AnalyticsServer();
$server->start();
