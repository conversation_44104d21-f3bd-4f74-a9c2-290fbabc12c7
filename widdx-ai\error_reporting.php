<?php
// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// Set error log file
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/logs/php_errors.log');

// Test database connection
require_once __DIR__ . '/includes/Env.php';
require_once __DIR__ . '/includes/Database.php';

echo "<h2>Error Reporting Test</h2>";

try {
    // Test PDO
    if (!extension_loaded('pdo')) {
        throw new Exception('PDO extension is not loaded');
    }
    
    // Test PDO MySQL driver
    if (!in_array('mysql', PDO::getAvailableDrivers())) {
        throw new Exception('PDO MySQL driver is not available');
    }
    
    // Test database connection
    $db = Database::getInstance()->getConnection();
    echo "<p style='color:green;'>Database connection successful!</p>";
    
} catch (Exception $e) {
    echo "<div style='color:red;'>";
    echo "<h3>Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . htmlspecialchars($e->getFile()) . " (Line: " . $e->getLine() . ")</p>";
    echo "<h4>Stack Trace:</h4><pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    
    // Show PHP info
    if (function_exists('phpinfo')) {
        echo "<h3>PHP Info:</h3>";
        ob_start();
        phpinfo();
        $phpinfo = ob_get_clean();
        echo $phpinfo;
    }
    
    echo "</div>";
}
?>
