<?php
// Enable all error reporting only in development
if (Env::get('APP_ENV', 'production') === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/logs/error.log');
}

// Include the Env class
require_once __DIR__ . '/includes/Env.php';

// Test environment variables
echo "<h2>Environment Variables Test</h2>";

echo "<h3>Required Environment Variables:</h3>";
$requiredVars = [
    'APP_ENV' => ['required' => true, 'default' => 'production'],
    'APP_KEY' => ['required' => true],
    'DB_HOST' => ['required' => true, 'default' => 'localhost'],
    'DB_DATABASE' => ['required' => true],
    'DB_USERNAME' => ['required' => true],
    'DB_PASSWORD' => ['required' => true],
    'DB_PORT' => ['required' => true, 'default' => '3306'],
    'DEEPSEEK_API_KEY' => ['required' => true],
    'GEMINI_API_KEY' => ['required' => true],
    'CACHE_DRIVER' => ['required' => true, 'default' => 'file'],
    'SESSION_DRIVER' => ['required' => true, 'default' => 'file'],
    'LOG_CHANNEL' => ['required' => true, 'default' => 'stack']
];

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Variable</th><th>Value</th><th>Status</th></tr>";

foreach ($requiredVars as $var => $config) {
    $value = Env::get($var, $config['default'] ?? 'NOT SET');
    
    // Validate required variables
    if ($config['required'] && $value === 'NOT SET') {
        $status = "<span style='color:red;'>MISSING (Required)</span>";
    } else {
        $status = "<span style='color:green;'>OK</span>";
    }
    
    // Mask sensitive values
    $displayValue = $value;
    if (in_array($var, ['DB_PASSWORD', 'DEEPSEEK_API_KEY', 'GEMINI_API_KEY']) && $value !== 'NOT SET') {
        $displayValue = str_repeat('*', min(8, strlen($value)));
    }
    
    // Add validation for specific variables
    if ($var === 'DB_PORT') {
        if (!is_numeric($value)) {
            $status = "<span style='color:red;'>INVALID (Must be numeric)</span>";
        }
    }
    
    echo "<tr>";
    echo "<td>$var</td>";
    echo "<td>$displayValue</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";

// Check if .env file exists and is readable
$envFile = __DIR__ . '/.env';

// Check permissions
$permissions = substr(sprintf('%o', fileperms($envFile)), -4);

// Check file size
$fileSize = filesize($envFile);

// Check modification time
$modifiedTime = date('Y-m-d H:i:s', filemtime($envFile));
echo "<h3>.env File Status:</h3>";

if (file_exists($envFile)) {
    if (is_readable($envFile)) {
        echo "<p style='color:green;'>.env file exists and is readable.</p>";
        
        // Check file permissions
        $perms = fileperms($envFile);
        echo "<p>File permissions: " . substr(sprintf('%o', $perms), -4) . "</p>";
        
        // Display first few lines of .env file (without sensitive data)
        echo "<h4>.env File Preview (first 10 non-comment lines):</h4>";
        echo "<pre style='background:#f5f5f5;padding:10px;border:1px solid #ddd;max-height:200px;overflow:auto;'>";
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $count = 0;
        foreach ($lines as $line) {
            $line = trim($line);
            // Skip comments and empty lines
            if (empty($line) || $line[0] === '#') continue;
            
            // Mask sensitive values
            if (preg_match('/^(DB_PASSWORD|DEEPSEEK_API_KEY|GEMINI_API_KEY)=(.+)$/i', $line, $matches)) {
                $line = $matches[1] . '=********';
            }
            
            echo htmlspecialchars($line) . "\n";
            $count++;
            if ($count >= 10) break;
        }
        echo "</pre>";
        
    } else {
        echo "<p style='color:red;'>.env file exists but is not readable. Check file permissions.</p>";
    }
} else {
    echo "<p style='color:red;'>.env file not found at: " . htmlspecialchars($envFile) . "</p>";
}

echo "<h3>PHP Configuration:</h3>";
echo "<ul>";
echo "<li>PHP Version: " . phpversion() . "</li>";
echo "<li>Loaded Extensions: " . implode(', ', get_loaded_extensions()) . "</li>";
echo "<li>PDO Drivers: " . (extension_loaded('pdo') ? implode(', ', PDO::getAvailableDrivers()) : 'PDO not loaded') . "</li>";
echo "</ul>";
?>
